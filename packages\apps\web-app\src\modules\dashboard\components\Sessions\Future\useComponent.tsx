import { useMutation, useQueryClient } from '@tanstack/react-query';
import { selectAuthUserId, selectCurrentUserFirstName, useAuthStore } from '@waitroom/auth';
import { useSessionPendingAccessStats } from '@waitroom/common';
import { DefaultApiResponse, SessionApiService, sessionApiService } from '@waitroom/common-api';
import { Session, SessionStatus } from '@waitroom/models';
import { sessionCacheService } from '@waitroom/react-query';
import { useUnmount } from 'ahooks';
import { useCallback, useEffect, useState } from 'react';
import { analyticsService } from '../../../../analytics/services';
import { updateAppState, useAppStore } from '../../../../core/store/store';
import { useComponent as useSessionComponent } from '../useComponent';
import { useFutureSessionList } from '../useSessionList';
import { useOnboardingChecklist } from '../../../hooks/useOnboardingChecklist';

type DeleteProps = { id: string; recurrenceId: string };

export const useComponent = () => {
  const queryClient = useQueryClient();
  const [del, setDeleteSession] = useState<Session>();
  const userId = useAuthStore(selectAuthUserId);
  const firstName = useAuthStore(selectCurrentUserFirstName);
  const query = useFutureSessionList();
  const sessions = query.data?.pages?.[0]?.data?.data?.sessions;
  const { isCompleted } = useOnboardingChecklist();

  const deleteMutation = useMutation<
    SessionApiService.Delete['response'],
    DefaultApiResponse,
    DeleteProps
  >({
    mutationFn: ({ id, recurrenceId }: DeleteProps) =>
      sessionApiService.delete(id, recurrenceId, {
        data: { sessionState: SessionStatus.CANCELLED },
      }),
  });
  const { reset, mutate } = deleteMutation;

  const onConfirmDelete = () => {
    if (!del) return;
    mutate(
      {
        id: del.sessionID,
        recurrenceId: del.sessionRecurrenceID,
      },
      {
        onSuccess: () => {
          if (userId) {
            sessionCacheService.session.remove({
              client: queryClient,
              id: del.sessionID,
              userId,
            });
          }
          setDeleteSession(undefined);
        },
      },
    );
  };

  const onDelete = useCallback(
    (session: Session | undefined) => {
      reset();
      setDeleteSession(session);
    },
    [reset],
  );
  const onConfirmClose = useCallback(() => setDeleteSession(undefined), []);
  const sessionRules = useSessionPendingAccessStats({
    sessions,
  });
  const { onManage } = useSessionComponent();
  useEffect(() => {
    analyticsService.page('DASHBOARD', {
      state: 'upcoming',
    });
  }, []);

  const hideWelcomeToDashboard = useAppStore.use.hideWelcomeToDashboard();

  useUnmount(() => {
    updateAppState({ hideWelcomeToDashboard: true });
  });

  return {
    deleteOpen: !!del,
    onDelete,
    onManage,
    onConfirmDelete,
    onConfirmClose,
    deleteMutation,
    query,
    sessions,
    sessionRules,
    hideWelcomeToDashboard,
    firstName,
    isCompleted,
  };
};
