import { ModalType } from '@core/components/App/Modals/types';
import { setModal } from '@waitroom/common';
import { Session } from '@waitroom/models';
import { useCallback } from 'react';
import { useNotetaker } from '../../../core/hooks/useNotetaker';

export const useComponent = () => {
  const onManage = useCallback((session: Session, onClose?: () => void) => {
    setModal({
      type: ModalType.VIEWER_ACCESS,
      props: {
        session,
      },
      onClose,
    });
  }, []);
  const { hasANotetakerConnected } = useNotetaker();

  return {
    onManage,
    hasANotetakerConnected,
  };
};
