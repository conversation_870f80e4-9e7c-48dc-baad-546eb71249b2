import { ModalType } from '@core/components/App/Modals/types';
import { ModalState, setModal } from '@waitroom/common';
import { DefaultApiResponse } from '@waitroom/common-api';
import { Session } from '@waitroom/models';
import { getRequestData } from '@waitroom/react-query';
import { TFunction } from 'i18next';
import { SessionFormProps } from '../../../session/components/Form/Session/SessionForm.types';
import { meetNowInitialValues } from '../../../session/components/Form/Session/SessionForm.utils';

export const openSessionAccessModal = (session: Session | undefined) => {
  if (session?.accessStatus === 'locked') {
    setModal({
      type: ModalType.VIEWER_ACCESS,
      props: {
        session,
      },
    });
  }
};

export const meetNowModalOnSuccess = (
  response: DefaultApiResponse<{
    session: Session;
  }>,
) => {
  const data = getRequestData(response);
  openSessionAccessModal(data?.session);
};

export const meetNowModalProps = (
  firstName: string,
  t: TFunction,
): ModalState<SessionFormProps> => {
  return {
    type: ModalType.SESSION_FORM,
    props: {
      initialValues: meetNowInitialValues(firstName, t),
      onSuccess: meetNowModalOnSuccess,
    },
  };
};
