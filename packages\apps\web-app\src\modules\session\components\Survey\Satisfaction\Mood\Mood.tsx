import { Button, ButtonProps, Grid, Text, VStack } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { StageLayout } from '../StageLayout/StageLayout';
import { useMood, UseMoodProps } from './useMood';

const stylesButton = (isActive: boolean): ButtonProps => ({
  variant: isActive ? 'solid' : 'ghost',
  rounded: 'lg',
  height: 'auto',
  color: isActive ? 'white' : 'gray.300',
  p: [1, 2],
  _hover: {
    bgColor: 'gray.900',
    color: 'white',
  },
  _active: {
    bgColor: 'gray.700',
    color: 'white',
  },
});

export type MoodProps = UseMoodProps & {
  onClose: () => void;
};

export const Mood = ({ onClose, next, sessionId, recurrenceId }: MoodProps) => {
  const { t } = useTranslation();
  const { options, submitMood, mood, ns } = useMood({
    next,
    sessionId,
    recurrenceId,
  });

  return (
    <StageLayout onClose={onClose} heading={t(`${ns}heading`)} data-testid="survey-stage-0">
      <Grid templateColumns={'repeat(5, 1fr)'} mx={-2} gap={[0, 0, 1]}>
        {options.map(({ value, label, description }) => (
          <Button
            key={value}
            {...stylesButton(mood === value)}
            data-testid={`mood-${value}`}
            value={value}
            p={[1, 1, 2, 3]}
            minW={0}
            onClick={() => submitMood(value)}
          >
            <VStack as="span">
              <Text as="span" fontSize={['2xl', '3xl']}>
                {label}
              </Text>
              <Text as="span" fontSize={'2xs'}>
                {t(description)}
              </Text>
            </VStack>
          </Button>
        ))}
      </Grid>
    </StageLayout>
  );
};
