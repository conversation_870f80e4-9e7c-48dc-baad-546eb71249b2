import { ApiResponse } from '@waitroom/http-client';
import {
  PaginatedQueryParams,
  PaginatedRequestResponse,
  RequestResponse,
  XRay,
  XRayComplete,
  XRayNotification,
  XRayTemplate,
  XRayTemplateComplete,
  XRayType,
} from '@waitroom/models';
import { ApiParams } from '../types';

export namespace XRayApiService {
  export interface GetByUser {
    response: ApiResponse<PaginatedRequestResponse<{ xrays: XRay[] }>>;
    params: ApiParams<never, PaginatedQueryParams>;
  }
  export interface GetById {
    response: ApiResponse<RequestResponse<XRayComplete>>;
    params: ApiParams;
  }
  export interface GeneratePrompt {
    response: ApiResponse<
      RequestResponse<{
        type: XRayType;
        prompt: string;
      }>
    >;
    params: ApiParams<{
      description: string;
    }>;
  }
  export interface GenerateInfo {
    response: ApiResponse<RequestResponse<Pick<XRay, 'title' | 'icon' | 'shortSummary'>>>;
    params: ApiParams<{
      type: XRayType;
      prompt: string;
    }>;
  }
  export type CreateData = Pick<
    XRay,
    | 'icon'
    | 'title'
    | 'icon'
    | 'description'
    | 'type'
    | 'prompt'
    | 'shortSummary'
    | 'frequency'
    | 'alertChannels'
  > &
    Partial<Pick<XRay, 'isActive'>>;
  export interface Create {
    response: ApiResponse<RequestResponse<XRay>>;
    params: ApiParams<CreateData>;
  }
  export type UpdateData = Partial<XRay>;
  export interface Update {
    response: ApiResponse<RequestResponse<XRay>>;
    params: ApiParams<UpdateData>;
  }
  export interface Delete {
    response: ApiResponse<RequestResponse>;
    params: ApiParams;
  }
  export interface Share {
    response: ApiResponse<RequestResponse<XRayTemplate>>;
    params: ApiParams<{ id: string }>;
  }
  export interface GetNotifications {
    response: ApiResponse<PaginatedRequestResponse<{ notifications: XRayNotification[] }>>;
    params: ApiParams<never, PaginatedQueryParams>;
  }
  export interface MarkNotificationsSeen {
    response: ApiResponse<RequestResponse>;
    params: ApiParams;
  }
  export interface GetTemplateById {
    response: ApiResponse<RequestResponse<XRayTemplateComplete>>;
    params: ApiParams;
  }
  export interface GetTemplates {
    response: ApiResponse<PaginatedRequestResponse<{ templates: XRayTemplate[] }>>;
    params: ApiParams<never, PaginatedQueryParams>;
  }
}
