import PageLoader from '@core/components/Loader/Page/PageLoader';
import {
  selectAuthStatus,
  selectCurrentUserId,
  selectCurrentUserOnboarding,
  useAuthStore,
} from '@waitroom/auth';
import { ReactNode } from 'react';
import { Intro } from '../../core/components/Intro/Intro';

export const OnboardedRoutes = ({ children }: { children: ReactNode }) => {
  const id = useAuthStore(selectCurrentUserId); // complete user info is required
  const isLoading = useAuthStore(selectAuthStatus) === 'loading';
  const onboarding = useAuthStore(selectCurrentUserOnboarding);

  if (isLoading || !id) return <PageLoader />;
  if (onboarding?.['0']) return <>{children}</>; // if intro complete
  return <Intro />;
};
