import { Badge, BadgeProps, Text } from '@chakra-ui/react';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';

export type PremiumBadgeProps = BadgeProps & {
  responsive?: boolean;
};

const PremiumBadge = ({ responsive, ...props }: PremiumBadgeProps): ReactElement | null => {
  const { t } = useTranslation();
  return (
    <Badge
      rounded="md"
      size="2xs"
      bgColor="gray.900"
      color="gray.300"
      textTransform="uppercase"
      px={[2, 3]}
      py={1.5}
      cursor="default"
      title={t('pricing.premium')}
      {...props}
    >
      <Text as="span" display={responsive ? ['none', 'initial'] : undefined}>
        {t('pricing.premium')}
      </Text>
      {!!responsive && (
        <Text as="span" display={['initial', 'none']}>
          P
        </Text>
      )}
    </Badge>
  );
};

export default PremiumBadge;
