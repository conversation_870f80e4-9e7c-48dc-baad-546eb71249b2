import Modal, { ModalProps } from '@core/components/Modal/Modal';
import { FnOnSubmitRef, UnboundFormProvider } from '@core/contexts/UnboundForm';
import { ModalState } from '@waitroom/common';
import { getApiResponseData } from '@waitroom/common-api';
import { SessionStatus } from '@waitroom/models';
import { useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { CRM as Crm } from '../../../../integrations';
import { SessionForm } from '../../Form/Session/SessionForm';
import { SessionFormProps, TMutationOptions } from '../../Form/Session/SessionForm.types';
import { Setup } from '../../Page/Integrations/Setup';

export type SessionFormModalProps = ModalState<SessionFormProps> & ModalProps;

const SessionFormModal = ({ props, isOpen = false, onClose, ...rest }: SessionFormModalProps) => {
  const { t } = useTranslation();
  const onSubmitRef = useRef<FnOnSubmitRef>({});
  const { sessionID, sessionRecurrenceID } = props?.initialValues || {};
  const onSuccess: NonNullable<TMutationOptions['onSuccess']> = useCallback(
    (data, ...rest) => {
      if (onSubmitRef.current) {
        const s = getApiResponseData(data)?.session;
        if (!s) return;
        const fns = Object.values(onSubmitRef.current);
        fns.forEach((fn) => fn(s.sessionID, s.sessionRecurrenceID));
      }

      onClose?.();
      props?.onSuccess?.(data, ...rest);
    },
    [onClose, props],
  );

  return (
    <Modal
      size="lg"
      isOpen={isOpen && !!props}
      onClose={onClose}
      isCentered
      scrollBehavior="inside"
      contentProps={{
        bg: 'gray.900',
      }}
      {...rest}
    >
      <Modal.Header>{t(props?.isEdit ? 'global.editMeeting' : 'global.newMeeting')}</Modal.Header>
      <Modal.Body>
        {props && (
          <SessionForm
            componentProps={{ minH: '320px' }}
            stickyControls
            {...props}
            onSuccess={onSuccess}
          >
            <Setup sessionId={sessionID} recurrenceId={sessionRecurrenceID} />
            <UnboundFormProvider onSubmitRef={onSubmitRef} autoSave>
              <Crm
                sessionStatus={SessionStatus.SCHEDULED}
                sessionId={sessionID}
                sessionRecurrenceId={sessionRecurrenceID}
              />
            </UnboundFormProvider>
          </SessionForm>
        )}
      </Modal.Body>
    </Modal>
  );
};

export default SessionFormModal;
