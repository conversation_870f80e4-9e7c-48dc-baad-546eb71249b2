import { useToast } from '@chakra-ui/react';
import copy from 'copy-to-clipboard';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import Toast from '../components/Toast/Toast';

export type UseClipboardResponse = {
  onCopy: (link?: string) => void;
};

export const useClipboard = (
  value = window.location.href,
  id = 'copy-link',
): UseClipboardResponse => {
  const { t } = useTranslation();
  const toast = useToast();

  const onCopy = useCallback(
    (link?: string) => {
      if (!toast.isActive(id)) {
        toast({
          id,
          render: () => <Toast fontWeight="bold">{t('global.linkCopied')}</Toast>,
          containerStyle: {
            minWidth: 0,
          },
          duration: 2500,
        });
      }
      copy(link ?? value);
    },
    [id, t, toast, value],
  );

  return { onCopy };
};
