{"name": "@waitroom/common", "version": "1.262.0", "license": "UNLICENSED", "author": "Rumi <<EMAIL>>", "description": "Rumi Common", "type": "module", "source": "src/index.ts", "types": "./dist/index.d.ts", "main": "./dist/index.es.js", "module": "./dist/index.es.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.es.js"}}, "repository": {"type": "git", "url": "git+https://github.com/Waitroom/rumi.ai"}, "bugs": {"url": "https://github.com/Waitroom/rumi.ai/issues"}, "keywords": [], "files": ["dist"], "scripts": {"start": "echo 'Noop'", "build": "rimraf dist && vite build && tsc --emitDeclarationOnly --project tsconfig.build.json", "build:libs": "yarn run build", "test": "vitest run", "test:cov": "vitest run --coverage", "test:nocov": "vitest run", "test:watch": "vitest", "test:related": "cross-env CI=true vitest related --run --passWithNoTests", "lint": "yarn run eslint", "lint:ts": "tsc --noEmit", "format": "prettier . --write", "publish": "yarn npm publish || true"}, "peerDependencies": {"@tanstack/react-query": "~5.74.0", "@waitroom/auth": "workspace:*", "@waitroom/braid": "workspace:*", "@waitroom/common-api": "workspace:*", "@waitroom/http-client": "workspace:*", "@waitroom/models": "workspace:*", "@waitroom/react-query": "workspace:*", "@waitroom/react-utils": "workspace:*", "@waitroom/state": "workspace:*", "@waitroom/utils": "workspace:*", "date-fns": "~4.1.0", "fast-json-patch": "~3.1.1", "nanoid": "~5.1.5", "react": ">=19.1.0", "reselect": "~5.1.1", "zustand": "~5.0.5"}, "dependencies": {"deepmerge": "~4.3.1"}, "devDependencies": {"@faker-js/faker": "~9.7.0", "@tanstack/react-query": "~5.74.0", "@testing-library/jest-dom": "~6.6.3", "@testing-library/react": "~16.3.0", "@types/node": "~22.15.14", "@types/react": "~19.1.5", "@vitejs/plugin-react": "~4.4.1", "@waitroom/auth": "workspace:*", "@waitroom/braid": "workspace:*", "@waitroom/common-api": "workspace:*", "@waitroom/http-client": "workspace:*", "@waitroom/logger": "workspace:*", "@waitroom/models": "workspace:*", "@waitroom/react-query": "workspace:*", "@waitroom/react-utils": "workspace:*", "@waitroom/state": "workspace:*", "@waitroom/tests": "workspace:*", "@waitroom/utils": "workspace:*", "date-fns": "~4.1.0", "eslint": "~9.24.0", "fast-json-patch": "~3.1.1", "happy-dom": "~17.5.6", "nanoid": "~5.1.5", "prettier": "~3.5.3", "react": "~19.1.0", "reselect": "~5.1.1", "typescript": "~5.8.3", "vite": "~6.3.5", "vitest": "~3.1.3", "vitest-dom": "~0.1.1", "zustand": "~5.0.5"}, "publishConfig": {"registry": "https://npm.pkg.github.com/"}}