import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON>ane<PERSON>, <PERSON>b<PERSON><PERSON><PERSON>, Tabs } from '@chakra-ui/react';
import { getInfinityRequestData } from '@waitroom/react-query';
import { ReactElement, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { baseNs } from '../../../constants';
import SharedTemplates from '../Shared';
import UserXRays, { UserXRaysProps } from '../User';

export type XRayGalleryTabsProps = {
  query: UserXRaysProps['query'];
};

const XRayGalleryTabs = ({ query }: XRayGalleryTabsProps): ReactElement | null => {
  const { t } = useTranslation();
  const hasXRays = !!getInfinityRequestData(query.data)?.xrays?.length;
  const [tabIndex, setTabIndex] = useState(() => (hasXRays ? 0 : 1));

  const handleTabsChange = (index: number) => {
    setTabIndex(index);
  };

  return (
    <Tabs overflow={'hidden'} colorScheme="orange" index={tabIndex} onChange={handleTabsChange}>
      <TabList borderBottomWidth="2px" borderColor="gray.600">
        <Tab fontSize={{ base: 'sm', md: 'md' }}>{t(`${baseNs}.yourXrays`)}</Tab>
        <Tab fontSize={{ base: 'sm', md: 'md' }}>{t(`${baseNs}.exploreXrays`)}</Tab>
      </TabList>
      <TabPanels pt={4}>
        <TabPanel px={0}>
          <UserXRays query={query} onTabChange={handleTabsChange} />
        </TabPanel>
        <TabPanel px={0}>
          <SharedTemplates hasXRays={hasXRays} />
        </TabPanel>
      </TabPanels>
    </Tabs>
  );
};
export default XRayGalleryTabs;
