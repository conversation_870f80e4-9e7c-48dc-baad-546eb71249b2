import { ButtonGroup, Flex, FlexProps, IconButton, Text, Tooltip } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import {
  faArrowsRotate,
  faCopy,
  faThumbsDown,
  faThumbsUp,
} from '@fortawesome/pro-regular-svg-icons';
import {
  faThumbsDown as faThumbsDownSolid,
  faThumbsUp as faThumbsUpSolid,
} from '@fortawesome/pro-solid-svg-icons';
import { EventType } from '@waitroom/analytics';
import { useFeedback, useRefreshLastResponse } from '@waitroom/common';
import { MeetingMemory } from '@waitroom/models';
import { useTranslation } from 'react-i18next';
import { analyticsService } from '../../../../../../analytics/services';
import { useCopyToClipboard } from '../../../../../hooks/useActions';
import TextToSpeech, { TextToSpeechProps } from './TextToSpeech/TextToSpeech';
import { buttonStylesDark, buttonStylesSx } from './styles';

export type ActionsProps = FlexProps & {
  cacheId: string;
  threadId: string;
  message: MeetingMemory.Message;
  sources?: MeetingMemory.Sources;
  onToggleSources: () => void;
  isOpenSources: boolean;
  refresh?: boolean;
  isStreaming?: boolean;
  contentRef: TextToSpeechProps['contentRef'];
};

export const Actions = ({
  cacheId,
  threadId,
  message,
  sources,
  onToggleSources,
  isOpenSources,
  refresh,
  isStreaming,
  contentRef,
  ...props
}: ActionsProps) => {
  const { t } = useTranslation('meetingMemory');
  const { thumbsDown, thumbsUp, isPending: feedbackLoading } = useFeedback(cacheId, message);
  const { isCopied, copyToClipboard } = useCopyToClipboard(message);
  const { refreshLastResponse, isPending: isLoadingAi } = useRefreshLastResponse(
    cacheId,
    threadId,
    message,
  );
  const sourcesCount = sources ? Object.keys(sources).length : 0;
  return (
    <Flex align="center" h={buttonStylesSx.h} gap={8} {...props}>
      {sourcesCount > 0 && (
        <Text
          fontSize="sm"
          color="gray.500"
          fontWeight="bold"
          onClick={onToggleSources}
          textDecoration={!isOpenSources ? 'underline' : 'none'}
          _hover={{
            color: 'red.500',
            cursor: 'pointer',
            textDecoration: 'underline',
          }}
        >
          {t('sourcesFound', {
            count: sourcesCount,
          })}
        </Text>
      )}
      {!isStreaming && (
        <ButtonGroup data-testid="actions" alignItems={'center'} variant="unstyled" p={0} gap={2}>
          <Tooltip label={t('thumbsUp')}>
            <IconButton
              sx={buttonStylesSx}
              _dark={buttonStylesDark}
              aria-label={t('thumbsUp')}
              onClick={() => {
                thumbsUp();
                analyticsService.track(EventType.MeetingMemoryMessageFeedback, { positive: true });
              }}
              isLoading={feedbackLoading === 1}
              isDisabled={!!feedbackLoading || isLoadingAi}
            >
              <Icon
                color={message.feedback === 1 ? 'gray.800' : undefined}
                icon={message.feedback === 1 ? faThumbsUpSolid : faThumbsUp}
              />
            </IconButton>
          </Tooltip>
          <Tooltip label={t('thumbsDown')}>
            <IconButton
              sx={buttonStylesSx}
              _dark={buttonStylesDark}
              aria-label={t('thumbsDown')}
              onClick={() => {
                thumbsDown();
                analyticsService.track(EventType.MeetingMemoryMessageFeedback, { positive: false });
              }}
              isLoading={feedbackLoading === 2}
              isDisabled={!!feedbackLoading || isLoadingAi}
            >
              <Icon
                color={message.feedback === 2 ? 'gray.800' : undefined}
                icon={message.feedback === 2 ? faThumbsDownSolid : faThumbsDown}
              />
            </IconButton>
          </Tooltip>
          {refresh && (
            <Tooltip label={t('refresh')}>
              <IconButton
                sx={buttonStylesSx}
                _dark={buttonStylesDark}
                aria-label={t('refresh')}
                isDisabled={!!feedbackLoading || isLoadingAi}
                onClick={() => {
                  refreshLastResponse();
                  analyticsService.track(EventType.MeetingMemoryMessageReload, {});
                }}
                isLoading={isLoadingAi}
              >
                <Icon icon={faArrowsRotate} />
              </IconButton>
            </Tooltip>
          )}
          <Tooltip label={t(isCopied ? 'copied' : 'copy')} isOpen={isCopied}>
            <IconButton
              sx={buttonStylesSx}
              _dark={buttonStylesDark}
              aria-label={t('copy')}
              isDisabled={isLoadingAi}
              onClick={() => {
                copyToClipboard();
                analyticsService.track(EventType.MeetingMemoryMessageCopy, {});
              }}
            >
              <Icon icon={faCopy} />
            </IconButton>
          </Tooltip>
          <TextToSpeech text={message.content} contentRef={contentRef} />
        </ButtonGroup>
      )}
    </Flex>
  );
};
