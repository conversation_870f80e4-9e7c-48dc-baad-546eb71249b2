import { Button } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { useClipboard } from '@core/hooks/useClipboard';
import { faLink } from '@fortawesome/pro-solid-svg-icons';
import {
  selectCurrentSessionId,
  selectCurrentSessionRecurrenceId,
  useSessionStore,
} from '@waitroom/common';
import { memo, ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { getSessionFullLink } from '../../../../../utils/helpers';

const CopyLink = memo((): ReactElement | null => {
  const { t } = useTranslation();
  const id = useSessionStore(selectCurrentSessionId) || '';
  const recurrenceId = useSessionStore(selectCurrentSessionRecurrenceId);
  const { onCopy } = useClipboard(getSessionFullLink(id, recurrenceId));
  return (
    <Button
      onClick={() => onCopy()}
      size={'3xs'}
      colorScheme={'gray.700'}
      variant={'outline'}
      leftIcon={<Icon icon={faLink} size={'lg'} />}
    >
      {t('global.copyLink')}
    </Button>
  );
});
export default CopyLink;
