import { Center, Container, Heading, SlideFade, VStack } from '@chakra-ui/react';
import { memo, ReactNode } from 'react';
import { Trans } from 'react-i18next';

export type LeftContainerTextProps = {
  breadcrumb: string;
  title: string;
  description?: string;
};

export type LeftContainerProps = LeftContainerTextProps & {
  step: number;
  children?: ReactNode | (({ onNext }: { onNext?: () => void }) => ReactNode);
  onNext?: () => void;
};

export const Left = memo(
  ({ breadcrumb, title, description, step, children, onNext }: LeftContainerProps) => {
    return (
      <Center h="full" w="full">
        <Container maxW="lg" py={10}>
          <SlideFade
            key={step}
            in
            offsetX={10}
            offsetY={0}
            transition={{ enter: { duration: 0.9 } }}
          >
            <VStack align="start" spacing={[6, 6, 8]}>
              <p>{breadcrumb}</p>
              <Heading as="h1" size="4xl">
                {title}
              </Heading>
              {!!description && (
                <div>
                  <Trans fontSize="xl">{description}</Trans>
                </div>
              )}
              {typeof children === 'function' ? children({ onNext }) : children}
            </VStack>
          </SlideFade>
        </Container>
      </Center>
    );
  },
);
