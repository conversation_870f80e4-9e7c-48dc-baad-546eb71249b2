import {
  Box,
  Button,
  Flex,
  <PERSON>ing,
  IconButton,
  Image,
  Input,
  InputGroup,
  InputRightElement,
  Link,
  Text,
} from '@chakra-ui/react';
import { usePostHogFeatureFlag } from '@core/components/FeatureFlagGate/FeatureFlag';
import { Response, Validation } from '@core/components/Form/Form';
import { FormControl } from '@core/components/FormControl/FormControl';
import { Icon } from '@core/components/Icon/Icon';
import { providers } from '@core/components/Integrations/Notetaker.constants';
import { commonConfig } from '@core/config';
import { faArrowRight, faVideo } from '@fortawesome/pro-regular-svg-icons';
import { faArrowUpRightFromSquare } from '@fortawesome/pro-solid-svg-icons';
import { getRecallIntegrationConnections } from '@modules/integrations/hooks/useRecallIntegration';
import { useQuery } from '@tanstack/react-query';
import { getResponse } from '@waitroom/common-api';
import { PostHogFeatureFlag } from '@waitroom/models';
import { memo, ReactElement } from 'react';
import { ns } from '../constants';
import { useAddBotToMeetingForm } from './useAddBotToMeetingForm';

const Bots = memo((): ReactElement | null => {
  const botsEnabled = usePostHogFeatureFlag(PostHogFeatureFlag.RecallCalendarIntegration);
  const query = useQuery(getRecallIntegrationConnections);
  const connections = query.data?.connections;
  const {
    t,
    methods: {
      register,
      handleSubmit,
      formState: { errors, isDirty },
    },
    mutation,
  } = useAddBotToMeetingForm();

  if (!botsEnabled || !connections?.length) return null;
  return (
    <Box py={8} borderBottom={'1px'} borderColor={'gray.700'}>
      <Flex mb={4}>
        <Icon icon={faVideo} size={'lg'} color={'gray.500'} mr={3} />
        <Heading as={'h4'} size={'md'}>
          {t(`${ns}.botsTitle`)}
        </Heading>
      </Flex>
      <Text color={'gray.300'} fontSize={'sm'} mb={4}>
        {t(`${ns}.botsSubtext`)}
      </Text>
      <Flex align="center" gap={2} mb={5}>
        <Text fontWeight={700} fontSize={'sm'} mr={2}>
          {t(`global.supports`)}:
        </Text>
        {Object.keys(providers).map((support) => (
          <Image
            key={support}
            src={providers[support as keyof typeof providers]}
            alt={support}
            w={5}
            m={0.5}
          />
        ))}
      </Flex>
      <Box mb={4}>
        <form
          onSubmit={handleSubmit((values) => {
            mutation.mutate(values);
          })}
        >
          <FormControl isInvalid={!!errors.meetingLink}>
            <InputGroup w={'full'}>
              <Input
                placeholder={t(`${ns}.botsPlaceholder`)}
                size={'sm'}
                pr={12}
                {...register('meetingLink')}
              />
              <InputRightElement>
                <IconButton
                  type={'submit'}
                  colorScheme={'gray.300'}
                  isLoading={mutation.isPending}
                  size={'2xs'}
                  w={'full'}
                  mt={2}
                  mr={2}
                  isDisabled={!isDirty}
                  aria-label={t('global.submit')}
                >
                  <Icon icon={faArrowRight} size={'lg'} />
                </IconButton>
              </InputRightElement>
            </InputGroup>
          </FormControl>
          <Validation errors={errors} mt={2} fontSize={'xs'} />
          {mutation.error ? (
            <Response response={getResponse(mutation.error)} mt={2} fontSize={'xs'} />
          ) : null}
        </form>
      </Box>
      <div>
        <Button
          as={Link}
          href={commonConfig.links.knowledgebase.notetaker}
          variant="link"
          size={'sm'}
          color={'gray.500'}
          leftIcon={<Icon icon={faArrowUpRightFromSquare} />}
          isExternal
        >
          {t(`${ns}.botsLearnHow`)}
        </Button>
      </div>
    </Box>
  );
});
export default Bots;
