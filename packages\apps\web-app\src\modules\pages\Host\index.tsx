import { fullscreenLoader } from '@core/components/Loader/Page/PageLoader';
import {
  selectCurrentUserEmail,
  selectCurrentUserOnboarding,
  useAuth,
  useAuthStore,
} from '@waitroom/auth';
import { ReactElement, useMemo } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { routes } from '../../../constants/routes';
import { Intro } from '../../core/components/Intro/Intro';
import { useAppStore } from '../../core/store/store';

const HostPage = (): ReactElement | null => {
  const { state } = useLocation();
  const { isLoading } = useAuth();
  const intro = useAuthStore(selectCurrentUserOnboarding)?.[0];
  const hostsOnboarded = useAppStore.use.hostsOnboarded();
  const email = useAuthStore(selectCurrentUserEmail);
  const hasOnboarded = useMemo(() => {
    if (!hostsOnboarded) return false;
    return !!(email && hostsOnboarded[email]);
  }, [email, hostsOnboarded]);

  if (isLoading) return fullscreenLoader;
  if (hasOnboarded || !!intro) {
    return <Navigate to={routes.DASHBOARD.DEFAULT.link} state={state} replace />;
  }
  return <Intro />;
};

export default HostPage;
