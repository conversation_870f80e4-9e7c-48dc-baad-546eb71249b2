import { Box, Flex, Link, SimpleGrid } from '@chakra-ui/react';
import CardLoader from '@core/components/Loader/Card/CardLoader';
import { useInfiniteQuery } from '@tanstack/react-query';
import { config } from '@waitroom/config';
import { getInfinityRequestData, xrayTemplatesQuery } from '@waitroom/react-query';
import { repeat } from '@waitroom/react-utils';
import { ReactElement } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { baseNs } from '../../../constants';
import XRayError from '../../../Error';
import XRayCard from '../Card';
import XRayCreateCard from '../Card/Create';

export interface SharedTemplatesProps {
  hasXRays: boolean;
}

const SharedTemplates = ({ hasXRays }: SharedTemplatesProps): ReactElement | null => {
  const { t } = useTranslation();
  const { data, isPending, refetch, isError, isRefetching } =
    useInfiniteQuery(xrayTemplatesQuery());
  const templates = getInfinityRequestData(data)?.templates;

  if (isError) {
    return (
      <Box my={4}>
        <XRayError onRetry={refetch} isLoading={isRefetching}>
          {t(`${baseNs}.templatesError`)}
        </XRayError>
      </Box>
    );
  }
  return (
    <>
      <Flex align={'center'} mb={6} minH={'40px'} fontSize={'sm'}>
        <p>
          {hasXRays ? (
            <Trans i18nKey={`${baseNs}.exploreDesc`} />
          ) : (
            <Trans
              i18nKey={`${baseNs}.exploreDescEmpty`}
              components={{
                help: <Link className="none" href={config.links.help} rel="noopener noreferrer" />,
              }}
            />
          )}
        </p>
      </Flex>
      <SimpleGrid columns={{ base: 1, md: 2, '2xl': 3 }} spacing={5}>
        <XRayCreateCard />
        {isPending ? (
          <>{repeat(<CardLoader bg={'gray.900'} image={false} lines={6} />, 4)}</>
        ) : (
          templates?.map((template) => <XRayCard key={template.id} data={template} isTemplate />)
        )}
      </SimpleGrid>
    </>
  );
};
export default SharedTemplates;
