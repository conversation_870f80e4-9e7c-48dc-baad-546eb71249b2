import { routes } from '@/constants/routes';
import {
  Al<PERSON>,
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
  Card,
  Container,
  Flex,
  Heading,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import ContentLoader from '@core/components/Loader/Content/ContentLoader';
import Modal from '@core/components/Modal/Modal';
import { useClipboard } from '@core/hooks/useClipboard';
import { fullUrl } from '@core/utils/url';
import { defaultBodyPadding } from '@dashboard/components/Layout/Default/constants';
import { faEnvelope, faLink, faShare } from '@fortawesome/pro-solid-svg-icons';
import { useMutation, useQuery } from '@tanstack/react-query';
import { selectAuthUserId, useAuthStore } from '@waitroom/auth';
import { buildSuccessApiRequestResponse } from '@waitroom/common-api';
import { XRay, XRayComplete } from '@waitroom/models';
import { getQueryRequestData, shareXRayMutation, xrayGetByIdQuery } from '@waitroom/react-query';
import { Trans, useTranslation } from 'react-i18next';
import {
  generatePath,
  Link,
  Navigate,
  Link as RouterLink,
  useLocation,
  useParams,
} from 'react-router-dom';
import { baseNs } from '../../constants';
import XRayError from '../../Error';
import Preview from '../../Preview';

const ns = `${baseNs}.share`;

const XRayEditPage = () => {
  const { t } = useTranslation();
  const userId = useAuthStore(selectAuthUserId);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { onCopy } = useClipboard();
  const params = useParams<{ id: string }>();
  const id = params.id ? Number(params.id) : undefined;
  const state = useLocation().state as XRay | undefined;
  const hasId = id !== undefined;
  const query = useQuery({
    ...xrayGetByIdQuery(id || -1, userId || ''),
    refetchOnWindowFocus: false,
    enabled: hasId,
    placeholderData: buildSuccessApiRequestResponse(state as XRayComplete),
  });
  // coerce to XRayComplete | XRay | undefined because of placeholderData
  const data: XRayComplete | XRay | undefined = getQueryRequestData(query);
  const mutation = useMutation({
    ...shareXRayMutation({ id: id || -1 }),
    onSuccess: () => {
      onOpen();
    },
  });
  const response = getQueryRequestData(mutation);

  if (!id) return <Navigate to={routes.DASHBOARD.XRAY.DEFAULT.link} />;
  const link = fullUrl(
    generatePath(routes.DASHBOARD.XRAY.SETUP.fullRoute, {
      id: response?.id,
    }),
  );
  const mailtoSubject = t(`${ns}.mailtoSubject`, {
    title: data?.title,
  });
  const mailtoBody = t(`${ns}.mailtoBody`, {
    link,
  });
  return (
    <Flex
      direction={{ base: 'column', lg: 'row' }}
      minH="100vh"
      bg="gray.1000"
      {...defaultBodyPadding}
    >
      <Container
        flex={1}
        maxW={{ base: '100%', lg: 'container.xl' }}
        mx="auto"
        px={{ base: 4, md: 8 }}
      >
        {query.isPending && !data ? (
          <ContentLoader avatar={false} py={12} />
        ) : !data || query.isError ? (
          <Box my={6}>
            <XRayError
              onRetry={() => {
                query.refetch();
              }}
              isLoading={query.isRefetching}
            >
              {t(`${baseNs}.basicError`)}
            </XRayError>
          </Box>
        ) : (
          <>
            <Flex
              wrap={'wrap'}
              align="center"
              justify="space-between"
              gap={2}
              color={'gray.300'}
              fontSize={'xs'}
              mb={[6, 8]}
            >
              <Breadcrumb spacing={1} fontWeight={'bold'}>
                <BreadcrumbItem>
                  <BreadcrumbLink as={RouterLink} to={routes.DASHBOARD.XRAY.DEFAULT.link}>
                    {t('global.xray')}
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbItem>
                  <BreadcrumbLink
                    as={Link}
                    to={generatePath(routes.DASHBOARD.XRAY.EDIT.fullRoute, { id })}
                  >
                    {data.title}
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbItem isCurrentPage>
                  <BreadcrumbLink href="#">{t('global.share')}</BreadcrumbLink>
                </BreadcrumbItem>
              </Breadcrumb>
            </Flex>
            <Card
              w={'full'}
              rounded={'xl'}
              borderWidth={'1px'}
              borderColor={'gray.600'}
              bg={'gray.1000'}
              maxH={'full'}
              overflow={{ base: 'hidden', md: 'auto' }}
              className="scroll-light scroll-bg"
            >
              <Flex
                w={'full'}
                direction={{ base: 'column', lg: 'row' }}
                gap={4}
                align={'center'}
                justify={'space-between'}
                px={[4, 5, 6]}
                py={[4, 5, 5, 5, 6]}
                borderBottom={'1px'}
                borderColor={'gray.600'}
                position={'sticky'}
                top={0}
                bg={'gray.1000'}
                zIndex={2}
              >
                <Heading as={'h3'} size={'lg'}>
                  {t(`${ns}.title`)}
                </Heading>
              </Flex>
              <Flex p={[4, 5, 6, 10, 12]} direction={'column'}>
                <Flex direction={'column'} w={'full'} gap={6} mb={12}>
                  <Text>
                    <Trans i18nKey={`${ns}.alert`} />
                  </Text>
                  <Alert fontSize={'sm'} status="warning">
                    <p>
                      <Trans i18nKey={`${ns}.alertWarning`} />
                    </p>
                  </Alert>
                </Flex>
                <Preview
                  data={{
                    icon: data.icon,
                    title: data.title,
                    type: data.type,
                    prompt: data.prompt,
                    shortSummary: data.shortSummary,
                  }}
                  hideEmpty
                />
              </Flex>
              <Flex
                direction={{ base: 'column', md: 'row' }}
                align={'center'}
                gap={2}
                px={6}
                py={[3, 3, 4]}
                borderTop="1px"
                borderColor="gray.600"
                position={'sticky'}
                bottom={0}
                bg={'gray.1000'}
                zIndex={2}
              >
                <Button
                  as={Link}
                  to={generatePath(routes.DASHBOARD.XRAY.EDIT.fullRoute, { id })}
                  className="none"
                  variant="outline"
                  size={{ base: 'xs', lg: 'sm' }}
                  type={'button'}
                  colorScheme={'gray.400'}
                  color={'white'}
                >
                  {t('global.discard')}
                </Button>
                <Button
                  colorScheme={'green'}
                  size={{ base: 'xs', lg: 'sm' }}
                  leftIcon={<Icon icon={faShare} size={'lg'} />}
                  isLoading={mutation.isPending}
                  onClick={() => (mutation.isSuccess ? onOpen() : mutation.mutate())}
                >
                  {t(`${ns}.confirmBtn`)}
                </Button>
              </Flex>
            </Card>
          </>
        )}
      </Container>
      <Modal isOpen={isOpen} onClose={onClose} size={'xl'} isCentered>
        <Modal.Header>{t(`${ns}.modalTitle`)}</Modal.Header>
        <Modal.Body>
          <Text mb={6}>{t(`${ns}.modalDesc`)}</Text>
          <Flex direction={'column'} gap={4}>
            <Button
              colorScheme={'black'}
              leftIcon={<Icon icon={faLink} />}
              size={'def'}
              onClick={() => {
                onCopy(link);
              }}
            >
              {t(`${ns}.modalLinkBtn`)}
            </Button>
            <Button
              variant={'outline'}
              colorScheme={'black'}
              leftIcon={<Icon icon={faEnvelope} />}
              as={'a'}
              size={'def'}
              className={'none'}
              target="_blank"
              rel="noreferrer noopener"
              href={`mailto:?subject=${encodeURIComponent(mailtoSubject)}&body=${encodeURIComponent(mailtoBody)}`}
            >
              {t(`${ns}.modalEmailBtn`)}
            </Button>
          </Flex>
        </Modal.Body>
      </Modal>
    </Flex>
  );
};

export default XRayEditPage;
