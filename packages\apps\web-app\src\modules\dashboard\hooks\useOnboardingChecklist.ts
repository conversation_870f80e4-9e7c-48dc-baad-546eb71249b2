import { PostHogFeatureFlag, User, userOnboardingChecklistValues } from "@waitroom/models";
import { useMemo } from "react";
import { userOnboarding } from "@waitroom/models";
import { usePostHogFeatureFlag } from "../../core/components/FeatureFlagGate/FeatureFlag";
import { selectCurrentUserOnboarding, useAuthStore } from "@waitroom/auth";

const checkCompleted = (
  list: typeof userOnboardingChecklistValues,
  onboarding: User['onboarding'],
) => {
  if (!onboarding) return 0;
  return list.filter((val) => !!onboarding[val]).length;
};

export const useOnboardingChecklist = () => {
  const onboarding = useAuthStore(selectCurrentUserOnboarding);
  const botsEnabled = usePostHogFeatureFlag(PostHogFeatureFlag.RecallCalendarIntegration);
  const checklist = useMemo(
    () =>
      userOnboardingChecklistValues.filter(
        (val) => botsEnabled || val !== userOnboarding.noteTaker,
      ),
    [botsEnabled],
  );
  const totalCount = checklist.length;

  // check if all checklist items are completed
  const completedCount = useMemo(
    () => checkCompleted(checklist, onboarding),
    [checklist, onboarding],
  );
  const uncompletedCount = totalCount - completedCount;
  const isCompleted = !uncompletedCount;

  return {
    checklist,
    totalCount,
    completedCount,
    uncompletedCount,
    isCompleted,
    progress: !totalCount ? 100 : 100 * (completedCount / totalCount),
  };
};