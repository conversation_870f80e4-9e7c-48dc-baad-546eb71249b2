import { Alert, AlertIcon, useToast } from '@chakra-ui/react';
import { usePostHogFeatureFlag } from '@core/components/FeatureFlagGate/FeatureFlag';
import { TwoColumns } from '@core/components/Layouts/TwoColumns/TwoColumns';
import { releaseIds } from '@core/hooks/useReleases';
import { EventType } from '@waitroom/analytics';
import {
  selectCurrentUserEmail,
  selectCurrentUserSubscriptionPlan,
  useAuthStore,
} from '@waitroom/auth';
import { isFreePlan, useActivateTrial, useHostOptIn, useUpdateOnboarding } from '@waitroom/common';
import { PostHogFeatureFlag, userOnboarding } from '@waitroom/models';
import { getRequestData } from '@waitroom/react-query';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { analyticsService } from '../../../analytics/services';
import { useAddTranslations } from '../../hooks/useAddTranslations';
import { useNotetaker } from '../../hooks/useNotetaker';
import { updateAppState } from '../../store/store';
import { stages, stagesWithBots } from './Intro.config';
import { Left, LeftContainerTextProps } from './Left/Left';
import { trans as locals } from './local';
import { Right } from './Right/Right';

export const Intro = () => {
  useAddTranslations(locals);
  const { t } = useTranslation();
  const [stage, setStage] = useState(0);
  const trans = (t('dashboard.onboarding.intro.steps', {
    returnObjects: true,
  }) || []) as LeftContainerTextProps[];
  const botsEnabled = usePostHogFeatureFlag(PostHogFeatureFlag.RecallCalendarIntegration);
  const { hasANotetakerConnected } = useNotetaker();
  const introStages = useMemo(
    () => (botsEnabled && !hasANotetakerConnected ? stagesWithBots : stages),
    [botsEnabled, hasANotetakerConnected],
  );
  const {
    mutation: { mutateAsync: mutateAsyncOnboarding },
    isLoading,
  } = useUpdateOnboarding();

  const email = useAuthStore(selectCurrentUserEmail);
  const setOnboarded = useCallback(() => {
    if (!email) return;
    updateAppState((prev) => ({
      ...prev,
      hostsOnboarded: {
        ...prev.hostsOnboarded,
        [email]: true,
      },
    }));
  }, [email]);
  const { mutateAsync: mutateAsyncHostOptIn, isPending: isHostOptInLoading } = useHostOptIn({});

  const plan = useAuthStore(selectCurrentUserSubscriptionPlan);
  const toast = useToast();
  const { isPending: isActivateTrialLoading, mutateAsync: mutateAsyncActivateTrial } =
    useActivateTrial({
      onSuccess: (response) => {
        const data = getRequestData(response);
        if (!data) return;
        if (data.authToken) {
          analyticsService.track(EventType.HostOptedIn, {});
        }
        if (data.userSubscriptionPlan?.status === 'trialing') {
          analyticsService.track(EventType.SubscriptionFreeTrialStarted, {
            isNewHost: false,
          });
        }
      },
      onError: () => {
        toast({
          position: 'top',
          duration: 6000,
          render: () => (
            <Alert status="error" fontWeight="bold" fontSize="lg" p={2}>
              <AlertIcon />
              {t('error.defaultTitle')}
            </Alert>
          ),
        });
      },
    });

  const onNext = useCallback(async () => {
    if (stage === introStages.length - 1) {
      if (plan && (plan.trialEndsAt || !isFreePlan(plan))) {
        await mutateAsyncHostOptIn();
        analyticsService.track(EventType.HostOptedIn, {});
      } else {
        await mutateAsyncActivateTrial();
      }
      await mutateAsyncOnboarding({
        [userOnboarding.intro]: true,
        [userOnboarding.features]: releaseIds.join(','),
      });
      // complete intro and mark all features as seen
      setOnboarded();
      updateAppState({ showChecklist: true });
      return;
    }
    setStage(stage + 1);
  }, [
    stage,
    introStages.length,
    mutateAsyncHostOptIn,
    mutateAsyncOnboarding,
    setOnboarded,
    mutateAsyncActivateTrial,
    plan,
  ]);
  const currentStage = introStages[stage];
  const left = (
    <Left step={stage} {...currentStage.left} {...trans[currentStage.key]} onNext={onNext} />
  );
  const right = (
    <Right
      {...currentStage.right}
      totalSteps={introStages.length}
      currentStep={stage}
      onNext={onNext}
      isLoading={!!isLoading?.['0'] || isHostOptInLoading || isActivateTrialLoading}
    />
  );

  return <TwoColumns leftColumn={left} rightColumn={right} />;
};
