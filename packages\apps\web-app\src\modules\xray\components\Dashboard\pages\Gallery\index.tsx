import { routes } from '@/constants/routes';
import { But<PERSON>, Container, Flex, Heading } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { defaultBodyPadding } from '@dashboard/components/Layout/Default/constants';
import { faPlus } from '@fortawesome/pro-regular-svg-icons';
import { useInfiniteQuery } from '@tanstack/react-query';
import { selectCurrentUserId, useAuthStore } from '@waitroom/auth';
import { xrayGetByUserQuery } from '@waitroom/react-query';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { generatePath, Link } from 'react-router-dom';
import ParagraphLoader from '../../../../../core/components/Loader/Paragraph/ParagraphLoader';
import { baseNs } from '../../constants';
import XRayGalleryTabs from './Tabs';

const XRayGalleryPage = (): ReactElement | null => {
  const { t } = useTranslation();
  const userId = useAuthStore(selectCurrentUserId) || '';
  const query = useInfiniteQuery(xrayGetByUserQuery(userId));

  return (
    <Container maxW="container.xl" {...defaultBodyPadding}>
      <Flex align="center" justify="space-between" mb={8}>
        <Heading as="h1" size="3xl">
          {t(`${baseNs}.title`)}
        </Heading>
        <Button
          as={Link}
          to={generatePath(routes.DASHBOARD.XRAY.SETUP.link)}
          variant="outline"
          colorScheme="red.700"
          color={'white'}
          size="xs"
          leftIcon={<Icon icon={faPlus} />}
        >
          {t(`${baseNs}.create`)}
        </Button>
      </Flex>
      {query.isLoading ? <ParagraphLoader header /> : <XRayGalleryTabs query={query} />}
    </Container>
  );
};

export default XRayGalleryPage;
