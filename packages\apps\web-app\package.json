{"name": "@waitroom/web-app", "version": "6.134.0", "author": "Rumi <<EMAIL>>", "license": "UNLICENSED", "description": "Allows people-of-influence the ability to share their thoughts and interact with others in a live video environment.", "repository": {"type": "git", "url": "https://github.com/Waitroom/rumi.ai", "directory": "@Waitroom/waitroom"}, "bugs": {"url": "https://github.com/Waitroom/rumi.ai/issues"}, "keywords": [], "files": ["build"], "scripts": {"analyze": "vite build -c vite.analyze.config.ts", "start": "vite", "start:ssl": "vite --config vite.ssl.config.mts", "profile": "vite --config vite.profile.config.mts", "build": "vite build", "serve": "vite preview --port 3000", "build:libs": "echo 'Noop'", "test": "vitest run", "test:cov": "vitest run --coverage", "test:nocov": "vitest run", "test:watch": "vitest", "test:e2e": "playwright test", "test:e2e:watch": "playwright test --ui", "test:e2e:setup": "yarn playwright install chromium", "test:related": "cross-env CI=true vitest related --run --passWithNoTests", "lint": "eslint", "lint:ts": "tsc --noEmit", "format": "prettier . --write", "lighthouse": "lhci autorun --preset=lighthouse:recommended", "publish": "echo 'Noop'"}, "dependencies": {"@chakra-ui/react": "~2.10.7", "@chakra-ui/theme-tools": "~2.2.6", "@date-fns/tz": "~1.2.0", "@emotion/react": "~11.14.0", "@emotion/styled": "~11.14.0", "@fortawesome/fontawesome-svg-core": "~6.7.2", "@fortawesome/free-brands-svg-icons": "~6.7.2", "@fortawesome/pro-regular-svg-icons": "~6.7.2", "@fortawesome/pro-solid-svg-icons": "~6.7.2", "@fortawesome/react-fontawesome": "~0.2.2", "@hookform/resolvers": "~5.0.1", "@lexical/react": "~0.31.0", "@livekit/components-react": "2.8.1", "@livekit/krisp-noise-filter": "~0.2.16", "@microsoft/fetch-event-source": "~2.0.1", "@million/lint": "~1.0.14", "@nangohq/frontend": "^0.57.0", "@paddle/paddle-js": "~1.4.1", "@peermetrics/sdk": "~2.7.2", "@popperjs/core": "~2.11.8", "@qwik.dev/partytown": "~0.11.1", "@react-oauth/google": "~0.12.1", "@segment/analytics-next": "~1.81.0", "@segment/in-eu": "~0.4.0", "@sentry/react": "~9.15.0", "@tanstack/query-sync-storage-persister": "~5.74.0", "@tanstack/react-query": "~5.74.0", "@tanstack/react-query-devtools": "~5.74.0", "@tanstack/react-query-persist-client": "~5.74.0", "@tanstack/react-table": "~8.21.3", "@tanstack/react-virtual": "~3.13.6", "@typeform/embed": "~5.3.1", "@types/dom-speech-recognition": "~0.0.6", "@use-gesture/react": "~10.3.1", "@vidstack/react": "next", "@waitroom/analytics": "workspace:*", "@waitroom/auth": "workspace:*", "@waitroom/braid": "workspace:*", "@waitroom/chat": "workspace:*", "@waitroom/common": "workspace:*", "@waitroom/common-api": "workspace:*", "@waitroom/config": "workspace:*", "@waitroom/hooks": "workspace:*", "@waitroom/http-client": "workspace:*", "@waitroom/logger": "workspace:*", "@waitroom/models": "workspace:*", "@waitroom/react-query": "workspace:*", "@waitroom/react-utils": "workspace:*", "@waitroom/state": "workspace:*", "@waitroom/stream": "workspace:*", "@waitroom/utils": "workspace:*", "ahooks": "~3.8.5", "bowser": "~2.11.0", "branch-sdk": "~2.86.3", "copy-to-clipboard": "~3.3.3", "cross-env": "7.0.3", "date-fns": "~4.1.0", "effects-sdk": "3.5.0", "emoji-picker-react": "~4.12.2", "fast-json-patch": "~3.1.1", "fingerprintjs2": "~2.1.4", "framer-motion": "~12.6.5", "hls.js": "~1.6.2", "i18next": "~25.1.1", "i18next-browser-languagedetector": "~8.0.5", "i18next-intervalplural-postprocessor": "~3.0.0", "immer": "~10.1.1", "jwt-decode": "~4.0.0", "lexical": "~0.31.0", "livekit-client": "2.9.3", "match-sorter": "~8.0.1", "nanoid": "~5.1.5", "posthog-js": "~1.240.0", "react": "~19.1.0", "react-app-polyfill": "~3.0.0", "react-apple-signin-auth": "1.1.1", "react-confetti-explosion": "~3.0.3", "react-cookie": "~8.0.1", "react-datepicker": "~7.4.0", "react-dom": "~19.1.0", "react-dropzone": "~14.3.8", "react-error-boundary": "~5.0.0", "react-headroom": "~3.2.1", "react-helmet-async": "2.0.5", "react-hook-form": "~7.56.2", "react-i18next": "~15.5.1", "react-markdown": "~10.1.0", "react-router": "~6.30.0", "react-router-dom": "~6.30.0", "react-textarea-autosize": "~8.5.9", "reselect": "~5.1.1", "serve": "14.2.4", "stream-chat": "~8.59.0", "use-broadcast-ts": "~2.0.1", "web-vitals": "~4.2.4", "zod": "~3.24.4", "zustand": "~5.0.5"}, "devDependencies": {"@faker-js/faker": "~9.7.0", "@playwright/test": "~1.52.0", "@testing-library/jest-dom": "~6.6.3", "@testing-library/react": "~16.3.0", "@types/branch-sdk": "~2.53.7", "@types/fingerprintjs2": "~2.0.0", "@types/invariant": "~2.2.37", "@types/node": "~22.15.14", "@types/react": "~19.1.5", "@types/react-dom": "~19.1.5", "@types/react-headroom": "~3.2.3", "@vitejs/plugin-basic-ssl": "^2.0.0", "@vitejs/plugin-react": "~4.4.1", "@waitroom/stories": "workspace:*", "@waitroom/tests": "workspace:*", "core-js": "~3.42.0", "eslint": "~9.24.0", "eslint-config-react-app": "~7.0.1", "eslint-plugin-import": "~2.31.0", "eslint-plugin-react-refresh": "~0.4.20", "happy-dom": "~17.5.6", "inquirer": "~12.6.0", "inquirer-directory": "2.2.0", "postcss-normalize": "~13.0.1", "prettier": "~3.5.3", "rehype-raw": "~7.0.0", "remark-gfm": "~4.0.1", "rollup-plugin-visualizer": "~5.14.0", "sass": "~1.86.3", "typescript": "~5.8.3", "vite": "~6.3.5", "vite-plugin-ejs": "~1.7.0", "vite-plugin-html": "~3.2.2", "vite-plugin-pwa": "~1.0.0", "vite-plugin-svgr": "~4.3.0", "vite-tsconfig-paths": "~5.1.4", "vitest": "~3.1.3", "vitest-canvas-mock": "~0.3.3", "vitest-dom": "~0.1.1", "workbox-background-sync": "~7.3.0", "workbox-broadcast-update": "~7.3.0", "workbox-cacheable-response": "~7.3.0", "workbox-core": "~7.3.0", "workbox-expiration": "~7.3.0", "workbox-google-analytics": "~7.3.0", "workbox-navigation-preload": "~7.3.0", "workbox-precaching": "~7.3.0", "workbox-range-requests": "~7.3.0", "workbox-routing": "~7.3.0", "workbox-strategies": "~7.3.0", "workbox-streams": "~7.3.0"}, "publishConfig": {"registry": "https://npm.pkg.github.com/"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"yarn": "4.9.1", "npm": ">=9.0.0", "node": ">=22.0.0"}}