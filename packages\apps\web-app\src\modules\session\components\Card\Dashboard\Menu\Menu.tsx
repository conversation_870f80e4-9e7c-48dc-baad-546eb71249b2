import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>uL<PERSON> } from '@chakra-ui/react';
import { ModalType } from '@core/components/App/Modals/types';
import { Icon } from '@core/components/Icon/Icon';
import { useClipboard } from '@core/hooks/useClipboard';
import { meetNowModalOnSuccess } from '@dashboard/components/Sessions/helpers';
import { faEllipsisVertical } from '@fortawesome/pro-solid-svg-icons';
import { QueryClient, useQueryClient } from '@tanstack/react-query';
import { selectCurrentUserId, useAuthStore } from '@waitroom/auth';
import { setModal } from '@waitroom/common';
import { Session } from '@waitroom/models';
import {
  getRequestData,
  sessionCacheService,
  UpdateSessionMutationOptionsProps,
} from '@waitroom/react-query';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { TeamMemorySwitch } from '../../../Buttons/TeamMemory';
import { SessionFormProps } from '../../../Form/Session/SessionForm.types';

export type DashboardSessionCardMenuProps = {
  session: Session;
  link: string;
  showEdit?: boolean;
  showTeamMemory?: boolean;
  teamMemoryText?: string;
  onDelete?: (session: Session) => void;
};

const onMemorySwitchSuccess = (session: Session, userId: string, queryClient: QueryClient) => {
  sessionCacheService.session.update({
    client: queryClient,
    userId,
    data: session,
  });
};

const DashboardSessionCardMenu = ({
  session,
  link,
  showEdit,
  showTeamMemory,
  teamMemoryText,
  onDelete,
}: DashboardSessionCardMenuProps): ReactElement | null => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { onCopy } = useClipboard(link);
  const userId = useAuthStore(selectCurrentUserId);

  const onSwitchSuccess: UpdateSessionMutationOptionsProps['onSuccess'] = (response) => {
    const s = getRequestData(response)?.session;
    if (!s || !userId) return;
    onMemorySwitchSuccess(s, userId, queryClient);
  };
  return (
    <Menu size="lg" placement="bottom-end">
      <MenuButton as={IconButton} colorScheme="gray.100" boxShadow="none" size="xs" rounded="full">
        <Icon icon={faEllipsisVertical} fontSize="xl" mt="px" />
      </MenuButton>
      <MenuList py={1} boxShadow="lg">
        {!!showEdit && (
          <>
            <MenuItem
              className="none"
              fontSize="sm"
              onClick={() => {
                setModal<SessionFormProps>({
                  type: ModalType.SESSION_FORM,
                  props: {
                    initialValues: session,
                    isEdit: true,
                    onSuccess: meetNowModalOnSuccess,
                  },
                });
              }}
            >
              {t('global.edit')}
            </MenuItem>
          </>
        )}
        {!!onDelete && (
          <>
            <MenuDivider my={0} />
            <MenuItem fontSize="sm" onClick={() => onDelete(session)}>
              {t('global.delete')}
            </MenuItem>
          </>
        )}
        <MenuDivider my={0} />
        <MenuItem fontSize="sm" onClick={() => onCopy()}>
          {t('global.copyLink')}
        </MenuItem>
        {!!showTeamMemory && (
          <>
            <MenuDivider my={0} />
            <MenuItem fontSize="sm" gap={2} closeOnSelect={false}>
              <span>{teamMemoryText || t('dashboard.teamMemorySwitchFuture')}</span>
              <TeamMemorySwitch
                sessionId={session.sessionID}
                recurrenceId={session.sessionRecurrenceID}
                dataVisibility={session.dataVisibility}
                onSuccess={onSwitchSuccess}
              />
            </MenuItem>
          </>
        )}
      </MenuList>
    </Menu>
  );
};
export default DashboardSessionCardMenu;
