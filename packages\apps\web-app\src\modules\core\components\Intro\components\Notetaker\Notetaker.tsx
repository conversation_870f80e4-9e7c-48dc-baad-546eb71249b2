import { ButtonProps, Flex, Image, Text, VStack } from '@chakra-ui/react';
import { PostHogFeatureFlagGate } from '@core/components/FeatureFlagGate/FeatureFlag';
import { providers } from '@core/components/Integrations/Notetaker.constants';
import { PostHogFeatureFlag } from '@waitroom/models';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { useAddTranslations } from '../../../../hooks/useAddTranslations';
import { transInt } from '../../local';
import { NotetakerButtons } from './Buttons';

export type NotetakerProps = {
  showButton?: boolean;
  onNext?: () => void;
};

const buttonProps: ButtonProps = {
  size: 'md',
  fontSize: 'md',
  variant: 'outline',
  colorScheme: 'gray.900',
  borderColor: 'gray.300',
  w: 'full',
  mt: 'auto',
  iconSpacing: 3,
};

export const Notetaker = ({ showButton = true, onNext }: NotetakerProps): ReactElement | null => {
  useAddTranslations(transInt);
  const { t } = useTranslation();
  return (
    <PostHogFeatureFlagGate flag={PostHogFeatureFlag.RecallCalendarIntegration}>
      <Flex direction={'column'} gap={10} w={'full'}>
        <Flex align={'center'} gap={4}>
          <Text fontWeight={'bold'}>{t('global.supports')}:</Text>
          {Object.values(providers).map((support) => (
            <Image key={support} src={support} alt={support} w={`${48}px`} h={'auto'} />
          ))}
        </Flex>
        <VStack gap={4}>
          {showButton && (
            <NotetakerButtons buttonProps={buttonProps} onNext={onNext} isSkipButton />
          )}
        </VStack>
      </Flex>
    </PostHogFeatureFlagGate>
  );
};
