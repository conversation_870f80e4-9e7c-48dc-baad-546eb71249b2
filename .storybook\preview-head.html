<style>
  @font-face {
    font-family: 'HK Nova';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src:
      url('https://cdn.rumi.ai/fonts/HKNova-Regular.woff2') format('woff2'),
      url('https://cdn.rumi.ai/fonts/HKNova-Regular.woff') format('woff');
  }
  @font-face {
    font-family: 'HK Nova';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src:
      url('https://cdn.rumi.ai/fonts/HKNova-Bold.woff2') format('woff2'),
      url('https://cdn.rumi.ai/fonts/HKNova-Bold.woff') format('woff');
  }
  @font-face {
    font-family: 'HK Nova';
    font-style: normal;
    font-weight: 800;
    font-display: swap;
    src:
      url('https://cdn.rumi.ai/fonts/HKNova-ExtraBold.woff2') format('woff2'),
      url('https://cdn.rumi.ai/fonts/HKNova-ExtraBold.woff') format('woff');
  }
  @font-face {
    font-family: 'Digital';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src:
      local('Digital'),
      url('https://cdn.rumi.ai/fonts/digital_7_mono_italic.ttf') format('truetype');
  }
</style>
