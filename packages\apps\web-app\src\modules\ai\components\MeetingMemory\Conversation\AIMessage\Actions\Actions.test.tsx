import { render } from '@testing-library/react';
import { useFeedback, useRefreshLastResponse } from '@waitroom/common';
import { faker } from '@waitroom/tests';
import { describe, expect, it } from 'vitest';
import { withAll } from '../../../../../../../tests/helpers/storeProviders';
import { Actions } from './Actions';

vi.mock('@waitroom/common', async (importOriginal) => {
  const mod = await importOriginal<typeof importOriginal>();
  return {
    ...mod,
    useFeedback: vi.fn().mockReturnValue({
      isLoading: false,
      feedback: undefined,
      thumbsDown: vi.fn(),
      thumbsUp: vi.fn(),
    }),
    useRefreshLastResponse: vi.fn().mockReturnValue({
      refreshLastResponse: vi.fn(),
      isLoading: false,
    }),
  };
});

describe('<Actions>', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render Actions component', () => {
    const message = faker.meetingMemory.message();
    const sources = faker.meetingMemory.sources();
    const { queryByTestId } = render(
      withAll(
        <Actions
          cacheId={faker.def.string.uuid()}
          threadId={faker.def.string.uuid()}
          message={message}
          sources={sources}
          isOpenSources={false}
          onToggleSources={vi.fn()}
          refresh={false}
          isStreaming={false}
          contentRef={null}
        />,
      ),
    );
    expect(queryByTestId('actions')).not.toBeNull();
  });

  it('should not render action buttons when message is streaming', () => {
    const message = faker.meetingMemory.message();
    const sources = faker.meetingMemory.sources();
    vi.mocked(useFeedback).mockReturnValue({
      isPending: 1,
      thumbsDown: vi.fn(),
      thumbsUp: vi.fn(),
    });

    vi.mocked(useRefreshLastResponse).mockReturnValue({
      isPending: true,
      refreshLastResponse: vi.fn(),
    });

    const { queryByTestId } = render(
      withAll(
        <Actions
          cacheId={faker.def.string.uuid()}
          threadId={faker.def.string.uuid()}
          message={message}
          sources={sources}
          isOpenSources={true}
          onToggleSources={vi.fn()}
          refresh={false}
          isStreaming={true}
          contentRef={null}
        />,
      ),
    );
    expect(queryByTestId('actions')).toBeNull();
  });

  it('should render action buttons when isOpenSources is true and message is not streaming', () => {
    const message = faker.meetingMemory.message();
    const sources = faker.meetingMemory.sources();
    const { queryAllByRole } = render(
      withAll(
        <Actions
          cacheId={faker.def.string.uuid()}
          threadId={faker.def.string.uuid()}
          message={message}
          sources={sources}
          isOpenSources={true}
          onToggleSources={vi.fn()}
          refresh={false}
          isStreaming={false}
          contentRef={null}
        />,
      ),
    );
    expect(queryAllByRole('button')).toHaveLength(5);
  });

  it('should render action buttons when feedback is loading and message is not streaming', () => {
    const message = faker.meetingMemory.message();
    const sources = faker.meetingMemory.sources();
    vi.mocked(useFeedback).mockReturnValue({
      isPending: 2,
      thumbsDown: vi.fn(),
      thumbsUp: vi.fn(),
    });
    const { queryAllByRole } = render(
      withAll(
        <Actions
          cacheId={faker.def.string.uuid()}
          threadId={faker.def.string.uuid()}
          message={message}
          sources={sources}
          isOpenSources={false}
          onToggleSources={vi.fn()}
          refresh={false}
          isStreaming={false}
          contentRef={null}
        />,
      ),
    );
    expect(queryAllByRole('button')).toHaveLength(5);
  });

  it('should render action buttons when refreshing last response and message is not streaming', () => {
    const message = faker.meetingMemory.message();
    const sources = faker.meetingMemory.sources();
    vi.mocked(useRefreshLastResponse).mockReturnValue({
      isPending: true,
      refreshLastResponse: vi.fn(),
    });
    const { queryAllByRole } = render(
      withAll(
        <Actions
          cacheId={faker.def.string.uuid()}
          threadId={faker.def.string.uuid()}
          message={message}
          sources={sources}
          isOpenSources={false}
          onToggleSources={vi.fn()}
          refresh={false}
          isStreaming={false}
          contentRef={null}
        />,
      ),
    );
    expect(queryAllByRole('button')).toHaveLength(5);
  });
});
