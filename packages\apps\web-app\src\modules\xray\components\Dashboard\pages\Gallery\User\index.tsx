import { Box, Flex, HStack, Link, Select, SimpleGrid, Text } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import CardLoader from '@core/components/Loader/Card/CardLoader';
import { faArrowDownWideShort, faBell, faCube } from '@fortawesome/pro-solid-svg-icons';
import { InfiniteData, UseInfiniteQueryResult } from '@tanstack/react-query';
import { DefaultApiResponse, XRayApiService } from '@waitroom/common-api';
import { config } from '@waitroom/config';
import { XRay, XRayTemplate, XRayType } from '@waitroom/models';
import { getInfinityRequestData } from '@waitroom/react-query';
import { repeat } from '@waitroom/react-utils';
import { toBoolean } from '@waitroom/utils';
import { ReactElement, useMemo, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { baseNs } from '../../../constants';
import DeleteConfirmModal from '../../../DeleteConfirmModal';
import XRayError from '../../../Error';
import XRayCard from '../Card';
import XRayCreateCard from '../Card/Create';
import XRayTemplatesLinkCard from '../Card/TemplatesLink';

const sorts = ['title', 'updated', 'created'] as const;

type Filters = {
  type?: XRay['type'];
  isActive?: boolean;
  sort?: (typeof sorts)[number];
};

export interface UserXRaysProps {
  query: UseInfiniteQueryResult<
    InfiniteData<XRayApiService.GetByUser['response'] | undefined>,
    DefaultApiResponse
  >;
  onTabChange: (index: number) => void;
}

const UserXRays = ({ query, onTabChange }: UserXRaysProps): ReactElement | null => {
  const { t } = useTranslation();
  const [deleteId, setDeleteId] = useState<number | undefined>(undefined);
  const [filters, setFilters] = useState<Filters>({
    isActive: true,
    sort: 'updated',
  });
  const xrays = getInfinityRequestData(query.data)?.xrays;
  const hasXRays = !!xrays?.length;

  const filteredXrays = useMemo(() => {
    if (!xrays?.length) return xrays;
    const f = xrays.filter((x) => {
      let is = true;
      if (filters.type) is = is && x.type === filters.type;
      if (filters.isActive !== undefined) is = is && x.isActive === filters.isActive;
      return is;
    });
    if (!filters.sort || filters.sort === 'title') {
      return f.sort((a, b) => a.title.localeCompare(b.title));
    } else if (filters.sort === 'updated') {
      return f.sort((a, b) => b.updatedAt - a.updatedAt);
    } else if (filters.sort === 'created') {
      return f.sort((a, b) => b.createdAt - a.createdAt);
    }
    return f;
  }, [xrays, filters]);

  return (
    <>
      {hasXRays ? (
        <Flex
          direction={{ base: 'column', md: 'row' }}
          gap={4}
          align={{ md: 'center' }}
          justify="space-between"
          mb={6}
        >
          <Flex
            w={'auto'}
            minW={0}
            direction={{ base: 'column', sm: 'row' }}
            align={{ base: 'flex-start', sm: 'center' }}
            gap={2}
          >
            <Text color="gray.300" fontSize="sm">
              {t('global.filters')}
            </Text>
            <Box position={'relative'} sx={{ select: { pl: 9 } }}>
              <Icon icon={faCube} color={'gray.400'} position={'absolute'} left={3} top={3} />
              <Select
                size="xs"
                minW="140px"
                placeholder={t('global.type')}
                title={t('global.type')}
                onChange={(e) =>
                  setFilters({ ...filters, type: e.target.value as XRayTemplate['type'] })
                }
                defaultValue={filters.type}
              >
                {Object.values(XRayType).map((type) => (
                  <option key={type} value={type}>
                    {t(`dashboard.xray.type.${type}.label`)}
                  </option>
                ))}
              </Select>
            </Box>
            <Box position={'relative'} sx={{ select: { pl: 9 } }}>
              <Icon icon={faBell} color={'gray.400'} position={'absolute'} left={3} top={3} />
              <Select
                size="xs"
                minW="120px"
                title="Status"
                textTransform={'capitalize'}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    isActive: toBoolean(e.target.value) as Filters['isActive'],
                  })
                }
                defaultValue={filters.isActive ? 1 : 0}
              >
                {[true, false].map((isActive) => (
                  <option key={String(isActive)} value={isActive ? 1 : 0}>
                    {t(isActive === true ? 'global.active' : 'global.disabled')}
                  </option>
                ))}
              </Select>
            </Box>
          </Flex>
          <HStack spacing={2}>
            <Text color="gray.300" fontSize="sm">
              {t('global.sortBy')}
            </Text>
            <Box position={'relative'} sx={{ select: { pl: 9 } }}>
              <Icon
                icon={faArrowDownWideShort}
                color={'gray.400'}
                position={'absolute'}
                left={3}
                top={3}
              />
              <Select
                size="xs"
                minW="150px"
                title={t('global.sortBy')}
                textTransform={'capitalize'}
                onChange={(e) =>
                  setFilters({ ...filters, sort: e.target.value as Filters['sort'] })
                }
                defaultValue={filters.sort}
              >
                {sorts.map((sort) => (
                  <option key={sort} value={sort}>
                    {t(`${baseNs}.sortBy.${sort}`)}
                  </option>
                ))}
              </Select>
            </Box>
          </HStack>
        </Flex>
      ) : (
        <Box mb={6} fontSize={'sm'}>
          <Trans
            i18nKey={`${baseNs}.exploreDescEmpty`}
            components={{
              help: <Link className="none" href={config.links.help} rel="noopener noreferrer" />,
            }}
          />
        </Box>
      )}
      <SimpleGrid columns={{ base: 1, md: 2, '2xl': 3 }} spacing={6}>
        <XRayCreateCard />
        {!hasXRays && <XRayTemplatesLinkCard onClick={() => onTabChange(1)} />}
        {query.isPending && !xrays?.length ? (
          <>{repeat(<CardLoader bg={'gray.900'} image={false} lines={5} />, 4)}</>
        ) : (
          filteredXrays?.map((xray) => (
            <XRayCard key={xray.id} data={xray} onDelete={() => setDeleteId(xray.id)} />
          ))
        )}
      </SimpleGrid>
      {query.isError && (
        <Box my={6}>
          <XRayError onRetry={query.refetch} isLoading={query.isRefetching}>
            {t(`${baseNs}.userXRaysError`)}
          </XRayError>
        </Box>
      )}
      <DeleteConfirmModal id={deleteId} onClose={() => setDeleteId(undefined)} />
    </>
  );
};
export default UserXRays;
