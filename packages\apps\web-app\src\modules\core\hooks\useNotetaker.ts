import { useQuery } from "@tanstack/react-query";
import { getRecallIntegrationConnections } from "../../integrations/hooks/useRecallIntegration";

export const useNotetaker = () => {
  const { data, isLoading } = useQuery(getRecallIntegrationConnections);
  const hasANotetakerConnected = !!data?.connections?.some(
    (connection) => connection.connected,
  );

  return {
    hasANotetakerConnected,
    isLoading,
    data,
  };
};