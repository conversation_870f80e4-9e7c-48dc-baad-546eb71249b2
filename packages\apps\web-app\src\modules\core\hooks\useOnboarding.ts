import { selectCurrentUserEmail, selectCurrentUserOnboarding, useAuthStore } from '@waitroom/auth';
import { useMemo } from 'react';
import { useAppStore } from '../store/store';

export const useOnboarding = () => {
  const intro = useAuthStore(selectCurrentUserOnboarding)?.[0];
  const hostsOnboarded = useAppStore.use.hostsOnboarded();
  const email = useAuthStore(selectCurrentUserEmail);
  const hasOnboarded = useMemo(() => {
    if (!hostsOnboarded) return false;
    return !!(email && hostsOnboarded[email]);
  }, [email, hostsOnboarded]);

  return {
    intro,
    hasOnboarded,
  };
};
