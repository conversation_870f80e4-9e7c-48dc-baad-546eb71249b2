import { withSpace } from '@waitroom/utils';
import { BASE_URL, CDN_URL, WORMHOLE_URL, environments } from '.';
import { envs } from '../services/envs';

const blobUrl = `blob: ${BASE_URL}`;
const preview = environments.isDev ? '*.encr.app' : '';
const base = `${WORMHOLE_URL} *.rumi.ai wss://*.rumi.ai *.waitroom.com wss://*.waitroom.com cdn.rumi.ai ${preview}`;
const cdnBase = `${withSpace(CDN_URL)}`;
const profiler = environments.isDev ? `localhost:* ws://localhost:*` : '';
const recordingsUrl = `${envs.VITE_S3_RECORDINGS}`;
const externalMedia = ``;

const defaultSrc = `default-src 'self'`;

const scriptSrc = `script-src 'self' 'unsafe-inline' 'unsafe-eval' data: tagmanager.google.com *.googletagmanager.com
 accounts.google.com app.link static.ads-twitter.com snap.licdn.com
 px.ads.linkedin.com cdn.paddle.com appleid.cdn-apple.com webforms.pipedrive.com
 kit.fontawesome.com connect.facebook.net js-na2.hs-analytics.net js-na2.hsadspixel.net js-na2.hscollectedforms.net
 js-na2.hs-banner.com www.creativeenterprisingbusiness.com ${blobUrl}`;

const ImgSrc = `img-src * 'self' data: https:`;
/* const ImgSrc = `img-src 'self' data: ${base}
 ${cdnBase} www.googletagmanager.com t.co
 analytics.twitter.com cdn.sanity.io logo.clearbit.com lh3.googleusercontent.com
 px.ads.linkedin.com waitroom-session-images.s3.amazonaws.com ${CDN_URL}
 avatars.slack-edge.com secure.gravatar.com *.imgur.com *.giphy.com *.discordapp.net *.slack.com
 *.imgbox.com *.postimg.cc *.tenor.com *.pinimg.com *.redd.it *.reddit.com *.tumblr.com *.media.tumblr.com
 *.twimg.com`; */

const mediaSrc = `media-src 'self' ${blobUrl} cdn.sanity.io
 ${recordingsUrl} ${cdnBase} ${externalMedia}`;

const frameSrc = `frame-src 'self' www.googletagmanager.com platform.linkedin.com
 *.paddle.com *.typeform.com td.doubleclick.net www.facebook.com connect.nango.dev`;

const connectSrc = `connect-src 'self' ${base} ${cdnBase} effectssdk.ai
 *.paddle.com *.google-analytics.com analytics.google.com *.analytics.google.com stats.g.doubleclick.net
 google.com www.google.com 6diaohap.api.sanity.io ka-p.fontawesome.com api-na2.hubapi.com *.hscollectedforms.net
 kit.fontawesome.com px.ads.linkedin.com *.ingest.sentry.io *.vidstack.io www.facebook.com
 ********* chat.stream-io-api.com wss://chat.stream-io-api.com api2.branch.io
 *.segment.com *.segment.io *.i.posthog.com us.i.posthog.com wss://*.nango.dev
 ${CDN_URL} *.production.livekit.cloud wss://*.production.livekit.cloud
 *.livekit.cloud *.livekit.io livekit.io wss://*.livekit.cloud
 calndr.link *.googleadservices.com connect.nango.dev r2.leadsy.ai snap.licdn.com ${profiler} ${externalMedia}`;

const styleSrc = `style-src 'self' 'unsafe-inline' *.paddle.com`;

const fontSrc = `font-src 'self' ka-p.fontawesome.com fonts.gstatic.com`;

const manifestSrc = `manifest-src 'self'`;

const scriptSrcElem = `script-src-elem 'self' ${base} accounts.google.com
 googleads.g.doubleclick.net appleid.cdn-apple.com snap.licdn.com r2.leadsy.ai
  *.segment.com *.segment.io *.gstatic.com
 app.link effectssdk.ai kit.fontawesome.com *.facebook.net
 *.paddle.com *.i.posthog.com us-assets.i.posthog.com ${blobUrl}
 *.livekit.io livekit.io`;

export const defaultSecurityPolicy = `${defaultSrc};${scriptSrc};${ImgSrc};${mediaSrc};
${frameSrc};${connectSrc};${scriptSrcElem};${styleSrc};${fontSrc};${manifestSrc};`;
