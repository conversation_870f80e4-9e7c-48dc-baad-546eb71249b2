import {
  Box,
  Container,
  DarkM<PERSON>,
  <PERSON>lex,
  Tab,
  <PERSON><PERSON><PERSON><PERSON>,
  TabPanel,
  TabPanels,
  Ta<PERSON>,
} from '@chakra-ui/react';
import { selectIsCurrentUserGuest, useAuthStore } from '@waitroom/auth';
import { memo, ReactElement, ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'react-router-dom';
import Header from '../Header';
import { tabHashMap } from './constants';
import EndedAnimation from './EndedAnimation/EndedAnimation';
import Feedback from './Feedback/Feedback';
import GuestAccess from './GuestAccess/GuestAccess';
import { ns } from './helpers';
import Info from './Info/Info';
import MeetingMemoryTab from './MeetingMemoryTab/MeetingMemoryTab';
import RecordingTab from './RecordingTab/RecordingTab';
import SummaryTab from './SummaryTab/SummaryTab';

export type EndedProps = {
  sidebar?: ReactNode;
  children?: ReactNode;
};

const tabSx = {
  px: [1.5, 3, 4],
  py: [3, 4],
  fontSize: ['sm', 'md'],
};

const Ended = memo(({ sidebar, children }: EndedProps): ReactElement | null => {
  const { t } = useTranslation();
  const isGuest = useAuthStore(selectIsCurrentUserGuest);
  const { hash } = useLocation();
  const idx = tabHashMap.indexOf(hash);

  return (
    <Box w={'full'}>
      <Header isEnded bg={undefined} />
      {isGuest ? (
        <GuestAccess />
      ) : (
        <>
          {sidebar}
          <Flex
            className={`p-sidebar`}
            position={'relative'}
            direction={'column'}
            w={'full'}
            zIndex={2}
          >
            <DarkMode>
              <Container
                maxW={'container.xl'}
                px={{ base: 4, sm: 6, md: 8 }}
                py={{ base: 8, xl: 12 }}
              >
                <Info />
                <div className={'tabs-wrapper'}>
                  <Tabs colorScheme={'orange'} index={idx === -1 ? 0 : idx}>
                    <TabList
                      className={'tabs-list'}
                      position={'sticky'}
                      zIndex={'sticky'}
                      bg={'gray.1000'}
                    >
                      <Tab
                        as={Link}
                        className="none"
                        to={tabHashMap[0]}
                        replace
                        sx={tabSx}
                        isTruncated
                        noOfLines={1}
                        title={t(`${ns}.askMeetingMemory`)}
                      >
                        {t(`${ns}.askMeetingMemory`)}
                      </Tab>
                      <Tab
                        as={Link}
                        className="none"
                        to={tabHashMap[1]}
                        replace
                        sx={tabSx}
                        title={t(`${ns}.summary`)}
                      >
                        {t(`${ns}.summary`)}
                      </Tab>
                      <Tab
                        as={Link}
                        className="none"
                        to={tabHashMap[2]}
                        replace
                        sx={tabSx}
                        isTruncated
                        noOfLines={1}
                        minW={[14, 'none']}
                        title={t(`${ns}.recording`)}
                      >
                        {t(`${ns}.recording`)}
                      </Tab>
                    </TabList>
                    <TabPanels>
                      <TabPanel py={6} px={0}>
                        <MeetingMemoryTab />
                      </TabPanel>
                      <TabPanel py={6} px={0}>
                        <SummaryTab />
                      </TabPanel>
                      <TabPanel py={6} px={0}>
                        <RecordingTab />
                      </TabPanel>
                    </TabPanels>
                  </Tabs>
                </div>
              </Container>
            </DarkMode>
          </Flex>
          <EndedAnimation />
        </>
      )}
      <Feedback />
      {children}
    </Box>
  );
});
export default Ended;
