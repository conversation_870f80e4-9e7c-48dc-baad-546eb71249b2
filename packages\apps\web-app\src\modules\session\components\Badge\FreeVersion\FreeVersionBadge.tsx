import { Badge, BadgeProps, Text } from '@chakra-ui/react';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';

export type FreeVersionBadgeProps = BadgeProps & {
  responsive?: boolean;
};

const FreeVersionBadge = ({ responsive, ...props }: FreeVersionBadgeProps): ReactElement | null => {
  const { t } = useTranslation();

  return (
    <Badge
      rounded="md"
      size="2xs"
      variant="outline"
      color="gray.400"
      textTransform="uppercase"
      px={2}
      py={1.5}
      cursor="default"
      {...props}
    >
      <Text as="span" display={responsive ? ['none', 'initial'] : undefined}>
        {t('global.freeVersion')}
      </Text>
      {!!responsive && (
        <Text as="span" display={['initial', 'none']}>
          F
        </Text>
      )}
    </Badge>
  );
};

export default FreeVersionBadge;
