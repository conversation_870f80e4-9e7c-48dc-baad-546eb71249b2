import { useDisclosure } from '@chakra-ui/react';
import { selectCurrentUserOnboarding, useAuthStore } from '@waitroom/auth';
import { useUpdateOnboarding } from '@waitroom/common';
import { User, userOnboardingChecklistValues } from '@waitroom/models';
import { getRequestData } from '@waitroom/react-query';
import { useEffect, useMemo, useState } from 'react';
import { updateAppState, useAppStore } from '../../../core/store/store';
import { useOnboardingChecklist } from '../../hooks/useOnboardingChecklist';

const checkCompleted = (
  list: typeof userOnboardingChecklistValues,
  onboarding: User['onboarding'],
) => {
  if (!onboarding) return 0;
  return list.filter((val) => !!onboarding[val]).length;
};

export const useComponent = () => {
  const modal = useDisclosure();
  const popover = useDisclosure();
  const onboarding = useAuthStore(selectCurrentUserOnboarding);
  const showChecklist = useAppStore.use.showChecklist();
  const [confetti, setConfetti] = useState(false);
  const { onOpen: onOpenPopover, onClose: onClosePopover } = popover;
  const { checklist, totalCount, uncompletedCount, isCompleted, progress } =
    useOnboardingChecklist();
  const {
    mutation: { mutate },
    isLoading,
  } = useUpdateOnboarding({
    onSuccess: (response) => {
      // confetti animation
      const { onboarding } = getRequestData(response)?.user || {};
      const isCompleted = !(totalCount - checkCompleted(checklist, onboarding));
      if (isCompleted) setConfetti(true);
    },
  });

  useEffect(() => {
    if (showChecklist && !isCompleted) onOpenPopover();
  }, [showChecklist, isCompleted, onOpenPopover]);

  useEffect(() => {
    // hide the popover if all checks are done
    if (isCompleted) onClosePopover();
  }, [isCompleted, onClosePopover]);

  const popoverMemoized = useMemo(
    () => ({
      ...popover,
      onClose: () => {
        updateAppState({ showChecklist: false });
        onClosePopover();
      },
    }),
    [onClosePopover, popover],
  );

  return {
    progress,
    checklist,
    userChecklist: onboarding,
    onCheck: mutate,
    isLoading,
    modal,
    popover: popoverMemoized,
    isCompleted,
    uncompletedCount,
    showChecklist,
    confetti,
  };
};
