# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [1.219.0](https://github.com/Waitroom/rumi.ai/compare/@waitroom/models@1.218.1...@waitroom/models@1.219.0) (2025-07-09)


### Features

* user onboarding updates ([#38](https://github.com/Waitroom/rumi.ai/issues/38)) ([7f9f8c7](https://github.com/Waitroom/rumi.ai/commit/7f9f8c7739fb7cc7d6213eb899bc56bbef55e39f))

## [1.218.1](https://github.com/Waitroom/rumi.ai/compare/@waitroom/models@1.218.0...@waitroom/models@1.218.1) (2025-07-04)


### Bug Fixes

* change user ids to string type ([#35](https://github.com/Waitroom/rumi.ai/issues/35)) ([7b6af65](https://github.com/Waitroom/rumi.ai/commit/7b6af652568304b6d10e9c25bcc568d833bad75a))

## [1.218.0](https://github.com/Waitroom/rumi.ai/compare/@waitroom/models@1.217.2...@waitroom/models@1.218.0) (2025-06-26)


### Features

*  personalized meeting memory suggestions ([#27](https://github.com/Waitroom/rumi.ai/issues/27)) ([779dae5](https://github.com/Waitroom/rumi.ai/commit/779dae5ef69a33200713f8f20fae6fd9333a471c))

## [1.217.2](https://github.com/Waitroom/rumi.ai/compare/@waitroom/models@1.217.1...@waitroom/models@1.217.2) (2025-06-19)


### Bug Fixes

* outlook bot integration text and icons ([#18](https://github.com/Waitroom/rumi.ai/issues/18)) ([efaee1d](https://github.com/Waitroom/rumi.ai/commit/efaee1dcb2b1554db055936593ad9ef1f57c6a76))

## [1.217.1](https://github.com/Waitroom/rumi.ai/compare/@waitroom/models@1.217.0...@waitroom/models@1.217.1) (2025-06-14)

## [1.217.0](https://github.com/Waitroom/rumi.ai/compare/@waitroom/models@1.216.3...@waitroom/models@1.217.0) (2025-06-13)


### Features

* recall calendar integrations ([#3](https://github.com/Waitroom/rumi.ai/issues/3)) ([fa3be19](https://github.com/Waitroom/rumi.ai/commit/fa3be190998b6d4016d18beca1a4e551c7ca9faf))

## [1.216.3](https://github.com/Waitroom/rumi.ai/compare/@waitroom/models@1.216.2...@waitroom/models@1.216.3) (2025-06-05)

## [1.216.2](https://github.com/Waitroom/rumi.ai/compare/@waitroom/models@1.216.1...@waitroom/models@1.216.2) (2025-06-02)

## [1.216.1](https://github.com/Waitroom/rumi.ai/compare/@waitroom/models@1.216.0...@waitroom/models@1.216.1) (2025-05-27)

## [1.216.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.215.8...@waitroom/models@1.216.0) (2025-05-21)


### Features

* complete some TODOs around the app ([#2708](https://github.com/Waitroom/waitroom/issues/2708)) ([23c590d](https://github.com/Waitroom/waitroom/commit/23c590d191e53f7f8c412cadf1e7d84c2f15afaf))

## [1.215.8](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.215.7...@waitroom/models@1.215.8) (2025-05-19)

## [1.215.7](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.215.6...@waitroom/models@1.215.7) (2025-05-19)


### Bug Fixes

* show only joined participants ([#2704](https://github.com/Waitroom/waitroom/issues/2704)) ([db401d4](https://github.com/Waitroom/waitroom/commit/db401d47a892f5b1591710cb6f91b509df97b48e))

## [1.215.6](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.215.5...@waitroom/models@1.215.6) (2025-05-19)


### Bug Fixes

* recordings link, remove test ai feed component ([#2703](https://github.com/Waitroom/waitroom/issues/2703)) ([fcb035b](https://github.com/Waitroom/waitroom/commit/fcb035bca8cd0771233e4f6b2ff908b1079ed6d9))

## [1.215.5](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.215.4...@waitroom/models@1.215.5) (2025-05-14)

## [1.215.4](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.215.3...@waitroom/models@1.215.4) (2025-05-14)

## [1.215.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.215.2...@waitroom/models@1.215.3) (2025-05-09)

## [1.215.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.215.1...@waitroom/models@1.215.2) (2025-05-07)

## [1.215.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.215.0...@waitroom/models@1.215.1) (2025-05-07)


### Bug Fixes

* mic not being initialized ([#2674](https://github.com/Waitroom/waitroom/issues/2674)) ([42940d9](https://github.com/Waitroom/waitroom/commit/42940d9b811f91dc42276c868fe83c0861e7ec34))

## [1.215.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.214.1...@waitroom/models@1.215.0) (2025-05-07)


### Features

* web 2590 onboarding update 33 optional show popup for existing users ([#2630](https://github.com/Waitroom/waitroom/issues/2630)) ([6b722de](https://github.com/Waitroom/waitroom/commit/6b722de59386b66e1161bebb8051919b8fac65f6))

## [1.214.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.214.0...@waitroom/models@1.214.1) (2025-05-06)


### Bug Fixes

* redirect issues, end summary loading, fix types ([b0a9549](https://github.com/Waitroom/waitroom/commit/b0a9549bfb3d90b889c4feda17d9900d67bdd1e3))

## [1.214.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.213.1...@waitroom/models@1.214.0) (2025-05-05)


### Features

* add general settings to the dashboard settings page ([#2670](https://github.com/Waitroom/waitroom/issues/2670)) ([390b64e](https://github.com/Waitroom/waitroom/commit/390b64e3aae9c761d8721bd17306037c40a67eed))

## [1.213.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.213.0...@waitroom/models@1.213.1) (2025-04-30)


### Bug Fixes

* player credentials, package versions ([8606cf0](https://github.com/Waitroom/waitroom/commit/8606cf0ae3be75b089ac8d155709ff705ba5be99))
* release versions ([3d66654](https://github.com/Waitroom/waitroom/commit/3d66654f6ca27fe13eb1a4fb7afc46f39da0d0fd))

## 1.213.0 (2025-04-30)

### Features

* lobbies access refactor WEB-2629 ([#2636](https://github.com/Waitroom/rumi.ai/issues/2636)) ([bb6f4ba](https://github.com/Waitroom/waitroom/commit/bb6f4bacd68cc00c49894855a6eaeb2428bfad5d))
* lobby branch libs updates ([#2600](https://github.com/Waitroom/rumi.ai/issues/2600)) ([6f121c6](https://github.com/Waitroom/waitroom/commit/6f121c628ef868e64b2c55f3bd782b1bbd7fbb87))
* meeting selector ([#2538](https://github.com/Waitroom/rumi.ai/issues/2538)) ([b7c072b](https://github.com/Waitroom/waitroom/commit/b7c072b2f57c92f6683e5938d33603257ba732eb))
* update listening mode models ([#2616](https://github.com/Waitroom/rumi.ai/issues/2616)) ([92a69ec](https://github.com/Waitroom/waitroom/commit/92a69ec6a9d649caa13a7dcf676b304ef4ccab69))
* update LobbyParticipant model ([#2640](https://github.com/Waitroom/rumi.ai/issues/2640)) ([ff616e1](https://github.com/Waitroom/waitroom/commit/ff616e13f584046983399d6ccb661f12e8cadc45))
* updated emoji reactions ([8ba8efe](https://github.com/Waitroom/waitroom/commit/8ba8efea5ea15c739ce28fbe8d66348650720496))
* web 2589 onboarding update 23 update getting started checklist ([#2623](https://github.com/Waitroom/rumi.ai/issues/2623)) ([644cc06](https://github.com/Waitroom/waitroom/commit/644cc06d71680982c2b0d199bdda0c5f50e39b94))


### Bug Fixes

* fixed missing plan config ([#2609](https://github.com/Waitroom/rumi.ai/issues/2609)) ([91b5872](https://github.com/Waitroom/waitroom/commit/91b58722b82df6d97a9ce8e3ada4a448a5c67dc7))

## [1.213.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.212.2...@waitroom/models@1.213.0) (2025-04-25)


### Features

* web 2589 onboarding update 23 update getting started checklist ([#2623](https://github.com/Waitroom/rumi.ai/issues/2623)) ([644cc06](https://github.com/Waitroom/waitroom/commit/644cc06d71680982c2b0d199bdda0c5f50e39b94))

## [1.212.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.212.1...@waitroom/models@1.212.2) (2025-04-25)

## [1.212.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.212.0...@waitroom/models@1.212.1) (2025-04-22)

## [1.212.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.211.0...@waitroom/models@1.212.0) (2025-04-18)


### Features

* update LobbyParticipant model ([#2640](https://github.com/Waitroom/rumi.ai/issues/2640)) ([ff616e1](https://github.com/Waitroom/waitroom/commit/ff616e13f584046983399d6ccb661f12e8cadc45))

## [1.211.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.210.0...@waitroom/models@1.211.0) (2025-04-17)


### Features

* lobbies access refactor WEB-2629 ([#2636](https://github.com/Waitroom/rumi.ai/issues/2636)) ([bb6f4ba](https://github.com/Waitroom/waitroom/commit/bb6f4bacd68cc00c49894855a6eaeb2428bfad5d))

## [1.210.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.209.0...@waitroom/models@1.210.0) (2025-04-10)


### Features

* updated emoji reactions ([8ba8efe](https://github.com/Waitroom/waitroom/commit/8ba8efea5ea15c739ce28fbe8d66348650720496))

## [1.209.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.208.1...@waitroom/models@1.209.0) (2025-04-10)


### Features

* update listening mode models ([#2616](https://github.com/Waitroom/rumi.ai/issues/2616)) ([92a69ec](https://github.com/Waitroom/waitroom/commit/92a69ec6a9d649caa13a7dcf676b304ef4ccab69))

## [1.208.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.208.0...@waitroom/models@1.208.1) (2025-04-07)


### Bug Fixes

* fixed missing plan config ([#2609](https://github.com/Waitroom/rumi.ai/issues/2609)) ([91b5872](https://github.com/Waitroom/waitroom/commit/91b58722b82df6d97a9ce8e3ada4a448a5c67dc7))

## [1.208.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.207.0...@waitroom/models@1.208.0) (2025-04-02)


### Features

* lobby branch libs updates ([#2600](https://github.com/Waitroom/rumi.ai/issues/2600)) ([6f121c6](https://github.com/Waitroom/waitroom/commit/6f121c628ef868e64b2c55f3bd782b1bbd7fbb87))

## [1.207.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.206.5...@waitroom/models@1.207.0) (2025-03-27)


### Features

* meeting selector ([#2538](https://github.com/Waitroom/rumi.ai/issues/2538)) ([b7c072b](https://github.com/Waitroom/waitroom/commit/b7c072b2f57c92f6683e5938d33603257ba732eb))

## [1.206.5](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.206.4...@waitroom/models@1.206.5) (2025-03-27)


### Bug Fixes

* crying emoji ([a4c7b5c](https://github.com/Waitroom/waitroom/commit/a4c7b5cdf0c3df42d18071d783ef068a5f08f9b7))

## [1.206.4](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.206.3...@waitroom/models@1.206.4) (2025-03-27)


### Bug Fixes

* possible undefined value on sanity images ([#2577](https://github.com/Waitroom/rumi.ai/issues/2577)) ([b6ab535](https://github.com/Waitroom/waitroom/commit/b6ab5355689503488f12a233c956e0c9171fad7c))

## [1.206.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.206.2...@waitroom/models@1.206.3) (2025-03-27)

## [1.206.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.206.1...@waitroom/models@1.206.2) (2025-03-19)

## [1.206.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.206.0...@waitroom/models@1.206.1) (2025-03-18)

## [1.206.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.205.0...@waitroom/models@1.206.0) (2025-03-18)


### Features

* sync lib changes for lobbies onto develop ([#2570](https://github.com/Waitroom/rumi.ai/issues/2570)) ([6f97dd2](https://github.com/Waitroom/waitroom/commit/6f97dd25fe605aca0a4626481dec170b32f286e8))

## [1.205.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.204.1...@waitroom/models@1.205.0) (2025-03-12)


### Features

* common lib updates for lobbies ([#2563](https://github.com/Waitroom/rumi.ai/issues/2563)) ([040cf91](https://github.com/Waitroom/waitroom/commit/040cf91058905392413fd6743c499a93008f7f09))

## [1.204.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.204.0...@waitroom/models@1.204.1) (2025-03-07)


### Bug Fixes

* recording: episode name ([d87bb1a](https://github.com/Waitroom/waitroom/commit/d87bb1ab5dada14d1b654e89824dcfe3f355eefa))

## [1.204.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.203.2...@waitroom/models@1.204.0) (2025-03-04)


### Features

* lobbies lib package updates ([#2540](https://github.com/Waitroom/rumi.ai/issues/2540)) ([106cebc](https://github.com/Waitroom/waitroom/commit/106cebc838ee1b4a95673638fbb51d62a11436ca))

## [1.203.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.203.1...@waitroom/models@1.203.2) (2025-02-27)


### Bug Fixes

* missing guest reactions ([#2532](https://github.com/Waitroom/rumi.ai/issues/2532)) ([6555488](https://github.com/Waitroom/waitroom/commit/655548883467d7fd2e2d2e2b2f57a14d60246de2))

## [1.203.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.203.0...@waitroom/models@1.203.1) (2025-02-25)


### Bug Fixes

* camera effect issue #WEB-2518 ([12b243d](https://github.com/Waitroom/waitroom/commit/12b243d8544033375febbe031cef12567d43c372)), closes [#WEB-2518](https://github.com/Waitroom/rumi.ai/issues/WEB-2518)

## [1.203.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.202.0...@waitroom/models@1.203.0) (2025-02-22)


### Features

* web 2470 improve ai feed performance ([#2476](https://github.com/Waitroom/rumi.ai/issues/2476)) ([c561e16](https://github.com/Waitroom/waitroom/commit/c561e1625dc69472234d62573ac45623c5af1bd2))

## [1.202.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.201.0...@waitroom/models@1.202.0) (2025-02-18)


### Features

* make investors heading editable ([#2510](https://github.com/Waitroom/rumi.ai/issues/2510)) ([cc96256](https://github.com/Waitroom/waitroom/commit/cc96256906c1feee99223e611719453d4f4765a7))

## [1.201.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.200.0...@waitroom/models@1.201.0) (2025-02-17)


### Features

* WEB-2484 add host controls for team memory visibility during meetings ([#2470](https://github.com/Waitroom/rumi.ai/issues/2470)) ([f7ae25f](https://github.com/Waitroom/waitroom/commit/f7ae25f58048413ee2a8d69b21382e770de4e95e))

## [1.200.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.199.3...@waitroom/models@1.200.0) (2025-02-17)


### Features

* update investors component, sanity studio ([#2496](https://github.com/Waitroom/rumi.ai/issues/2496)) ([d504012](https://github.com/Waitroom/waitroom/commit/d50401224d2552f70c06feb673b77498b1b32c8b))

## [1.199.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.199.2...@waitroom/models@1.199.3) (2025-02-17)

## [1.199.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.199.1...@waitroom/models@1.199.2) (2025-02-03)

## [1.199.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.199.0...@waitroom/models@1.199.1) (2025-02-03)

## [1.199.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.198.2...@waitroom/models@1.199.0) (2025-01-27)


### Features

* notetaker dashboard changes (past meetings list) ([#2458](https://github.com/Waitroom/rumi.ai/issues/2458)) ([f6f52db](https://github.com/Waitroom/waitroom/commit/f6f52db3538a786b6a5b4e5052f6207e4b6883b3))

## [1.198.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.198.1...@waitroom/models@1.198.2) (2025-01-27)


### Bug Fixes

* web 2462 presence on prescheduled screen is sometimes failing ([#2448](https://github.com/Waitroom/rumi.ai/issues/2448)) ([162c774](https://github.com/Waitroom/waitroom/commit/162c774e5ff506e701277f69c9ad47540a7f2e22))

## [1.198.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.198.0...@waitroom/models@1.198.1) (2025-01-23)


### Bug Fixes

* WEB-2477 auto focusing new users on checklist after intro ([#2450](https://github.com/Waitroom/rumi.ai/issues/2450)) ([674867f](https://github.com/Waitroom/waitroom/commit/674867f279e730458edf4b935f624967f2165ecd))

## [1.198.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.197.2...@waitroom/models@1.198.0) (2025-01-22)


### Features

* **notetaker:** Adds Notetaker UI Cards ([#2435](https://github.com/Waitroom/rumi.ai/issues/2435)) ([7255c07](https://github.com/Waitroom/waitroom/commit/7255c07db048a44f08cbf57f62f30426921390ce))

## [1.197.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.197.1...@waitroom/models@1.197.2) (2025-01-21)

## [1.197.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.197.0...@waitroom/models@1.197.1) (2025-01-20)


### Reverts

* "feat: web 2462 presence on prescheduled screen is sometimes failing ([#2445](https://github.com/Waitroom/rumi.ai/issues/2445))" ([e710c29](https://github.com/Waitroom/waitroom/commit/e710c29851de8231c421af0260f53122906fde8d))

## [1.197.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.196.0...@waitroom/models@1.197.0) (2025-01-20)


### Features

* web 2462 presence on prescheduled screen is sometimes failing ([#2445](https://github.com/Waitroom/rumi.ai/issues/2445)) ([5a49bb3](https://github.com/Waitroom/waitroom/commit/5a49bb35e62775ff470931dcac0e5fcfd3fa387b))

## [1.196.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.195.2...@waitroom/models@1.196.0) (2025-01-17)


### Features

* update livekit ([e6bc163](https://github.com/Waitroom/waitroom/commit/e6bc163ef1b5fdae4c3b3f350bb0adcd593c642b))

## [1.195.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.195.1...@waitroom/models@1.195.2) (2025-01-16)


### Bug Fixes

* onboarding display issue ([5b9df4a](https://github.com/Waitroom/waitroom/commit/5b9df4a0188fd3a4cc91088c44f43ebe0f5b7328))

## [1.195.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.195.0...@waitroom/models@1.195.1) (2025-01-16)


### Bug Fixes

* confetti explosion ([77a4c32](https://github.com/Waitroom/waitroom/commit/77a4c32ae261c8e8b502cbb581c6e424679e28a7))

## [1.195.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.194.3...@waitroom/models@1.195.0) (2025-01-16)


### Features

* meeting memory redesign, added container to sessions page ([17d63a5](https://github.com/Waitroom/waitroom/commit/17d63a5ab8ff65cf61a39dd5712a68894165d33a))

## [1.194.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.194.2...@waitroom/models@1.194.3) (2025-01-14)


### Reverts

* Revert "fix: race condition between onPatch and react-query queryFn promise resolve (#2444)" ([32fee7d](https://github.com/Waitroom/waitroom/commit/32fee7ddd6bc27bd3dca037ff96a8500217da07c)), closes [#2444](https://github.com/Waitroom/rumi.ai/issues/2444)

## [1.194.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.194.1...@waitroom/models@1.194.2) (2025-01-14)


### Bug Fixes

* race condition between onPatch and react-query queryFn promise resolve ([#2444](https://github.com/Waitroom/rumi.ai/issues/2444)) ([d82e57d](https://github.com/Waitroom/waitroom/commit/d82e57db9cb9563df7b2be4aefe3939b1d031d8d))

## [1.194.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.194.0...@waitroom/models@1.194.1) (2025-01-08)


### Bug Fixes

* modify ai-stream request payload ([#2437](https://github.com/Waitroom/rumi.ai/issues/2437)) ([25109c5](https://github.com/Waitroom/waitroom/commit/25109c52aa2ec2286b3da88e867fe53da81744c8))

## [1.194.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.193.1...@waitroom/models@1.194.0) (2024-12-19)


### Features

* added more info to stats panel ([d6206c3](https://github.com/Waitroom/waitroom/commit/d6206c3b35a5bf9f6e157971502d875fdd0d1caf))

## [1.193.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.193.0...@waitroom/models@1.193.1) (2024-12-18)

## [1.193.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.192.2...@waitroom/models@1.193.0) (2024-12-17)


### Features

* ai feed visibility ([3355232](https://github.com/Waitroom/waitroom/commit/3355232eeea7cabc78ac2e6ab1bed51f65970ef5))

## [1.192.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.192.1...@waitroom/models@1.192.2) (2024-12-11)

## [1.192.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.192.0...@waitroom/models@1.192.1) (2024-12-09)

## [1.192.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.191.0...@waitroom/models@1.192.0) (2024-12-06)


### Features

* updated effects UI ([#2402](https://github.com/Waitroom/rumi.ai/issues/2402)) ([aa72e48](https://github.com/Waitroom/waitroom/commit/aa72e488ded21b4025f7c8b103d87b3552e09baf))

## [1.191.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.190.3...@waitroom/models@1.191.0) (2024-12-05)


### Features

* web 2348 update pricing table to include crm ([176d9e1](https://github.com/Waitroom/waitroom/commit/176d9e11a6ab30aae9e25c4115ef93d6ae594571))

## [1.190.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.190.2...@waitroom/models@1.190.3) (2024-12-04)

## [1.190.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.190.1...@waitroom/models@1.190.2) (2024-12-04)

## [1.190.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.190.0...@waitroom/models@1.190.1) (2024-12-03)

## [1.190.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.189.2...@waitroom/models@1.190.0) (2024-12-03)


### Features

* improvements to MM on end screen and recordings screen ([#2382](https://github.com/Waitroom/rumi.ai/issues/2382)) ([6559beb](https://github.com/Waitroom/waitroom/commit/6559bebb791719067418d683b05b62674cae3144))

## [1.189.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.189.1...@waitroom/models@1.189.2) (2024-12-02)

## [1.189.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.189.0...@waitroom/models@1.189.1) (2024-12-02)


### Bug Fixes

* undefined track check, refactored cam and mic init, updated packages ([81fb7c1](https://github.com/Waitroom/waitroom/commit/81fb7c1bc7ce6e713272da1632bf86faea0e849c))

## [1.189.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.188.0...@waitroom/models@1.189.0) (2024-11-28)


### Features

* effects sdk ([#2372](https://github.com/Waitroom/rumi.ai/issues/2372)) ([fe8a8fe](https://github.com/Waitroom/waitroom/commit/fe8a8fe237f79bd663980c6c8fdbb937858e8d1a))

## [1.188.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.187.3...@waitroom/models@1.188.0) (2024-11-25)


### Features

* cms footer menu ([e80c80f](https://github.com/Waitroom/waitroom/commit/e80c80f75dcd08eca2a90a4c1a5a535a533424fe))


### Bug Fixes

* missing types ([703539c](https://github.com/Waitroom/waitroom/commit/703539c8d826f4e1a0cf490de029143f42433fc7))

## [1.187.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.187.2...@waitroom/models@1.187.3) (2024-11-20)


### Bug Fixes

* showing recordings icon on dashboard session listing ([#2350](https://github.com/Waitroom/rumi.ai/issues/2350)) ([16232f9](https://github.com/Waitroom/waitroom/commit/16232f95dd06666301b9aaf8a2a2ee940d8872a2))

## [1.187.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.187.1...@waitroom/models@1.187.2) (2024-11-14)


### Bug Fixes

* added integration provider workspace ([#2347](https://github.com/Waitroom/rumi.ai/issues/2347)) ([b6b71fa](https://github.com/Waitroom/waitroom/commit/b6b71fa9cae1b04c603b8ebdfa1f408dea2b0e62))

## [1.187.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.187.0...@waitroom/models@1.187.1) (2024-11-14)


### Bug Fixes

* improve block kit UX ([#2346](https://github.com/Waitroom/rumi.ai/issues/2346)) ([10f173e](https://github.com/Waitroom/waitroom/commit/10f173ef8ed5a7d652c4c8677c367ded5bb250a0))

## [1.187.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.186.0...@waitroom/models@1.187.0) (2024-11-12)


### Features

* crm contact cards ([#2246](https://github.com/Waitroom/rumi.ai/issues/2246)) ([c18da67](https://github.com/Waitroom/waitroom/commit/c18da675545e2a30c18a1b5d432270d29f2dea8a))

## [1.186.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.185.0...@waitroom/models@1.186.0) (2024-11-07)


### Features

* text to speech on dev ([9b7ea4c](https://github.com/Waitroom/waitroom/commit/9b7ea4cb9731cee22483ce845fef24f2b988fcea))

## [1.185.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.184.2...@waitroom/models@1.185.0) (2024-11-05)


### Features

* **subscriptions:** add support for 30 day trial ([#2328](https://github.com/Waitroom/rumi.ai/issues/2328)) ([a20e0ad](https://github.com/Waitroom/waitroom/commit/a20e0ad212ed9fc76395e94ea951982826a8e30b))

## [1.184.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.184.1...@waitroom/models@1.184.2) (2024-11-01)

## [1.184.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.184.0...@waitroom/models@1.184.1) (2024-11-01)

## [1.184.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.183.1...@waitroom/models@1.184.0) (2024-10-24)


### Features

* team support  ([#2223](https://github.com/Waitroom/rumi.ai/issues/2223)) ([a80b450](https://github.com/Waitroom/waitroom/commit/a80b450dca98cfa78c60634dec5eea631538a7d9))

## [1.183.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.183.0...@waitroom/models@1.183.1) (2024-10-23)


### Bug Fixes

* added hubspot meeting data ([#2301](https://github.com/Waitroom/rumi.ai/issues/2301)) ([9366aea](https://github.com/Waitroom/waitroom/commit/9366aea741cdd088ce44ee1a12c991a424619927))

## [1.183.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.182.0...@waitroom/models@1.183.0) (2024-10-22)


### Features

* rerelease packages ([573ab7c](https://github.com/Waitroom/waitroom/commit/573ab7c131b0f5c9dab2872e706ae7d677e46d60))

## [1.182.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.181.4...@waitroom/models@1.182.0) (2024-10-22)


### Features

* rerelease all packages ([ec0b06c](https://github.com/Waitroom/waitroom/commit/ec0b06cd0e5795a9bfa3f19bc6e1c2975975b346))

## [1.181.4](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.181.3...@waitroom/models@1.181.4) (2024-10-22)

## [1.181.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.181.2...@waitroom/models@1.181.3) (2024-10-22)


### Bug Fixes

* add state and code challenge params to auth requests ([33ce9ec](https://github.com/Waitroom/waitroom/commit/33ce9ecf8d684c0ff32d2cb6deaff59ca347d234))

## [1.181.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.181.1...@waitroom/models@1.181.2) (2024-10-21)


### Bug Fixes

* session timer ([#2294](https://github.com/Waitroom/rumi.ai/issues/2294)) ([6ab2336](https://github.com/Waitroom/waitroom/commit/6ab2336ff18648635dd2959dbcb6d128be9d8b8b))

## [1.181.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.181.0...@waitroom/models@1.181.1) (2024-10-21)


### Bug Fixes

* request approval cache updating ([#2291](https://github.com/Waitroom/rumi.ai/issues/2291)) ([6e5ec89](https://github.com/Waitroom/waitroom/commit/6e5ec898d00c372f836fbd65a9a2d9ee047ee528))

## [1.181.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.180.0...@waitroom/models@1.181.0) (2024-10-21)


### Features

* listening mode models ([#2292](https://github.com/Waitroom/rumi.ai/issues/2292)) ([6a4fb83](https://github.com/Waitroom/waitroom/commit/6a4fb83ab0f1efd7684848bd561d742e5e34e486))

## [1.180.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.179.0...@waitroom/models@1.180.0) (2024-10-21)


### Features

* meeting memory threads UI update ([#2284](https://github.com/Waitroom/rumi.ai/issues/2284)) ([07ba86a](https://github.com/Waitroom/waitroom/commit/07ba86afde2a70bfa173bd12a56927109ec21e94))
* reenable personalized suggestions ([#2280](https://github.com/Waitroom/rumi.ai/issues/2280)) ([70884fd](https://github.com/Waitroom/waitroom/commit/70884fd5b13c788bf5ad8fe64f0d50cf210c43bf))

## [1.179.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.178.2...@waitroom/models@1.179.0) (2024-10-10)


### Features

* cms menu external links ([9f1a923](https://github.com/Waitroom/waitroom/commit/9f1a9238eca506e1725ba33f5a14f1d3f3dece33))

## [1.178.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.178.1...@waitroom/models@1.178.2) (2024-10-10)

## [1.178.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.178.0...@waitroom/models@1.178.1) (2024-10-07)


### Bug Fixes

* improve local participant hook logic ([5419fca](https://github.com/Waitroom/waitroom/commit/5419fca02af22b18a1d4157983e9458d272a3803))
* session saving ([2f411e2](https://github.com/Waitroom/waitroom/commit/2f411e27576ee9e2c971a97e3ffdb61ae51b42d2))

## [1.178.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.177.1...@waitroom/models@1.178.0) (2024-10-03)


### Features

* adds new inactive session err code ([#2248](https://github.com/Waitroom/rumi.ai/issues/2248)) ([b226b8b](https://github.com/Waitroom/waitroom/commit/b226b8bc20c1dd4c001bdf8617740d19662487c6))

## [1.177.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.177.0...@waitroom/models@1.177.1) (2024-10-03)

## [1.177.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.176.0...@waitroom/models@1.177.0) (2024-10-01)


### Features

* project frictionless guest access ([#2233](https://github.com/Waitroom/rumi.ai/issues/2233)) ([948ddca](https://github.com/Waitroom/waitroom/commit/948ddca1047ed6394c9b5744bd4a4e3d4295636d))

## [1.176.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.175.0...@waitroom/models@1.176.0) (2024-10-01)


### Features

* web 2177 allow users to browse their previous meeting memory threads ([#2224](https://github.com/Waitroom/rumi.ai/issues/2224)) ([9a3c11d](https://github.com/Waitroom/waitroom/commit/9a3c11dcf18a73785608dbd9c09d1927399bf917))

## [1.175.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.174.0...@waitroom/models@1.175.0) (2024-09-27)


### Features

* listening mode meeting type ([#2242](https://github.com/Waitroom/rumi.ai/issues/2242)) ([8bdf499](https://github.com/Waitroom/waitroom/commit/8bdf499b20ffd127db98c3242ce1a4665e1c2964))

## [1.174.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.173.1...@waitroom/models@1.174.0) (2024-09-18)


### Features

* add company deails for hubspot deals ([#2235](https://github.com/Waitroom/rumi.ai/issues/2235)) ([c6d3f7d](https://github.com/Waitroom/waitroom/commit/c6d3f7d7667e86be8c46aad2ac79abca107a11bf))

## [1.173.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.173.0...@waitroom/models@1.173.1) (2024-09-13)


### Bug Fixes

* hubspot data models ([#2230](https://github.com/Waitroom/rumi.ai/issues/2230)) ([b99861e](https://github.com/Waitroom/waitroom/commit/b99861e0d24fac597742c44fd34eed0947e4b388))

## [1.173.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.172.1...@waitroom/models@1.173.0) (2024-09-11)


### Features

* WEB-2183 create new components for hubspot integration ([#2205](https://github.com/Waitroom/rumi.ai/issues/2205)) ([6636529](https://github.com/Waitroom/waitroom/commit/663652973dfd117ab9bf8d728830181ee498512b))

## [1.172.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.172.0...@waitroom/models@1.172.1) (2024-09-10)

## [1.172.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.171.3...@waitroom/models@1.172.0) (2024-09-09)


### Features

* added faq title ([8772335](https://github.com/Waitroom/waitroom/commit/8772335273abde475a124123042d0e1acd245ec5))

## [1.171.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.171.2...@waitroom/models@1.171.3) (2024-09-06)


### Bug Fixes

* cms grid item icon, sanity update ([d901dd7](https://github.com/Waitroom/waitroom/commit/d901dd7372533303103cbb5c2ab968de40a1d211))

## [1.171.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.171.1...@waitroom/models@1.171.2) (2024-09-04)


### Bug Fixes

* dev tools services list ([25cfac4](https://github.com/Waitroom/waitroom/commit/25cfac4f4cbfdaa1a758b3ad5e6bcec151860438))

## [1.171.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.171.0...@waitroom/models@1.171.1) (2024-09-04)


### Bug Fixes

* braid models, updated logger ([0d7d3a7](https://github.com/Waitroom/waitroom/commit/0d7d3a722379da5a7f0bfba2b25513b20be9a078))

## [1.171.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.170.1...@waitroom/models@1.171.0) (2024-09-04)


### Features

* adds peermetric SDK to Livekit room ([#2197](https://github.com/Waitroom/rumi.ai/issues/2197)) ([5411842](https://github.com/Waitroom/waitroom/commit/541184245d04426e527ba53d2c7f4f6eeb9d0d08))

## [1.170.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.170.0...@waitroom/models@1.170.1) (2024-09-03)


### Reverts

* braid service change ([a9501d9](https://github.com/Waitroom/waitroom/commit/a9501d9b2fff992c9ec4afb36a97a73310f7b7db))

## [1.170.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.169.0...@waitroom/models@1.170.0) (2024-09-03)


### Features

* personalized suggestions ([#2201](https://github.com/Waitroom/rumi.ai/issues/2201)) ([10fd13d](https://github.com/Waitroom/waitroom/commit/10fd13dd7afdece1feeb56b0b30f0a75949bb8d9))

## [1.169.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.168.0...@waitroom/models@1.169.0) (2024-09-02)


### Features

* added basic hubspot data models and api endpoints ([#2179](https://github.com/Waitroom/rumi.ai/issues/2179)) ([d01688a](https://github.com/Waitroom/waitroom/commit/d01688a3479382981a3f9add867d1f2d327923bc))

## [1.168.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.167.0...@waitroom/models@1.168.0) (2024-09-02)


### Features

* replace add event with calndr ([bea5188](https://github.com/Waitroom/waitroom/commit/bea5188e41a364535aebdff76c1be66376d2983c))

## [1.167.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.166.0...@waitroom/models@1.167.0) (2024-08-29)


### Features

* meeting memory enabled, pricing ui update ([#2189](https://github.com/Waitroom/rumi.ai/issues/2189)) ([228f5ee](https://github.com/Waitroom/waitroom/commit/228f5eea2bae30fae9a4248faebde1e87312c9f1))

## [1.166.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.165.1...@waitroom/models@1.166.0) (2024-08-26)


### Features

* loading state ([#2160](https://github.com/Waitroom/rumi.ai/issues/2160)) ([10841f9](https://github.com/Waitroom/waitroom/commit/10841f9811b8cf4973e183a9ae7a31fb9dc039b2))

## [1.165.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.165.0...@waitroom/models@1.165.1) (2024-08-23)


### Bug Fixes

* listing contacts ([#2174](https://github.com/Waitroom/rumi.ai/issues/2174)) ([b6c3ba7](https://github.com/Waitroom/waitroom/commit/b6c3ba779ee2bc225371297a8f8f56218173522d))

## [1.165.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.164.0...@waitroom/models@1.165.0) (2024-08-22)


### Features

* web-2148 bug fixes for salesforce integration ([#2172](https://github.com/Waitroom/rumi.ai/issues/2172)) ([7ea2e2a](https://github.com/Waitroom/waitroom/commit/7ea2e2a808fc2d33c649984e5842c0bfc41ca2f1))

## [1.164.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.163.0...@waitroom/models@1.164.0) (2024-08-21)


### Features

* WEB-2147 update formatting for numbers ([#2169](https://github.com/Waitroom/rumi.ai/issues/2169)) ([5742fd5](https://github.com/Waitroom/waitroom/commit/5742fd5f688dcaeb22a0002b814be330e5b3eff7))

## [1.163.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.162.1...@waitroom/models@1.163.0) (2024-08-21)


### Features

* WEB-2073 sales force button ([#2147](https://github.com/Waitroom/rumi.ai/issues/2147)) ([bd5d3b4](https://github.com/Waitroom/waitroom/commit/bd5d3b4b8256b9aa8971f9643dc0665293508bd6))


### Bug Fixes

* trigger release ([7c0b515](https://github.com/Waitroom/waitroom/commit/7c0b515f9722bf90b2a36efa1bae0f463264fa72))

## [1.162.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.162.0...@waitroom/models@1.162.1) (2024-08-20)

## [1.162.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.161.2...@waitroom/models@1.162.0) (2024-08-19)


### Features

* isAudioDiarized is Session Model ([#2159](https://github.com/Waitroom/rumi.ai/issues/2159)) ([b7baa4f](https://github.com/Waitroom/waitroom/commit/b7baa4fba481f6ff07e24e502aa1babe106d4ce1))

## [1.161.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.161.1...@waitroom/models@1.161.2) (2024-08-17)

## [1.161.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.161.0...@waitroom/models@1.161.1) (2024-08-15)


### Bug Fixes

* tests ([07ecc97](https://github.com/Waitroom/waitroom/commit/07ecc97c81ab4a999e2caf21227f1febee530dfd))

## [1.161.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.160.0...@waitroom/models@1.161.0) (2024-08-14)


### Features

* mm streaming ([#2148](https://github.com/Waitroom/rumi.ai/issues/2148)) ([5943da9](https://github.com/Waitroom/waitroom/commit/5943da912d1708a15cf8b45a48913f1d2d952d7d))

## [1.160.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.159.5...@waitroom/models@1.160.0) (2024-08-14)


### Features

* quality selector ([49ce726](https://github.com/Waitroom/waitroom/commit/49ce7267f2d2e36fd53d79eab8e7c300cdc4d59d))

## [1.159.5](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.159.4...@waitroom/models@1.159.5) (2024-08-02)

## [1.159.4](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.159.3...@waitroom/models@1.159.4) (2024-08-02)


### Bug Fixes

* ai event logging ([ca12060](https://github.com/Waitroom/waitroom/commit/ca12060fb2c30c52b83d4c068927d13f4f225e60))

## [1.159.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.159.2...@waitroom/models@1.159.3) (2024-07-31)

## [1.159.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.159.1...@waitroom/models@1.159.2) (2024-07-25)


### Bug Fixes

* ux meeting memory ([#2122](https://github.com/Waitroom/rumi.ai/issues/2122)) ([6aabf4a](https://github.com/Waitroom/waitroom/commit/6aabf4a585d4c451af78da1fc01e605a2fa316a0))

## [1.159.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.159.0...@waitroom/models@1.159.1) (2024-07-24)


### Bug Fixes

* import issue ([0f89549](https://github.com/Waitroom/waitroom/commit/0f8954914924db9d1b2ccc7c1cb9649cda2e488b))

## [1.159.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.158.2...@waitroom/models@1.159.0) (2024-07-24)


### Features

* setTimeout instead of setInterval ([#2128](https://github.com/Waitroom/rumi.ai/issues/2128)) ([9aab675](https://github.com/Waitroom/waitroom/commit/9aab6754587a46bab69dcb95b0a83286c04fc911))

## [1.158.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.158.1...@waitroom/models@1.158.2) (2024-07-22)


### Bug Fixes

* auth responses ([b6232d4](https://github.com/Waitroom/waitroom/commit/b6232d4280c26edd14d67533639248bc4e0d8410))

## [1.158.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.158.0...@waitroom/models@1.158.1) (2024-07-22)

## [1.158.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.157.1...@waitroom/models@1.158.0) (2024-07-19)


### Features

* braid query cancellation ([fe02cd9](https://github.com/Waitroom/waitroom/commit/fe02cd96621a6880a5375806ae57df7e4c024f94))

## [1.157.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.157.0...@waitroom/models@1.157.1) (2024-07-18)

## [1.157.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.156.0...@waitroom/models@1.157.0) (2024-07-10)


### Features

* WEB-2013 render ai response ([#2117](https://github.com/Waitroom/rumi.ai/issues/2117)) ([2301be6](https://github.com/Waitroom/waitroom/commit/2301be6b2d0e479cb010f84bba9ee2893036c0b5))

## [1.156.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.155.1...@waitroom/models@1.156.0) (2024-07-09)


### Features

* braid client ([ff4075c](https://github.com/Waitroom/waitroom/commit/ff4075ca2f73061c25828897c5b93ed1bd58132a))
* braid package ([910b7fd](https://github.com/Waitroom/waitroom/commit/910b7fd09832b55e91b0e87afdf80324844d4821))
* fixes ([1879340](https://github.com/Waitroom/waitroom/commit/187934096c622c93c7a514daadcf8efd715b93ac))
* participant metadata ([1e57304](https://github.com/Waitroom/waitroom/commit/1e5730404f04b98590ec9204befa1afa5989a334))
* release ([c28a08e](https://github.com/Waitroom/waitroom/commit/c28a08e7e8a552fb66b34d907a0ab7a6c99c8dcd))
* request access ([cd0ee0c](https://github.com/Waitroom/waitroom/commit/cd0ee0c0741470db2726eebaf104f096e79b9fdb))
* web-2011 meeting memory landing page ([#2115](https://github.com/Waitroom/rumi.ai/issues/2115)) ([e86833f](https://github.com/Waitroom/waitroom/commit/e86833f075ef0308ef4840d43cea5dfa6bc989d7))

## [1.155.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.155.0...@waitroom/models@1.155.1) (2024-07-09)

## [1.155.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.154.1...@waitroom/models@1.155.0) (2024-07-08)


### Features

* add components for meeting memory feature ([#2114](https://github.com/Waitroom/rumi.ai/issues/2114)) ([bad0ef6](https://github.com/Waitroom/waitroom/commit/bad0ef6117d90dee5f78ef12c0d6fbf5ad782d1d))

## [1.154.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.154.0...@waitroom/models@1.154.1) (2024-07-02)


### Bug Fixes

* added general as the default user meeting type ([#2106](https://github.com/Waitroom/rumi.ai/issues/2106)) ([a3d9ab9](https://github.com/Waitroom/waitroom/commit/a3d9ab9ff0c489a84941d55b4a82c6767cba9ac6))

## [1.154.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.153.0...@waitroom/models@1.154.0) (2024-06-27)


### Features

* onboarding popovers ([#2097](https://github.com/Waitroom/rumi.ai/issues/2097)) ([11308cb](https://github.com/Waitroom/waitroom/commit/11308cb7f23fd825c1e85eda8a34f97f3c6e06a2))

## [1.153.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.152.0...@waitroom/models@1.153.0) (2024-06-17)
## [1.151.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.150.0...@waitroom/models@1.151.0) (2024-06-14)


### Features

* braid client ([ff4075c](https://github.com/Waitroom/waitroom/commit/ff4075ca2f73061c25828897c5b93ed1bd58132a))
* braid package ([910b7fd](https://github.com/Waitroom/waitroom/commit/910b7fd09832b55e91b0e87afdf80324844d4821))

* WEB-1951 add confetti animation ([#2088](https://github.com/Waitroom/rumi.ai/issues/2088)) ([b0400c3](https://github.com/Waitroom/waitroom/commit/b0400c39f925005ad7105777400e52a007e98611))

## [1.152.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.151.0...@waitroom/models@1.152.0) (2024-06-17)


### Features

* onboarding checklist ([#2084](https://github.com/Waitroom/rumi.ai/issues/2084)) ([ad2a4be](https://github.com/Waitroom/waitroom/commit/ad2a4beb38296db3d84f5baea912fb8470e8bd7e))

## [1.151.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.150.0...@waitroom/models@1.151.0) (2024-06-13)


### Features

* host onboarding journey ([#2083](https://github.com/Waitroom/rumi.ai/issues/2083)) ([41cc060](https://github.com/Waitroom/waitroom/commit/41cc0603b373a2e152c76f5e87b970b909c1355f))

## [1.150.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.149.2...@waitroom/models@1.150.0) (2024-06-11)


### Features

* dashboard sessions listing update ([#2076](https://github.com/Waitroom/rumi.ai/issues/2076)) ([327a7e3](https://github.com/Waitroom/waitroom/commit/327a7e321e018118e9063a113c11f00f840c216c))

## [1.149.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.149.1...@waitroom/models@1.149.2) (2024-06-07)

## [1.149.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.149.0...@waitroom/models@1.149.1) (2024-05-31)

## [1.149.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.148.1...@waitroom/models@1.149.0) (2024-05-31)


### Features

* request user delete ([#2071](https://github.com/Waitroom/rumi.ai/issues/2071)) ([711e3f0](https://github.com/Waitroom/waitroom/commit/711e3f0e4a27aa6f41bea9c85ef95a695ad3e735))

## [1.148.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.148.0...@waitroom/models@1.148.1) (2024-05-30)


### Bug Fixes

* off the record loading ([0acc0e6](https://github.com/Waitroom/waitroom/commit/0acc0e602ce111f806ba3e608f6d431adef6dd1f))

## [1.148.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.147.4...@waitroom/models@1.148.0) (2024-05-21)


### Features

* migrate trixta OTR toggle from trixta to elio ([#2054](https://github.com/Waitroom/rumi.ai/issues/2054)) ([ae56d71](https://github.com/Waitroom/waitroom/commit/ae56d71f578dfc51452f5506a730b36f96433ba2))

## [1.147.4](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.147.3...@waitroom/models@1.147.4) (2024-05-15)


### Bug Fixes

* host page, refactor browser service ([6426b6d](https://github.com/Waitroom/waitroom/commit/6426b6d9ff10440b0358003f3dadf1eb068dc0ac))

## [1.147.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.147.2...@waitroom/models@1.147.3) (2024-05-09)

## [1.147.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.147.1...@waitroom/models@1.147.2) (2024-05-08)

## [1.147.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.147.0...@waitroom/models@1.147.1) (2024-04-30)

## [1.147.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.146.0...@waitroom/models@1.147.0) (2024-04-18)


### Features

* sanity update ([4da4d60](https://github.com/Waitroom/waitroom/commit/4da4d60c420e03daf095501dd6bfbbc11c4a0978))

## [1.146.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.145.0...@waitroom/models@1.146.0) (2024-03-28)


### Features

* WEB-1630 add pss email sending recipient rules to session settings ([#1987](https://github.com/Waitroom/rumi.ai/issues/1987)) ([92a2768](https://github.com/Waitroom/waitroom/commit/92a2768629efbf36e37ff43f96212a4574645901))

## [1.145.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.144.0...@waitroom/models@1.145.0) (2024-03-26)


### Features

* WEB-1685 implement the satisfaction survey on the end screen ([#1976](https://github.com/Waitroom/rumi.ai/issues/1976)) ([a16d6a5](https://github.com/Waitroom/waitroom/commit/a16d6a51123eba45e76a7ff666413cea8520806e))

## [1.144.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.143.0...@waitroom/models@1.144.0) (2024-03-19)


### Features

* added feature flag to control userMeetingType ([#1968](https://github.com/Waitroom/rumi.ai/issues/1968)) ([7860242](https://github.com/Waitroom/waitroom/commit/78602424f47a9eac16ea0ce7ef323dc1025bc69d))

## [1.143.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.142.3...@waitroom/models@1.143.0) (2024-03-19)


### Features

* userMeetingType implementation ([#1936](https://github.com/Waitroom/rumi.ai/issues/1936)) ([01b5e4b](https://github.com/Waitroom/waitroom/commit/01b5e4ba58777091e682920676df497c1f50afd6))

## [1.142.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.142.2...@waitroom/models@1.142.3) (2024-03-14)


### Bug Fixes

* release tags ([b9c0c0d](https://github.com/Waitroom/waitroom/commit/b9c0c0d8c2958a650fe6b27de55d73de9085b1cd))

## [1.142.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.142.1...@waitroom/models@1.142.2) (2024-03-14)

## [1.142.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.142.0...@waitroom/models@1.142.1) (2024-03-11)


### Bug Fixes

* screen share for guests ([8e1cc97](https://github.com/Waitroom/waitroom/commit/8e1cc97c0a8f63f7f964ce64fd6760ad0ab63aa5))

## [1.142.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.141.4...@waitroom/models@1.142.0) (2024-03-04)


### Features

* host onboarding questionnaire UI ([#1942](https://github.com/Waitroom/rumi.ai/issues/1942)) ([17b2af3](https://github.com/Waitroom/waitroom/commit/17b2af353a22bd77cefe13b256e26eb9c93a7e1e))

## [1.141.4](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.141.3...@waitroom/models@1.141.4) (2024-03-01)

## [1.141.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.141.2...@waitroom/models@1.141.3) (2024-02-29)

## [1.141.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.141.1...@waitroom/models@1.141.2) (2024-02-29)


### Bug Fixes

* session form ([a5e65b6](https://github.com/Waitroom/waitroom/commit/a5e65b6237123913abd0f74e2dc9e918f50221ff))

## [1.141.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.141.0...@waitroom/models@1.141.1) (2024-02-28)


### Bug Fixes

* session form ([2222453](https://github.com/Waitroom/waitroom/commit/22224531576aeeb0238dc78792b9331f2e8299ec))

## [1.141.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.140.0...@waitroom/models@1.141.0) (2024-02-28)


### Features

* WEB-1472 when a remote stream fallback we need to show the audio only ([#1853](https://github.com/Waitroom/rumi.ai/issues/1853)) ([1d0742a](https://github.com/Waitroom/waitroom/commit/1d0742ad380ae4c83c80237f60cb280705e88235))

## [1.140.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.139.1...@waitroom/models@1.140.0) (2024-02-28)


### Features

* added summaries options to session form ([#1935](https://github.com/Waitroom/rumi.ai/issues/1935)) ([d8f65fe](https://github.com/Waitroom/waitroom/commit/d8f65fe0b6e8c62145f6e682e8b955b7a138cf08))

## [1.139.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.139.0...@waitroom/models@1.139.1) (2024-02-27)

## [1.139.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.138.1...@waitroom/models@1.139.0) (2024-02-21)


### Features

* improved error handling in agora related stored ([#1928](https://github.com/Waitroom/rumi.ai/issues/1928)) ([d51562a](https://github.com/Waitroom/waitroom/commit/d51562abe1b45785f3526b336e9f528c2ed7dc6e))

## [1.138.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.138.0...@waitroom/models@1.138.1) (2024-02-20)

## [1.138.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.137.1...@waitroom/models@1.138.0) (2024-02-19)


### Features

* sanity landing page hide header menu option ([55882a3](https://github.com/Waitroom/waitroom/commit/55882a3908b419c27720b8abe38881ef218e27de))

## [1.137.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.137.0...@waitroom/models@1.137.1) (2024-02-12)


### Bug Fixes

* showing loading screen infinitly for unauthed user ([#1854](https://github.com/Waitroom/rumi.ai/issues/1854)) ([7f110f7](https://github.com/Waitroom/waitroom/commit/7f110f7fd98a8dac796f6f665d81d1ee916b2057))

## [1.137.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.136.0...@waitroom/models@1.137.0) (2024-02-07)


### Features

* subscription avatar ([b79bd6f](https://github.com/Waitroom/waitroom/commit/b79bd6fcb3567efe4d2367c92795a37df399a625))

## [1.136.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.135.1...@waitroom/models@1.136.0) (2024-02-07)


### Features

* project chat UI refactor v1 ([#1906](https://github.com/Waitroom/rumi.ai/issues/1906)) ([83f67be](https://github.com/Waitroom/waitroom/commit/83f67be08b8ad783083fadc381c52d475448f23d))

## [1.135.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.135.0...@waitroom/models@1.135.1) (2024-02-06)

## [1.135.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.134.4...@waitroom/models@1.135.0) (2024-02-05)


### Features

* web 1547 add integrations component to sanity ([#1904](https://github.com/Waitroom/rumi.ai/issues/1904)) ([1c86c39](https://github.com/Waitroom/waitroom/commit/1c86c391145da42a76d67c2e9fc0dc5a7957cd7b))

## [1.134.4](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.134.3...@waitroom/models@1.134.4) (2024-02-01)


### Bug Fixes

* skip checkout button ([9dcb448](https://github.com/Waitroom/waitroom/commit/9dcb448df4ebca3adaea358c2e7c44cce7d7f5ff))

## [1.134.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.134.2...@waitroom/models@1.134.3) (2024-01-31)


### Bug Fixes

* added copyAiFeed feature flag back ([#1890](https://github.com/Waitroom/rumi.ai/issues/1890)) ([2801be1](https://github.com/Waitroom/waitroom/commit/2801be15c3cd365f3ec15cedead65734c2e30aaa))

## [1.134.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.134.1...@waitroom/models@1.134.2) (2024-01-29)


### Bug Fixes

* build ([ffa56bf](https://github.com/Waitroom/waitroom/commit/ffa56bf8291eb50712259f2cbf166b6d76f90303))

## [1.134.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.134.0...@waitroom/models@1.134.1) (2024-01-29)

## [1.134.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.133.0...@waitroom/models@1.134.0) (2024-01-29)


### Features

* project monetization ([#1841](https://github.com/Waitroom/rumi.ai/issues/1841)) ([edbaf07](https://github.com/Waitroom/waitroom/commit/edbaf0700bd694d2d195efd484d637dec1c97acb))

## [1.133.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.132.0...@waitroom/models@1.133.0) (2024-01-26)


### Features

* WEB-1359 show someone is logging ai feed item ([#1875](https://github.com/Waitroom/rumi.ai/issues/1875)) ([8652c1b](https://github.com/Waitroom/waitroom/commit/8652c1b70fae6501424021aab7984af7dad9e998))

## [1.132.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.131.1...@waitroom/models@1.132.0) (2024-01-17)


### Features

* WEB-1488 client needs to listen on new reaction when the session data ([#1863](https://github.com/Waitroom/rumi.ai/issues/1863)) ([a1ea37f](https://github.com/Waitroom/waitroom/commit/a1ea37ffae0fa1e7f39d41bf56b7d41f8e87deb2))

## [1.131.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.131.0...@waitroom/models@1.131.1) (2024-01-15)


### Bug Fixes

* styles for errors ([#1850](https://github.com/Waitroom/rumi.ai/issues/1850)) ([74e0cf6](https://github.com/Waitroom/waitroom/commit/74e0cf6051e15494c481b31407bf884e0b779475))

## [1.131.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.130.0...@waitroom/models@1.131.0) (2024-01-12)


### Features

* updated integration form depenency handling ([#1851](https://github.com/Waitroom/rumi.ai/issues/1851)) ([00ece16](https://github.com/Waitroom/waitroom/commit/00ece16ae39dd311f726557f22fa774bf04e64de))

## [1.130.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.129.0...@waitroom/models@1.130.0) (2024-01-12)


### Features

* add timezone info to x-client-info ([#1860](https://github.com/Waitroom/rumi.ai/issues/1860)) ([2b4bf48](https://github.com/Waitroom/waitroom/commit/2b4bf48a1f463f0e07e4ba3a4961f5d750339ae0))

## [1.129.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.128.0...@waitroom/models@1.129.0) (2024-01-02)


### Features

* simplified integration feature flags ([#1842](https://github.com/Waitroom/rumi.ai/issues/1842)) ([b0f8219](https://github.com/Waitroom/waitroom/commit/b0f8219a332d7175cad43c9c53f8f081df2fb71d))

## [1.128.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.127.0...@waitroom/models@1.128.0) (2023-12-22)


### Features

* web 1216 allow for more stream layouts that users can pick from ([#1749](https://github.com/Waitroom/rumi.ai/issues/1749)) ([55762bd](https://github.com/Waitroom/waitroom/commit/55762bdf2263a913602169dd6fbc35e224f0db52))


### Bug Fixes

* release ([606066c](https://github.com/Waitroom/waitroom/commit/606066ce01a7d2c09339d3e624cc452ff034bf4f))

## [1.127.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.126.1...@waitroom/models@1.127.0) (2023-12-21)


### Features

* improved dependent input value handling ([#1838](https://github.com/Waitroom/rumi.ai/issues/1838)) ([75218e6](https://github.com/Waitroom/waitroom/commit/75218e6825d894cd36f1671927ca19f5595f709c))

## [1.126.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.126.0...@waitroom/models@1.126.1) (2023-12-18)

## [1.126.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.125.1...@waitroom/models@1.126.0) (2023-12-14)


### Features

* providerIconBackgroundColor in ai feed item references ([#1830](https://github.com/Waitroom/rumi.ai/issues/1830)) ([9cb2c5c](https://github.com/Waitroom/waitroom/commit/9cb2c5ce55a44e4d1fca04b0f936f1ba88f6dfda))

## [1.125.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.125.0...@waitroom/models@1.125.1) (2023-12-13)


### Bug Fixes

* integrations UI issues on dashboard ([#1820](https://github.com/Waitroom/rumi.ai/issues/1820)) ([4acdb26](https://github.com/Waitroom/waitroom/commit/4acdb2660aa202efbc2b60e5803bad17e7422a20))

## [1.125.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.124.0...@waitroom/models@1.125.0) (2023-12-08)


### Features

* next meeting notification ([#1806](https://github.com/Waitroom/rumi.ai/issues/1806)) ([839523d](https://github.com/Waitroom/waitroom/commit/839523d1ec4dd33d305796e9912a69974bbddb57))

## [1.124.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.123.0...@waitroom/models@1.124.0) (2023-12-08)


### Features

* added dashboard ui for integrations ([#1809](https://github.com/Waitroom/rumi.ai/issues/1809)) ([8bb3ea6](https://github.com/Waitroom/waitroom/commit/8bb3ea673df010e49fe07ad378457f6322827134))

## [1.123.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.122.0...@waitroom/models@1.123.0) (2023-12-06)


### Features

* added integation info to feed items ([#1808](https://github.com/Waitroom/rumi.ai/issues/1808)) ([e86f65a](https://github.com/Waitroom/waitroom/commit/e86f65aa9323fcfe91ee52b403ab6160bdb6e819))

## [1.122.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.121.1...@waitroom/models@1.122.0) (2023-12-06)


### Features

* nango auth updates for slack integration ([#1807](https://github.com/Waitroom/rumi.ai/issues/1807)) ([17e5d68](https://github.com/Waitroom/waitroom/commit/17e5d681788c67ad307c051312794cbb6cd9a5f1))

## [1.121.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.121.0...@waitroom/models@1.121.1) (2023-12-05)


### Bug Fixes

* integrations UI fixes ([#1801](https://github.com/Waitroom/rumi.ai/issues/1801)) ([a69e123](https://github.com/Waitroom/waitroom/commit/a69e1231f43b1f4548816149e60093b5c03a0619))

## [1.121.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.120.2...@waitroom/models@1.121.0) (2023-11-30)


### Features

* WEB-1315 use localstorage for storing recently used integration ([#1796](https://github.com/Waitroom/rumi.ai/issues/1796)) ([d883442](https://github.com/Waitroom/waitroom/commit/d883442f233f5d6cd73f93b5e55374438fca5a9d))

## [1.120.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.120.1...@waitroom/models@1.120.2) (2023-11-29)


### Bug Fixes

* action type ([#1795](https://github.com/Waitroom/rumi.ai/issues/1795)) ([aca0799](https://github.com/Waitroom/waitroom/commit/aca07995ac8f5c5ae3545d00c196372e0cef5be0))

## [1.120.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.120.0...@waitroom/models@1.120.1) (2023-11-29)


### Bug Fixes

* ui issues for integrations ([#1794](https://github.com/Waitroom/rumi.ai/issues/1794)) ([a00d938](https://github.com/Waitroom/waitroom/commit/a00d938e0c9dcdc0ab19d66e26c7117bfe5d22f7))

## [1.120.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.119.0...@waitroom/models@1.120.0) (2023-11-28)


### Features

* redeploy packages ([#1792](https://github.com/Waitroom/rumi.ai/issues/1792)) ([8d183ea](https://github.com/Waitroom/waitroom/commit/8d183eaabc29e48d14182ea010e44cd03022d815))

## [1.119.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.118.1...@waitroom/models@1.119.0) (2023-11-28)


### Features

* WEB-1237 ticketing form submit mutation hook ([#1777](https://github.com/Waitroom/rumi.ai/issues/1777)) ([2ec5695](https://github.com/Waitroom/waitroom/commit/2ec5695d40ed1ee2e444165454356f71581d6293))

## [1.118.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.118.0...@waitroom/models@1.118.1) (2023-11-27)


### Bug Fixes

* better track cleanup ([e411729](https://github.com/Waitroom/waitroom/commit/e4117294494978fbdaf5bc3be8b77ce3bb24f71a))

## [1.118.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.117.1...@waitroom/models@1.118.0) (2023-11-22)


### Features

* improve email integration ([#1776](https://github.com/Waitroom/rumi.ai/issues/1776)) ([c3ecadd](https://github.com/Waitroom/waitroom/commit/c3ecadd00b3be15a38680fdf540a4c24dbca5b75))

## [1.117.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.117.0...@waitroom/models@1.117.1) (2023-11-21)

## [1.117.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.116.0...@waitroom/models@1.117.0) (2023-11-15)


### Features

* added nango sdk integration ([#1768](https://github.com/Waitroom/rumi.ai/issues/1768)) ([7573c25](https://github.com/Waitroom/waitroom/commit/7573c2534d2ade49265b094071974a92919081bc))

## [1.116.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.115.0...@waitroom/models@1.116.0) (2023-11-13)


### Features

* web 1232 update feed item context menu items ([#1762](https://github.com/Waitroom/rumi.ai/issues/1762)) ([6204d57](https://github.com/Waitroom/waitroom/commit/6204d57df3af6a236e57783bacca2429888d0e53))

## [1.115.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.114.0...@waitroom/models@1.115.0) (2023-11-06)


### Features

* email integration ([#1755](https://github.com/Waitroom/rumi.ai/issues/1755)) ([8094be0](https://github.com/Waitroom/waitroom/commit/8094be0361117038fda9dbdde7b65a03c4d910b1))

## [1.114.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.113.0...@waitroom/models@1.114.0) (2023-11-03)


### Features

* added basic integration api service ([#1754](https://github.com/Waitroom/rumi.ai/issues/1754)) ([97bd46f](https://github.com/Waitroom/waitroom/commit/97bd46feb33b7db4fba11d683946b6f722f82be7))

## [1.113.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.112.0...@waitroom/models@1.113.0) (2023-10-31)


### Features

* add integrations feature flag ([#1750](https://github.com/Waitroom/rumi.ai/issues/1750)) ([5bc746d](https://github.com/Waitroom/waitroom/commit/5bc746d33cd4490c920bb0e547f77f384dbd3d87))

## [1.112.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.111.0...@waitroom/models@1.112.0) (2023-10-16)


### Features

* improve streams play ([#1706](https://github.com/Waitroom/rumi.ai/issues/1706)) ([8446d92](https://github.com/Waitroom/waitroom/commit/8446d92b4d1c6712dffd88293b2d7cb53f724f59))

## [1.111.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.110.0...@waitroom/models@1.111.0) (2023-10-04)


### Features

* action item editing milestone 2 ([#1614](https://github.com/Waitroom/rumi.ai/issues/1614)) ([67cec4c](https://github.com/Waitroom/waitroom/commit/67cec4c73a26caf64262a139633ee554e59080bf))

## [1.110.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.109.1...@waitroom/models@1.110.0) (2023-10-04)


### Features

* trixta integration ([d0bd5f2](https://github.com/Waitroom/waitroom/commit/d0bd5f274e3d930719dd00206315d7c55afdd65e))

## [1.109.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.109.0...@waitroom/models@1.109.1) (2023-10-02)


### Bug Fixes

* cms button and link display ([bc3845e](https://github.com/Waitroom/waitroom/commit/bc3845eeeeb707f98f3ec42f3b87c7c00edf4292))

## [1.109.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.108.0...@waitroom/models@1.109.0) (2023-09-27)


### Features

* rumi rebrand ([#1672](https://github.com/Waitroom/rumi.ai/issues/1672)) ([f3940a6](https://github.com/Waitroom/waitroom/commit/f3940a6443bbbf1d6df80a6a3f782b720a19a041)), closes [#1677](https://github.com/Waitroom/rumi.ai/issues/1677) [#1678](https://github.com/Waitroom/rumi.ai/issues/1678) [#1688](https://github.com/Waitroom/rumi.ai/issues/1688) [#1690](https://github.com/Waitroom/rumi.ai/issues/1690)

## [1.108.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.107.0...@waitroom/models@1.108.0) (2023-09-18)


### Features

* virtual background ([#1671](https://github.com/Waitroom/rumi.ai/issues/1671)) ([457c3c6](https://github.com/Waitroom/waitroom/commit/457c3c6aa7458150000bb673e8c3564a63a51ce8))

## [1.107.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.106.2...@waitroom/models@1.107.0) (2023-09-08)


### Features

* updated cms ([46d3a13](https://github.com/Waitroom/waitroom/commit/46d3a1391f91e4d26bca16b2ea9c26cd17a996ea))

## [1.106.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.106.1...@waitroom/models@1.106.2) (2023-09-08)

## [1.106.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.106.0...@waitroom/models@1.106.1) (2023-09-06)


### Bug Fixes

* trigger build ([add8ef6](https://github.com/Waitroom/waitroom/commit/add8ef64e2e3c07b03ddfe46eb13f8be85fd1a66))

## [1.106.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.105.5...@waitroom/models@1.106.0) (2023-09-06)


### Features

* remove update last viewed ([#1648](https://github.com/Waitroom/rumi.ai/issues/1648)) ([3a564cb](https://github.com/Waitroom/waitroom/commit/3a564cb39d399f455c69d4d557bf83aea50802b2))

## [1.105.5](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.105.4...@waitroom/models@1.105.5) (2023-08-31)


### Bug Fixes

* ai feed ([9760adf](https://github.com/Waitroom/waitroom/commit/9760adf04a124ca56ca5fce73c76b60efb2d2cf9))

## [1.105.4](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.105.3...@waitroom/models@1.105.4) (2023-08-23)


### Bug Fixes

* ai feed types ([714eac9](https://github.com/Waitroom/waitroom/commit/714eac9bab292f2d293fa9a7ff7e13a28eedf99e))

## [1.105.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.105.2...@waitroom/models@1.105.3) (2023-08-23)


### Bug Fixes

* lint issue ([92c5028](https://github.com/Waitroom/waitroom/commit/92c5028121e56f99a09fa7061b5a4c3b14101f22))

## [1.105.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.105.1...@waitroom/models@1.105.2) (2023-08-21)

## [1.105.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.105.0...@waitroom/models@1.105.1) (2023-08-16)


### Bug Fixes

* storybook deploy ([5648c62](https://github.com/Waitroom/waitroom/commit/5648c628b9624a5cbc83678b7b80d71fa744f480))

## [1.105.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.104.1...@waitroom/models@1.105.0) (2023-08-16)


### Features

* feed item editing milestone 1 ([#1593](https://github.com/Waitroom/rumi.ai/issues/1593)) ([8ac1419](https://github.com/Waitroom/waitroom/commit/8ac1419e146212c3796d70f6ea19a1eab40a7038))

## [1.104.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.104.0...@waitroom/models@1.104.1) (2023-08-14)

## [1.104.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.103.0...@waitroom/models@1.104.0) (2023-08-14)


### Features

* handle errors thrown from backend related to session start ([45d0d43](https://github.com/Waitroom/waitroom/commit/45d0d431a964a3e856c05ce24eae4ee35ca1b651))

## [1.103.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.102.1...@waitroom/models@1.103.0) (2023-08-14)


### Features

* add multi-language support ([dd1aca6](https://github.com/Waitroom/waitroom/commit/dd1aca68216709f6adac20b34ec8134b15eaac29))

## [1.102.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.102.0...@waitroom/models@1.102.1) (2023-08-10)


### Bug Fixes

* try catch track effects ([ea61420](https://github.com/Waitroom/waitroom/commit/ea614205538f4dc10a3c586c094373ed022af261))

## [1.102.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.101.4...@waitroom/models@1.102.0) (2023-08-09)


### Features

* show permission fix guide ([#1570](https://github.com/Waitroom/rumi.ai/issues/1570)) ([3297821](https://github.com/Waitroom/waitroom/commit/329782196e7720702ded96f45765e529cb0377da))

## [1.101.4](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.101.3...@waitroom/models@1.101.4) (2023-08-08)


### Bug Fixes

* dashboard cache issue ([b7a5962](https://github.com/Waitroom/waitroom/commit/b7a59627ec0726f1db1afe673d63411bbfa4ba95))

## [1.101.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.101.2...@waitroom/models@1.101.3) (2023-08-02)

## [1.101.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.101.2...@waitroom/models@1.101.3) (2023-08-02)

## [1.101.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.101.2...@waitroom/models@1.101.3) (2023-08-02)

## [1.101.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.101.1...@waitroom/models@1.101.2) (2023-07-31)

## [1.101.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.101.0...@waitroom/models@1.101.1) (2023-07-31)

## [1.101.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.100.0...@waitroom/models@1.101.0) (2023-07-31)


### Features

* updated release setup ([f452e8a](https://github.com/Waitroom/waitroom/commit/f452e8aa98243fbcc24799738c4d4eb1eb2f4fa8))

# @waitroom/models [1.100.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.99.1...@waitroom/models@1.100.0) (2023-07-27)


### Features

* facilitate party mode type of meetings ([#1524](https://github.com/Waitroom/rumi.ai/issues/1524)) ([5ab4168](https://github.com/Waitroom/waitroom/commit/5ab4168e1212375418c59455deedfbd12731e16a)), closes [#1525](https://github.com/Waitroom/rumi.ai/issues/1525) [#1529](https://github.com/Waitroom/rumi.ai/issues/1529) [#1535](https://github.com/Waitroom/rumi.ai/issues/1535) [#1534](https://github.com/Waitroom/rumi.ai/issues/1534) [#1536](https://github.com/Waitroom/rumi.ai/issues/1536) [#1539](https://github.com/Waitroom/rumi.ai/issues/1539) [#1538](https://github.com/Waitroom/rumi.ai/issues/1538) [#1543](https://github.com/Waitroom/rumi.ai/issues/1543) [#1546](https://github.com/Waitroom/rumi.ai/issues/1546) [#1550](https://github.com/Waitroom/rumi.ai/issues/1550) [#1551](https://github.com/Waitroom/rumi.ai/issues/1551)

## @waitroom/models [1.99.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.99.0...@waitroom/models@1.99.1) (2023-07-24)


### Bug Fixes

* downgrade limit on getAiFeed ([#1542](https://github.com/Waitroom/rumi.ai/issues/1542)) ([347b76e](https://github.com/Waitroom/waitroom/commit/347b76e682d153455b6821f60c7bc1b0d95f8144))

# @waitroom/models [1.99.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.98.1...@waitroom/models@1.99.0) (2023-07-21)


### Features

* more common changes for party-mode ([#1541](https://github.com/Waitroom/rumi.ai/issues/1541)) ([59a0c2f](https://github.com/Waitroom/waitroom/commit/59a0c2f3ab1c41f7ea2d65942373056864712026))

## @waitroom/models [1.98.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.98.0...@waitroom/models@1.98.1) (2023-07-20)

# @waitroom/models [1.98.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.97.3...@waitroom/models@1.98.0) (2023-07-17)


### Features

* party-mode common changes ([#1531](https://github.com/Waitroom/rumi.ai/issues/1531)) ([9a7e4aa](https://github.com/Waitroom/waitroom/commit/9a7e4aa5459217fc0aaa8f11929cd5b7365fcd95))

## @waitroom/models [1.97.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.97.2...@waitroom/models@1.97.3) (2023-07-17)

## @waitroom/models [1.97.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.97.1...@waitroom/models@1.97.2) (2023-07-12)


### Bug Fixes

* session model ([4f5cc25](https://github.com/Waitroom/waitroom/commit/4f5cc2568ffe79407398aa677bd8bb26e9024319))

## @waitroom/models [1.97.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.97.0...@waitroom/models@1.97.1) (2023-07-11)

# @waitroom/models [1.97.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.96.1...@waitroom/models@1.97.0) (2023-07-10)


### Features

* post session summary trixta reactions (WIP) ([#1520](https://github.com/Waitroom/rumi.ai/issues/1520)) ([e04f977](https://github.com/Waitroom/waitroom/commit/e04f977d7506e03a56954e363b95a5e92ddbf554))

## @waitroom/models [1.96.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.96.0...@waitroom/models@1.96.1) (2023-07-08)


### Bug Fixes

* build ([e2a8e95](https://github.com/Waitroom/waitroom/commit/e2a8e95ae4aaf382be48d7ed099ab6020ab12ff1))
* cache keys ([f0e868e](https://github.com/Waitroom/waitroom/commit/f0e868e0b46411602548e6c81b97472abc96c35f))

# @waitroom/models [1.96.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.95.0...@waitroom/models@1.96.0) (2023-07-08)


### Features

* summary message click player skip ([f9c8e00](https://github.com/Waitroom/waitroom/commit/f9c8e0033b90ab9d576b2fd7a962fea48b5e6053))

# @waitroom/models [1.95.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.94.3...@waitroom/models@1.95.0) (2023-07-07)


### Features

* added meetingType and isQueueEnabled flags for meeting ([#1522](https://github.com/Waitroom/rumi.ai/issues/1522)) ([23aabfb](https://github.com/Waitroom/waitroom/commit/23aabfb92cf115ef2e8f0e32241c1c631ed99ecb))

## @waitroom/models [1.94.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.94.2...@waitroom/models@1.94.3) (2023-07-05)

## @waitroom/models [1.94.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.94.1...@waitroom/models@1.94.2) (2023-06-22)

## @waitroom/models [1.94.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.94.0...@waitroom/models@1.94.1) (2023-06-13)

# @waitroom/models [1.94.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.93.0...@waitroom/models@1.94.0) (2023-06-08)


### Features

* updated api gateway headers ([#1484](https://github.com/Waitroom/rumi.ai/issues/1484)) ([0656fd1](https://github.com/Waitroom/waitroom/commit/0656fd140762b76c6ae6cfccbc4beabfe729a295))

# @waitroom/models [1.93.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.92.0...@waitroom/models@1.93.0) (2023-06-05)


### Features

* WEB-818 meeting live updates ([#1473](https://github.com/Waitroom/rumi.ai/issues/1473)) ([dee0b84](https://github.com/Waitroom/waitroom/commit/dee0b847e32cd8f295433b9dc0d500401e156fc1)), closes [#1461](https://github.com/Waitroom/rumi.ai/issues/1461)

# @waitroom/models [1.92.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.91.0...@waitroom/models@1.92.0) (2023-05-31)


### Features

* summa AI enabled toggle ([17f2541](https://github.com/Waitroom/waitroom/commit/17f254162eb0290c0e99c196fcbeda475693a55a))

# @waitroom/models [1.91.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.90.1...@waitroom/models@1.91.0) (2023-05-30)


### Features

* shortcut instructions ([e8424d7](https://github.com/Waitroom/waitroom/commit/e8424d786c97aad53afb609437c204fdf60138bb))

# @waitroom/models [1.90.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.89.0...@waitroom/models@1.90.0) (2023-05-29)


### Features

* build trigger ([30e866f](https://github.com/Waitroom/waitroom/commit/30e866ff01f29aaac10a5504e413a9ccdc29acb6))
* web 755 implement summa ai tabs ([#1418](https://github.com/Waitroom/rumi.ai/issues/1418)) ([452aa38](https://github.com/Waitroom/waitroom/commit/452aa38fe035f1829f3d52a15536144003fcb41d))

# @waitroom/models [1.89.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.88.0...@waitroom/models@1.89.0) (2023-05-26)


### Features

* there will be at least 1 host now ([#1456](https://github.com/Waitroom/rumi.ai/issues/1456)) ([0c7c845](https://github.com/Waitroom/waitroom/commit/0c7c84591929811b7dff583101934e9e532b4a05))

# @waitroom/models [1.88.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.87.0...@waitroom/models@1.88.0) (2023-05-25)


### Features

* project adding a co host-producer ([#1394](https://github.com/Waitroom/rumi.ai/issues/1394)) ([d9388f3](https://github.com/Waitroom/waitroom/commit/d9388f3584c9ed4aef912212a87d7b8c0c4396c8)), closes [#1396](https://github.com/Waitroom/rumi.ai/issues/1396) [#1398](https://github.com/Waitroom/rumi.ai/issues/1398) [#1403](https://github.com/Waitroom/rumi.ai/issues/1403) [#1399](https://github.com/Waitroom/rumi.ai/issues/1399) [#1406](https://github.com/Waitroom/rumi.ai/issues/1406) [#1420](https://github.com/Waitroom/rumi.ai/issues/1420) [#1435](https://github.com/Waitroom/rumi.ai/issues/1435) [#1436](https://github.com/Waitroom/rumi.ai/issues/1436) [#1444](https://github.com/Waitroom/rumi.ai/issues/1444) [#1445](https://github.com/Waitroom/rumi.ai/issues/1445)

# @waitroom/models [1.87.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.86.1...@waitroom/models@1.87.0) (2023-05-18)


### Features

* added actions and reaction for host downgrade ([1e4ca82](https://github.com/Waitroom/waitroom/commit/1e4ca82ec0092c35cc11c2de054d59c975a49d34))
* added trixta action and reaction types ([49a283a](https://github.com/Waitroom/waitroom/commit/49a283a66cecdf73ab3162abf2808df8d8df0393))

## @waitroom/models [1.86.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.86.0...@waitroom/models@1.86.1) (2023-05-10)

# @waitroom/models [1.86.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.85.0...@waitroom/models@1.86.0) (2023-05-10)


### Features

* updated summary hook ([08fe5b9](https://github.com/Waitroom/waitroom/commit/08fe5b988175e3d560d3443044240c2c3e2d2342))

# @waitroom/models [1.85.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.84.1...@waitroom/models@1.85.0) (2023-05-09)


### Features

* WEB-480 virtual onboarding ([#1409](https://github.com/Waitroom/rumi.ai/issues/1409)) ([f0c11fb](https://github.com/Waitroom/waitroom/commit/f0c11fb6957be3b52ee9d21d305ab56a72edd217))

## @waitroom/models [1.84.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.84.0...@waitroom/models@1.84.1) (2023-04-25)

# @waitroom/models [1.84.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.83.0...@waitroom/models@1.84.0) (2023-04-22)


### Features

* transcription summary ([414c36f](https://github.com/Waitroom/waitroom/commit/414c36f9666e126bb6a3fa6520cec18650603c4d))

# @waitroom/models [1.83.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.82.0...@waitroom/models@1.83.0) (2023-04-21)


### Features

* get live transcription summary hook ([#1395](https://github.com/Waitroom/rumi.ai/issues/1395)) ([d999851](https://github.com/Waitroom/waitroom/commit/d9998518235d6acf35a9a0132872d0643d267a5e))

# @waitroom/models [1.82.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.81.0...@waitroom/models@1.82.0) (2023-04-18)


### Features

* agora rtt ([#1393](https://github.com/Waitroom/rumi.ai/issues/1393)) ([1ddd65b](https://github.com/Waitroom/waitroom/commit/1ddd65b4c1258073870a5ae5168bb1a30b82ffa7))

# @waitroom/models [1.81.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.80.0...@waitroom/models@1.81.0) (2023-04-14)


### Features

* useSendTranscription hook ([#1388](https://github.com/Waitroom/rumi.ai/issues/1388)) ([02a9201](https://github.com/Waitroom/waitroom/commit/02a92018a83ec16e6d0fa1b159aa61bf89547531))

# @waitroom/models [1.80.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.79.3...@waitroom/models@1.80.0) (2023-04-13)


### Features

* add trixta action payload model for live transcript  ([#1386](https://github.com/Waitroom/rumi.ai/issues/1386)) ([0e1feb0](https://github.com/Waitroom/waitroom/commit/0e1feb09f38b08f5634f2b412584877aa8634d7a))

## @waitroom/models [1.79.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.79.2...@waitroom/models@1.79.3) (2023-04-05)

## @waitroom/models [1.79.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.79.1...@waitroom/models@1.79.2) (2023-03-27)


### Bug Fixes

* response bug ([bdae8e8](https://github.com/Waitroom/waitroom/commit/bdae8e80ea4255ad40498d91ad7b8e31b739a7f5))

## @waitroom/models [1.79.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.79.0...@waitroom/models@1.79.1) (2023-03-24)


### Bug Fixes

* video border ([c88337b](https://github.com/Waitroom/waitroom/commit/c88337b4c99b93297c7a5dd5e9698650559ed3ba))

# @waitroom/models [1.79.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.78.0...@waitroom/models@1.79.0) (2023-03-23)


### Bug Fixes

* success button ([23e446c](https://github.com/Waitroom/waitroom/commit/23e446ca4c36a274d1a102fbe63dd63038a4849a))


### Features

* updated UI according to recordings state ([#1342](https://github.com/Waitroom/rumi.ai/issues/1342)) ([c56c27c](https://github.com/Waitroom/waitroom/commit/c56c27c9b7f3136e289aeca35e9552e13351a398))

# @waitroom/models [1.78.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.77.0...@waitroom/models@1.78.0) (2023-03-21)


### Features

* add recordingStartTimestamp to episode ([#1332](https://github.com/Waitroom/rumi.ai/issues/1332)) ([9f22bad](https://github.com/Waitroom/waitroom/commit/9f22bad9e71318ad6f0b8811d8715f1b5ef165c3))

# @waitroom/models [1.77.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.76.1...@waitroom/models@1.77.0) (2023-03-21)


### Features

* cms video with preview ([babd381](https://github.com/Waitroom/waitroom/commit/babd381c39a07e96c71a9af64159de1f69179616))

## @waitroom/models [1.76.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.76.0...@waitroom/models@1.76.1) (2023-03-20)


### Bug Fixes

* header padding ([66781f2](https://github.com/Waitroom/waitroom/commit/66781f26e34e5b8111d2b8913fc43f38e131cad2))

# @waitroom/models [1.76.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.75.0...@waitroom/models@1.76.0) (2023-03-17)


### Features

* grid video ([b160ba4](https://github.com/Waitroom/waitroom/commit/b160ba4e880f9012883ab5a54609ac5778ed7577))

# @waitroom/models [1.75.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.74.0...@waitroom/models@1.75.0) (2023-03-17)


### Features

* instant meetings ([#1279](https://github.com/Waitroom/rumi.ai/issues/1279)) ([e5f08eb](https://github.com/Waitroom/waitroom/commit/e5f08eb9a1bd6db6dddd4ec7a6a40f8ccf1dcbf1)), closes [#1284](https://github.com/Waitroom/rumi.ai/issues/1284) [#1289](https://github.com/Waitroom/rumi.ai/issues/1289) [#1287](https://github.com/Waitroom/rumi.ai/issues/1287) [#1295](https://github.com/Waitroom/rumi.ai/issues/1295) [#1296](https://github.com/Waitroom/rumi.ai/issues/1296) [#1298](https://github.com/Waitroom/rumi.ai/issues/1298) [#1300](https://github.com/Waitroom/rumi.ai/issues/1300) [#1303](https://github.com/Waitroom/rumi.ai/issues/1303) [#1302](https://github.com/Waitroom/rumi.ai/issues/1302) [#1304](https://github.com/Waitroom/rumi.ai/issues/1304) [#1305](https://github.com/Waitroom/rumi.ai/issues/1305) [#1306](https://github.com/Waitroom/rumi.ai/issues/1306) [#1310](https://github.com/Waitroom/rumi.ai/issues/1310) [#1309](https://github.com/Waitroom/rumi.ai/issues/1309) [#1315](https://github.com/Waitroom/rumi.ai/issues/1315) [#1317](https://github.com/Waitroom/rumi.ai/issues/1317) [#1320](https://github.com/Waitroom/rumi.ai/issues/1320) [#1319](https://github.com/Waitroom/rumi.ai/issues/1319) [#1321](https://github.com/Waitroom/rumi.ai/issues/1321) [#1318](https://github.com/Waitroom/rumi.ai/issues/1318)

# @waitroom/models [1.74.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.73.0...@waitroom/models@1.74.0) (2023-03-15)


### Features

* cms menu update ([0026976](https://github.com/Waitroom/waitroom/commit/002697687af3e494a821293ee5e69da0a811ef7a))

# @waitroom/models [1.73.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.72.0...@waitroom/models@1.73.0) (2023-03-15)


### Features

* analytics ids, events and some fixes ([1adb756](https://github.com/Waitroom/waitroom/commit/1adb756600a906d1eb5ed59d0d4802c518654744))

# @waitroom/models [1.72.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.71.0...@waitroom/models@1.72.0) (2023-02-23)


### Features

* chime-in polishes ([ed7d54a](https://github.com/Waitroom/waitroom/commit/ed7d54abfa11c3e5c63214ed626ef4c555879644)), closes [#2](https://github.com/Waitroom/rumi.ai/issues/2)

# @waitroom/models [1.71.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.70.0...@waitroom/models@1.71.0) (2023-02-23)


### Features

* one on one conversations ([#1232](https://github.com/Waitroom/rumi.ai/issues/1232)) ([feb0d53](https://github.com/Waitroom/waitroom/commit/feb0d535d4520bb355c8e5dcfaf4a53c86f25aa4)), closes [#1125](https://github.com/Waitroom/rumi.ai/issues/1125) [#1128](https://github.com/Waitroom/rumi.ai/issues/1128) [#1182](https://github.com/Waitroom/rumi.ai/issues/1182) [#1183](https://github.com/Waitroom/rumi.ai/issues/1183) [#1184](https://github.com/Waitroom/rumi.ai/issues/1184) [#1185](https://github.com/Waitroom/rumi.ai/issues/1185) [#1187](https://github.com/Waitroom/rumi.ai/issues/1187) [#1205](https://github.com/Waitroom/rumi.ai/issues/1205) [#1208](https://github.com/Waitroom/rumi.ai/issues/1208) [#1209](https://github.com/Waitroom/rumi.ai/issues/1209) [#1215](https://github.com/Waitroom/rumi.ai/issues/1215) [#1227](https://github.com/Waitroom/rumi.ai/issues/1227) [#1229](https://github.com/Waitroom/rumi.ai/issues/1229) [#1228](https://github.com/Waitroom/rumi.ai/issues/1228)

# @waitroom/models [1.70.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.69.1...@waitroom/models@1.70.0) (2023-02-23)


### Features

* update ChimedInUser model with duration ([#1268](https://github.com/Waitroom/rumi.ai/issues/1268)) ([47e40e4](https://github.com/Waitroom/waitroom/commit/47e40e4ceb8944ec3470405d29731b04a394b7ab))

## @waitroom/models [1.69.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.69.0...@waitroom/models@1.69.1) (2023-02-20)


### Bug Fixes

* toast messages when accepting request ([#1258](https://github.com/Waitroom/rumi.ai/issues/1258)) ([298a713](https://github.com/Waitroom/waitroom/commit/298a713ed9ff85963ddd508a3c2f2a8f15f87bc4))

# @waitroom/models [1.69.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.68.4...@waitroom/models@1.69.0) (2023-02-17)


### Features

* chime-in ([fc7c956](https://github.com/Waitroom/waitroom/commit/fc7c956a24b78a9eb96fdf60c841bdf1b2f0cd9c)), closes [#1241](https://github.com/Waitroom/rumi.ai/issues/1241)

## @waitroom/models [1.68.4](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.68.3...@waitroom/models@1.68.4) (2023-02-15)

## @waitroom/models [1.68.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.68.2...@waitroom/models@1.68.3) (2023-02-11)

## @waitroom/models [1.68.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.68.1...@waitroom/models@1.68.2) (2023-02-10)

## @waitroom/models [1.68.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.68.0...@waitroom/models@1.68.1) (2023-02-09)


### Bug Fixes

* refactored vertical section ([e41509a](https://github.com/Waitroom/waitroom/commit/e41509aad0e3cd6c60ae5693e10287de91e9d591))

# @waitroom/models [1.68.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.67.0...@waitroom/models@1.68.0) (2023-02-09)


### Features

* added footnote to vertical section ([f2b9a4c](https://github.com/Waitroom/waitroom/commit/f2b9a4cfde1edece459fd23317e3bf06e2efaa21))

# @waitroom/models [1.67.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.66.0...@waitroom/models@1.67.0) (2023-02-08)


### Features

* add users array to aggregate emoji reactions ([ef705d1](https://github.com/Waitroom/waitroom/commit/ef705d1d7aad89be6532053e504da95112870653))

# @waitroom/models [1.66.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.65.0...@waitroom/models@1.66.0) (2023-02-08)


### Features

* added footnote to cms components ([2b88155](https://github.com/Waitroom/waitroom/commit/2b881554b747a7a257f73d823bddaca9d2b658f4))

# @waitroom/models [1.65.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.64.1...@waitroom/models@1.65.0) (2023-02-06)


### Features

* adds models & common package chime-in changes ([#1234](https://github.com/Waitroom/rumi.ai/issues/1234)) ([7e6ac71](https://github.com/Waitroom/waitroom/commit/7e6ac711ddb80d3302fa7698c36bdbfb9a3e27af))

## @waitroom/models [1.64.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.64.0...@waitroom/models@1.64.1) (2023-01-25)

# @waitroom/models [1.64.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.63.3...@waitroom/models@1.64.0) (2023-01-25)


### Features

* hero callout text ([#1223](https://github.com/Waitroom/rumi.ai/issues/1223)) ([f0de4b2](https://github.com/Waitroom/waitroom/commit/f0de4b2e3e8fc302437c8279a3e25e60a31ad9dc))

## @waitroom/models [1.63.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.63.2...@waitroom/models@1.63.3) (2023-01-24)


### Bug Fixes

* packages, WEB-349 ([1edad47](https://github.com/Waitroom/waitroom/commit/1edad471139b8afbb12721d480e0d5a2acf343e1))

## @waitroom/models [1.63.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.63.1...@waitroom/models@1.63.2) (2023-01-24)

## @waitroom/models [1.63.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.63.0...@waitroom/models@1.63.1) (2023-01-20)

# @waitroom/models [1.63.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.62.0...@waitroom/models@1.63.0) (2023-01-15)


### Features

* Project/website 3.0 ([#1203](https://github.com/Waitroom/rumi.ai/issues/1203)) ([c4cdc27](https://github.com/Waitroom/waitroom/commit/c4cdc273337b2e2b47531d03b6c790d983f6347d)), closes [#1194](https://github.com/Waitroom/rumi.ai/issues/1194) [#1196](https://github.com/Waitroom/rumi.ai/issues/1196)

# @waitroom/models [1.62.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.61.1...@waitroom/models@1.62.0) (2023-01-13)


### Features

* CRE-1235 update auth service ([#1114](https://github.com/Waitroom/rumi.ai/issues/1114)) ([392073b](https://github.com/Waitroom/waitroom/commit/392073b50c4458441ec1b98112ef0de8778d6554)), closes [#1125](https://github.com/Waitroom/rumi.ai/issues/1125) [#1128](https://github.com/Waitroom/rumi.ai/issues/1128) [#1182](https://github.com/Waitroom/rumi.ai/issues/1182) [#1183](https://github.com/Waitroom/rumi.ai/issues/1183) [#1184](https://github.com/Waitroom/rumi.ai/issues/1184) [#1185](https://github.com/Waitroom/rumi.ai/issues/1185) [#1187](https://github.com/Waitroom/rumi.ai/issues/1187) [#1189](https://github.com/Waitroom/rumi.ai/issues/1189)

## @waitroom/models [1.61.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.61.0...@waitroom/models@1.61.1) (2022-12-14)

# @waitroom/models [1.61.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.60.2...@waitroom/models@1.61.0) (2022-12-14)


### Features

* added common-api package ([a72f7b2](https://github.com/Waitroom/waitroom/commit/a72f7b2233601592b2ceb5b74aef314c4e0197ee))

## @waitroom/models [1.60.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.60.1...@waitroom/models@1.60.2) (2022-12-08)


### Bug Fixes

* viewer list memo, refactor useCallbacks hook ([b300d9a](https://github.com/Waitroom/waitroom/commit/b300d9ab4c6b01309060d66f4fb5ddc4fdc08276))

## @waitroom/models [1.60.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.60.0...@waitroom/models@1.60.1) (2022-11-30)

# @waitroom/models [1.60.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.59.1...@waitroom/models@1.60.0) (2022-11-30)


### Features

* cre 1137 viewer reactions web ([#1120](https://github.com/Waitroom/rumi.ai/issues/1120)) ([4867a4f](https://github.com/Waitroom/waitroom/commit/4867a4f472b7b0b19cbab5c5c14f23456de84be6)), closes [#1118](https://github.com/Waitroom/rumi.ai/issues/1118) [#1133](https://github.com/Waitroom/rumi.ai/issues/1133)

## @waitroom/models [1.59.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.59.0...@waitroom/models@1.59.1) (2022-11-22)

# @waitroom/models [1.59.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.58.1...@waitroom/models@1.59.0) (2022-11-08)


### Features

* hosting flow redesign ([#1100](https://github.com/Waitroom/rumi.ai/issues/1100)) ([202a917](https://github.com/Waitroom/waitroom/commit/202a91764879a745f7bed5ce9d5d19bda9559669))

## @waitroom/models [1.58.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.58.0...@waitroom/models@1.58.1) (2022-10-28)

# @waitroom/models [1.58.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.57.1...@waitroom/models@1.58.0) (2022-10-13)


### Features

* now producer can share screen ([#1076](https://github.com/Waitroom/rumi.ai/issues/1076)) ([957f6ad](https://github.com/Waitroom/waitroom/commit/957f6adacbdf5d74138cd81e5ba9ab5074b89f88))

## @waitroom/models [1.57.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.57.0...@waitroom/models@1.57.1) (2022-10-06)

# @waitroom/models [1.57.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.56.1...@waitroom/models@1.57.0) (2022-10-03)


### Features

* CRE=875 screen sharing ([#1020](https://github.com/Waitroom/rumi.ai/issues/1020)) ([5f2ed1d](https://github.com/Waitroom/waitroom/commit/5f2ed1d1df06b7bbb812e503389ac522932d9932)), closes [#1016](https://github.com/Waitroom/rumi.ai/issues/1016) [#1010](https://github.com/Waitroom/rumi.ai/issues/1010) [#1019](https://github.com/Waitroom/rumi.ai/issues/1019) [#1028](https://github.com/Waitroom/rumi.ai/issues/1028) [#1027](https://github.com/Waitroom/rumi.ai/issues/1027) [#1029](https://github.com/Waitroom/rumi.ai/issues/1029) [#1030](https://github.com/Waitroom/rumi.ai/issues/1030) [#1032](https://github.com/Waitroom/rumi.ai/issues/1032) [#1035](https://github.com/Waitroom/rumi.ai/issues/1035) [#1036](https://github.com/Waitroom/rumi.ai/issues/1036) [#1039](https://github.com/Waitroom/rumi.ai/issues/1039) [#1041](https://github.com/Waitroom/rumi.ai/issues/1041) [#1042](https://github.com/Waitroom/rumi.ai/issues/1042) [#1044](https://github.com/Waitroom/rumi.ai/issues/1044) [#1049](https://github.com/Waitroom/rumi.ai/issues/1049) [#1051](https://github.com/Waitroom/rumi.ai/issues/1051) [#1055](https://github.com/Waitroom/rumi.ai/issues/1055) [#1057](https://github.com/Waitroom/rumi.ai/issues/1057)

## @waitroom/models [1.56.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.56.0...@waitroom/models@1.56.1) (2022-09-29)


### Bug Fixes

* tests ([84a1b93](https://github.com/Waitroom/waitroom/commit/84a1b93675e2243dc7eb6fe6cff0955dce246698))

# @waitroom/models [1.56.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.55.0...@waitroom/models@1.56.0) (2022-09-27)


### Features

* common chanages for screen share feature ([#1037](https://github.com/Waitroom/rumi.ai/issues/1037)) ([85819ee](https://github.com/Waitroom/waitroom/commit/85819ee7857f2d1f50726c7f38f2f35e22e7c94e))

# @waitroom/models [1.55.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.54.0...@waitroom/models@1.55.0) (2022-09-21)


### Features

* common changes for screen share ([#1025](https://github.com/Waitroom/rumi.ai/issues/1025)) ([6bfcfe0](https://github.com/Waitroom/waitroom/commit/6bfcfe02164e7dc9b50cb2a669925a86cdc071d9))

# @waitroom/models [1.54.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.53.0...@waitroom/models@1.54.0) (2022-09-16)


### Features

* sanity ui updates ([#1014](https://github.com/Waitroom/rumi.ai/issues/1014)) ([2257f56](https://github.com/Waitroom/waitroom/commit/2257f56c9ba7db751f0059c1eb4f53461623b274))

# @waitroom/models [1.53.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.52.0...@waitroom/models@1.53.0) (2022-09-08)


### Features

* rename presentaiton to screen share ([cf5cff4](https://github.com/Waitroom/waitroom/commit/cf5cff450becbf4db7591c455fbd028c12372cbe))

# @waitroom/models [1.52.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.51.0...@waitroom/models@1.52.0) (2022-09-06)


### Features

* revert payload change for request_user_session_info ([5f4852b](https://github.com/Waitroom/waitroom/commit/5f4852b37c86e2ca9fad1caebca85f5177eea10f))

# @waitroom/models [1.51.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.50.0...@waitroom/models@1.51.0) (2022-09-06)


### Features

* cms file upload ([#1001](https://github.com/Waitroom/rumi.ai/issues/1001)) ([1190c95](https://github.com/Waitroom/waitroom/commit/1190c95c774e9b3c0da4b460dbc96404b24a514a))

# @waitroom/models [1.50.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.49.0...@waitroom/models@1.50.0) (2022-09-05)


### Features

* screen presentation details ([318c317](https://github.com/Waitroom/waitroom/commit/318c3179a4609434ef99797f8452a53fee1547a2))

# @waitroom/models [1.49.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.48.0...@waitroom/models@1.49.0) (2022-09-05)


### Features

* share presentation ([8fdb77b](https://github.com/Waitroom/waitroom/commit/8fdb77bacbc2230f4c31b396a30091b595109499))

# @waitroom/models [1.48.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.47.1...@waitroom/models@1.48.0) (2022-08-26)


### Features

* cms video element ([#995](https://github.com/Waitroom/rumi.ai/issues/995)) ([c5d1fc0](https://github.com/Waitroom/waitroom/commit/c5d1fc00ec2c7576d0692a451c89726b73d5b794))

## @waitroom/models [1.47.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.47.0...@waitroom/models@1.47.1) (2022-08-23)

# @waitroom/models [1.47.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.46.0...@waitroom/models@1.47.0) (2022-08-18)


### Bug Fixes

* ci ([f85ea4d](https://github.com/Waitroom/waitroom/commit/f85ea4d25ec9ac810c361b9ed242e8163ab6ee5a))
* ci and added lighthouse report ([69a286a](https://github.com/Waitroom/waitroom/commit/69a286aff938b45f7a808eacad6ae6209f334846))


### Features

* sanity cms config ([#959](https://github.com/Waitroom/rumi.ai/issues/959)) ([ce70e33](https://github.com/Waitroom/waitroom/commit/ce70e330ee949f85b2ff142e8b45162e9b4b4635))

# @waitroom/models [1.46.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.45.1...@waitroom/models@1.46.0) (2022-08-16)


### Features

* update the pending requests across session producers on interaction ([8097ac6](https://github.com/Waitroom/waitroom/commit/8097ac6d4691cd23f27ee4b246f4893831dc42da))

## @waitroom/models [1.45.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.45.0...@waitroom/models@1.45.1) (2022-08-16)


### Bug Fixes

* session already requested error code ([56993a4](https://github.com/Waitroom/waitroom/commit/56993a412ef72606ebba9d61fb706b2710173b2b))

# @waitroom/models [1.45.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.44.0...@waitroom/models@1.45.0) (2022-08-03)


### Features

* CRE-723 show how many people the session is ([#952](https://github.com/Waitroom/rumi.ai/issues/952)) ([26ec7f8](https://github.com/Waitroom/waitroom/commit/26ec7f83187c40db5c98d056ce736cc151c9ce43))

# @waitroom/models [1.44.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.43.2...@waitroom/models@1.44.0) (2022-08-01)


### Features

* stream ids map ([#949](https://github.com/Waitroom/rumi.ai/issues/949)) ([ee996b0](https://github.com/Waitroom/waitroom/commit/ee996b041d29878d52f6087cca1632fe325aa09f))

## @waitroom/models [1.43.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.43.1...@waitroom/models@1.43.2) (2022-07-25)

## @waitroom/models [1.43.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.43.0...@waitroom/models@1.43.1) (2022-07-18)

# @waitroom/models [1.43.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.42.0...@waitroom/models@1.43.0) (2022-07-06)


### Bug Fixes

* fixed bugs and issues ([#892](https://github.com/Waitroom/rumi.ai/issues/892)) ([9f46895](https://github.com/Waitroom/waitroom/commit/9f468955de1d7e87afa53bab2301703abf72483e))


### Features

* setup user if granted access for a session ([c6d9a43](https://github.com/Waitroom/waitroom/commit/c6d9a430ac0862571952fdbe273d5c106049f73d))

# @waitroom/models [1.42.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.41.0...@waitroom/models@1.42.0) (2022-06-30)


### Features

* request access live session ([#881](https://github.com/Waitroom/rumi.ai/issues/881)) ([13b0415](https://github.com/Waitroom/waitroom/commit/13b0415b2ae8ca1cddc993e17894b2fea679e4cb))


### Reverts

* reverted agora package ([2f91a5d](https://github.com/Waitroom/waitroom/commit/2f91a5db782c5846053f99c2bc6c727d8c5be22b))

# @waitroom/models [1.41.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.40.2...@waitroom/models@1.41.0) (2022-06-20)


### Features

* CON-552 create a toggle to disable receiving ([#859](https://github.com/Waitroom/rumi.ai/issues/859)) ([cd42485](https://github.com/Waitroom/waitroom/commit/cd42485df0a2774044903e1500b923693e02cc70))

## @waitroom/models [1.40.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.40.1...@waitroom/models@1.40.2) (2022-06-17)


### Bug Fixes

* added --passWithNoTests as pre commit hook starts breaking ([#858](https://github.com/Waitroom/rumi.ai/issues/858)) ([9ab4329](https://github.com/Waitroom/waitroom/commit/9ab43299a3a105d83235f767be139914ad683321))

## @waitroom/models [1.40.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.40.0...@waitroom/models@1.40.1) (2022-06-17)

# @waitroom/models [1.40.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.39.0...@waitroom/models@1.40.0) (2022-06-15)


### Features

* con 533 implement request access flow ([#842](https://github.com/Waitroom/rumi.ai/issues/842)) ([43dd93c](https://github.com/Waitroom/waitroom/commit/43dd93c6db43284045edb5000de2654c1f9272e0))

# @waitroom/models [1.39.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.38.1...@waitroom/models@1.39.0) (2022-06-08)


### Features

* CRE-525 session access approval UI ([#837](https://github.com/Waitroom/rumi.ai/issues/837)) ([94654c2](https://github.com/Waitroom/waitroom/commit/94654c2b824741b8793a6bf854a927d2a9c3d8c6))

## @waitroom/models [1.38.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.38.0...@waitroom/models@1.38.1) (2022-06-07)


### Bug Fixes

* button variants ([87a59ae](https://github.com/Waitroom/waitroom/commit/87a59aed367fdd65330d1060ed4731f22aa1bab6))

# @waitroom/models [1.38.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.37.0...@waitroom/models@1.38.0) (2022-06-06)


### Features

* fix tests, release trigger ([7d51b3e](https://github.com/Waitroom/waitroom/commit/7d51b3eb09f50cfed9a34ee6ae64fc2f77ad43dc))
* release trigger ([d28ed96](https://github.com/Waitroom/waitroom/commit/d28ed96d8eb4dccc32bf4a164c7e9b0e3a621a5a))

# @waitroom/models [1.37.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.36.0...@waitroom/models@1.37.0) (2022-06-01)


### Features

* STATUS_VIEWER role is being replaced by SESSION_INFO_VIEWER ([7f9e02c](https://github.com/Waitroom/waitroom/commit/7f9e02cd4c9f30056492502f548b2c189647a962))

# @waitroom/models [1.36.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.35.0...@waitroom/models@1.36.0) (2022-05-24)


### Features

* cre 404 create invite form ([#811](https://github.com/Waitroom/rumi.ai/issues/811)) ([efd673d](https://github.com/Waitroom/waitroom/commit/efd673d3c4d6b0a96e78751909f9064f4141c1ac))

# @waitroom/models [1.35.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.34.0...@waitroom/models@1.35.0) (2022-05-23)


### Features

* Feature/cre 346 update the session form UI new ([#812](https://github.com/Waitroom/rumi.ai/issues/812)) ([2e38151](https://github.com/Waitroom/waitroom/commit/2e38151fff6b62ccd96864f3beeb398ff344721e)), closes [#806](https://github.com/Waitroom/rumi.ai/issues/806) [#810](https://github.com/Waitroom/rumi.ai/issues/810)

# @waitroom/models [1.34.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.33.0...@waitroom/models@1.34.0) (2022-05-20)


### Features

* wr 1162 implement refresh token ([#791](https://github.com/Waitroom/rumi.ai/issues/791)) ([c75366e](https://github.com/Waitroom/waitroom/commit/c75366e4412d308270096b2c25ba7572bc03c3a7))

# @waitroom/models [1.33.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.32.0...@waitroom/models@1.33.0) (2022-05-19)


### Features

* aurora needs filename ([bbe7cdb](https://github.com/Waitroom/waitroom/commit/bbe7cdb125185abc36af24b15e70df1d8112fe47))

# @waitroom/models [1.32.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.31.0...@waitroom/models@1.32.0) (2022-05-18)


### Features

* aurora image service implemented ([0d0546b](https://github.com/Waitroom/waitroom/commit/0d0546b25d6ed7d9cd9a6cc87a89b14685db89c7))

# @waitroom/models [1.31.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.30.0...@waitroom/models@1.31.0) (2022-05-18)


### Features

* cre 362 replace trixta components with hooks ([#798](https://github.com/Waitroom/rumi.ai/issues/798)) ([63eb4b2](https://github.com/Waitroom/waitroom/commit/63eb4b2b3ca2d81249cc1cd7949f569defa580fc))

# @waitroom/models [1.30.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.29.1...@waitroom/models@1.30.0) (2022-05-17)


### Features

* add clip cookies models ([#802](https://github.com/Waitroom/rumi.ai/issues/802)) ([fc55627](https://github.com/Waitroom/waitroom/commit/fc55627a058911676c0c5fccc293f9ac23dfe00e))

## @waitroom/models [1.29.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.29.0...@waitroom/models@1.29.1) (2022-05-12)

# @waitroom/models [1.29.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.28.0...@waitroom/models@1.29.0) (2022-05-09)


### Bug Fixes

* fixed tests ([7ce91a4](https://github.com/Waitroom/waitroom/commit/7ce91a4952afbb14d7a8b289b48ab086b1888a62))


### Features

* subscription hook ([e53285b](https://github.com/Waitroom/waitroom/commit/e53285b8671fe63c0051460acf8f980c510f01a0))

# @waitroom/models [1.28.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.27.0...@waitroom/models@1.28.0) (2022-05-05)


### Features

* implement trixta types ([09eb5fd](https://github.com/Waitroom/waitroom/commit/09eb5fd84a09d2d1556891dfbc5f286dec1304a1))

# @waitroom/models [1.27.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.26.0...@waitroom/models@1.27.0) (2022-05-04)


### Features

* trixta reaction types ([1e7e201](https://github.com/Waitroom/waitroom/commit/1e7e20149e535afd8877d234b44a8bb955c5c1ec))

# @waitroom/models [1.26.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.25.1...@waitroom/models@1.26.0) (2022-05-03)


### Features

* add presence listeners and dispatches to set socket meta data ([a070ffa](https://github.com/Waitroom/waitroom/commit/a070ffaeed0f88438caf704d4273d5c77098d011))

## @waitroom/models [1.25.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.25.0...@waitroom/models@1.25.1) (2022-05-03)


### Bug Fixes

* clips query for trixta action ([1c4343a](https://github.com/Waitroom/waitroom/commit/1c4343ac9702eef4a1bb0e608cc9e370cdce875a))

# @waitroom/models [1.25.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.24.0...@waitroom/models@1.25.0) (2022-05-03)


### Features

* trixta action types ([a58dff6](https://github.com/Waitroom/waitroom/commit/a58dff60ee98cdf0b8e120e615be345ee53d0b88))

# @waitroom/models [1.24.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.23.2...@waitroom/models@1.24.0) (2022-04-20)


### Features

* add active stream roles to hosts channel users ([#757](https://github.com/Waitroom/rumi.ai/issues/757)) ([f46ef26](https://github.com/Waitroom/waitroom/commit/f46ef26bf0bc1e092fe200e1417ff0bdc4379e95))

## @waitroom/models [1.23.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.23.1...@waitroom/models@1.23.2) (2022-04-20)

## @waitroom/models [1.23.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.23.0...@waitroom/models@1.23.1) (2022-04-14)

# @waitroom/models [1.23.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.22.0...@waitroom/models@1.23.0) (2022-04-13)


### Features

* wr 748 develop and improve trixta tools for data fetching ([#755](https://github.com/Waitroom/rumi.ai/issues/755)) ([8d0a6d9](https://github.com/Waitroom/waitroom/commit/8d0a6d9da4205149abf518a688913c4e331af4e0))

# @waitroom/models [1.22.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.21.0...@waitroom/models@1.22.0) (2022-04-11)


### Features

* presence for producers and hosts between eachother ([#736](https://github.com/Waitroom/rumi.ai/issues/736)) ([1b925d2](https://github.com/Waitroom/waitroom/commit/1b925d267ff6e3ad93589d1a2a6e9b1bd7afe7a9))

# @waitroom/models [1.21.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.20.0...@waitroom/models@1.21.0) (2022-04-08)


### Features

* implement trixta recording tracking events ([#738](https://github.com/Waitroom/rumi.ai/issues/738)) ([2aca224](https://github.com/Waitroom/waitroom/commit/2aca22485b25949f9747506487be65f2170ec305))
* update packages ([#740](https://github.com/Waitroom/rumi.ai/issues/740)) ([b3fc7ca](https://github.com/Waitroom/waitroom/commit/b3fc7ca467efe4b83aa3d936a4f8e57646a238b1))

# @waitroom/models [1.20.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.19.3...@waitroom/models@1.20.0) (2022-04-04)


### Features

* update featured_sessions to reducer ([#731](https://github.com/Waitroom/rumi.ai/issues/731)) ([43603d4](https://github.com/Waitroom/waitroom/commit/43603d4f9d6300f42234d5055236514cc4112777))

## @waitroom/models [1.19.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.19.2...@waitroom/models@1.19.3) (2022-03-25)


### Bug Fixes

* clip models portrait ([#724](https://github.com/Waitroom/rumi.ai/issues/724)) ([789bff2](https://github.com/Waitroom/waitroom/commit/789bff211731beb3effc7510f9861da94e15742b))

## @waitroom/models [1.19.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.19.1...@waitroom/models@1.19.2) (2022-03-25)


### Bug Fixes

* update clip modal ([#721](https://github.com/Waitroom/rumi.ai/issues/721)) ([9474cea](https://github.com/Waitroom/waitroom/commit/9474ceae4bcefe6ab0b18a6f0da540f59d47f83d))
* update faker for durations ([#722](https://github.com/Waitroom/rumi.ai/issues/722)) ([d690ac1](https://github.com/Waitroom/waitroom/commit/d690ac19e9c0c3f4c32cc8aea90283a2dd666c97))

## @waitroom/models [1.19.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.19.0...@waitroom/models@1.19.1) (2022-03-22)


### Bug Fixes

* featured banner status link ([#706](https://github.com/Waitroom/rumi.ai/issues/706)) ([fa82dea](https://github.com/Waitroom/waitroom/commit/fa82dea2c54f5b1c9636517efee1b4513418ff99))

# @waitroom/models [1.19.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.18.0...@waitroom/models@1.19.0) (2022-03-22)


### Features

* session reminders ([#692](https://github.com/Waitroom/rumi.ai/issues/692)) ([ce5a6a2](https://github.com/Waitroom/waitroom/commit/ce5a6a29e465f4f77da6a7faa6ac6652e97fa258))

# @waitroom/models [1.18.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.17.0...@waitroom/models@1.18.0) (2022-03-21)


### Features

* check for logged in user to get sessions ([#697](https://github.com/Waitroom/rumi.ai/issues/697)) ([10a283b](https://github.com/Waitroom/waitroom/commit/10a283b2eab7b08ae7fa8fe3f364aa7113556128))

# @waitroom/models [1.17.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.16.0...@waitroom/models@1.17.0) (2022-03-21)


### Features

* success feedback for subscription ([#694](https://github.com/Waitroom/rumi.ai/issues/694)) ([2c4c805](https://github.com/Waitroom/waitroom/commit/2c4c8056eb91c40dd8de71fd08af2eed1bdfbedd))

# @waitroom/models [1.16.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.15.0...@waitroom/models@1.16.0) (2022-03-18)


### Features

* now player uses HLS video as the default playback format ([#689](https://github.com/Waitroom/rumi.ai/issues/689)) ([29921d7](https://github.com/Waitroom/waitroom/commit/29921d716375623b0ef29bcd75d158a3297f452a))

# @waitroom/models [1.15.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.14.1...@waitroom/models@1.15.0) (2022-03-18)


### Features

* home page redesign ([#667](https://github.com/Waitroom/rumi.ai/issues/667)) ([9c13ceb](https://github.com/Waitroom/waitroom/commit/9c13ceb295e9e070cd3171296301107441d882c3))

## @waitroom/models [1.14.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.14.0...@waitroom/models@1.14.1) (2022-03-14)


### Bug Fixes

* fixed tests ([48f6a5d](https://github.com/Waitroom/waitroom/commit/48f6a5da715704bf69b5232cac71aed70a7c78d2))

# @waitroom/models [1.14.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.13.2...@waitroom/models@1.14.0) (2022-03-10)


### Features

* waitroom list subscription hook ([#671](https://github.com/Waitroom/rumi.ai/issues/671)) ([bad9abc](https://github.com/Waitroom/waitroom/commit/bad9abc660fe51ea9f49dc1b212eb180b8535927))

## @waitroom/models [1.13.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.13.1...@waitroom/models@1.13.2) (2022-03-07)

## @waitroom/models [1.13.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.13.0...@waitroom/models@1.13.1) (2022-03-06)

# @waitroom/models [1.13.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.12.4...@waitroom/models@1.13.0) (2022-03-01)


### Features

* session producer ([3f5675d](https://github.com/Waitroom/waitroom/commit/3f5675d7490bdb794eb30afefb18d195f9cce625))

## @waitroom/models [1.12.4](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.12.3...@waitroom/models@1.12.4) (2022-02-22)

## @waitroom/models [1.12.3](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.12.2...@waitroom/models@1.12.3) (2022-02-11)


### Bug Fixes

* session page shadow cutoff, player clip dates ([2626601](https://github.com/Waitroom/waitroom/commit/2626601991b785ae25bb6023353ad60ae8e13ee2))

## @waitroom/models [1.12.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.12.1...@waitroom/models@1.12.2) (2022-01-31)


### Bug Fixes

* loose option build fix ([fa09e7b](https://github.com/Waitroom/waitroom/commit/fa09e7bb49d063e7ab908784a539a834233dc336))

## @waitroom/models [1.12.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.12.0...@waitroom/models@1.12.1) (2022-01-26)

# @waitroom/models [1.12.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.11.0...@waitroom/models@1.12.0) (2022-01-14)


### Features

* add portraitPlaylistURLs to models ([#523](https://github.com/Waitroom/rumi.ai/issues/523)) ([25c621e](https://github.com/Waitroom/waitroom/commit/25c621e98ace8657e4bc0f37118d2dfa4842e01f))

# @waitroom/models [1.11.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.10.0...@waitroom/models@1.11.0) (2022-01-14)


### Features

* playback ([04438e0](https://github.com/Waitroom/waitroom/commit/04438e0f1e7d9de824f049c44c60dd410deba450))

# @waitroom/models [1.10.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.9.2...@waitroom/models@1.10.0) (2021-12-22)


### Features

* added more link to about session content ([8c9db35](https://github.com/Waitroom/waitroom/commit/8c9db354deed650d056b9055720ec172b204034c))

## @waitroom/models [1.9.2](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.9.1...@waitroom/models@1.9.2) (2021-12-16)

## @waitroom/models [1.9.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.9.0...@waitroom/models@1.9.1) (2021-12-14)


### Bug Fixes

* fixed models ([5ee8211](https://github.com/Waitroom/waitroom/commit/5ee82111b66a246942568c999b97811d71c08eb8))

# @waitroom/models [1.9.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.8.1...@waitroom/models@1.9.0) (2021-12-14)


### Features

* playback components, folder refactor ([c8399c4](https://github.com/Waitroom/waitroom/commit/c8399c464778221f5b4b0b04b045235d0fa429b1))

## @waitroom/models [1.8.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.8.0...@waitroom/models@1.8.1) (2021-12-08)

# @waitroom/models [1.8.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.7.1...@waitroom/models@1.8.0) (2021-12-07)


### Features

* refactor component folder structure, updated font weight, playback components ([de57f54](https://github.com/Waitroom/waitroom/commit/de57f547f9eceb3a66313687fd5f4ecec1cfd43a))

## @waitroom/models [1.7.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.7.0...@waitroom/models@1.7.1) (2021-12-06)


### Bug Fixes

* now on air sound effect doesn't play on queue pause/resume ([#480](https://github.com/Waitroom/rumi.ai/issues/480)) ([3cfbea7](https://github.com/Waitroom/waitroom/commit/3cfbea7247472217316b60b455b40c023a61a890))

# @waitroom/models [1.7.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.6.1...@waitroom/models@1.7.0) (2021-12-02)


### Features

* nfts ([7639545](https://github.com/Waitroom/waitroom/commit/7639545b6a38c9e4fd6a503e75bc8cf5570df1a5))
* replaced IG profile with email ([5bed466](https://github.com/Waitroom/waitroom/commit/5bed4665fb6e201c283075dbb7d405393a714938))

## @waitroom/models [1.6.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.6.0...@waitroom/models@1.6.1) (2021-11-09)

# @waitroom/models [1.6.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.5.0...@waitroom/models@1.6.0) (2021-11-08)


### Features

* crypto package ([edd02dd](https://github.com/Waitroom/waitroom/commit/edd02dd2c773bbf6a30a51c5b3ec108af33174d2))

# @waitroom/models [1.5.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.4.0...@waitroom/models@1.5.0) (2021-11-08)


### Features

* added crypto package ([89d8d46](https://github.com/Waitroom/waitroom/commit/89d8d460ea9235d843b40e153897917c84862885))


### Reverts

* reverted commit ([a7087dd](https://github.com/Waitroom/waitroom/commit/a7087dd31001114498e5f5d86b24a77a51d6c4a6))
* reverted commit ([eeb1791](https://github.com/Waitroom/waitroom/commit/eeb1791d94ed960fa7bbe505f76b2b624645e5de))

# @waitroom/models [1.4.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.3.0...@waitroom/models@1.4.0) (2021-10-06)


### Features

* agora update, fixed some stream issue ([63035d7](https://github.com/Waitroom/waitroom/commit/63035d7e3d787310b3c7e5d93fd73c64358b6ca5))

# @waitroom/models [1.3.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.2.1...@waitroom/models@1.3.0) (2021-10-01)


### Features

* multiple session blocking ([#418](https://github.com/Waitroom/rumi.ai/issues/418)) ([40fa711](https://github.com/Waitroom/waitroom/commit/40fa711e5b601df490c0c3e6ce4cb3c7bc0b7df8))

## @waitroom/models [1.2.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.2.0...@waitroom/models@1.2.1) (2021-09-22)


### Bug Fixes

* reorder socials ([d731224](https://github.com/Waitroom/waitroom/commit/d7312240d3f78b63381a9674f825cf8478b2bebc))

# @waitroom/models [1.2.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.1.0...@waitroom/models@1.2.0) (2021-09-20)


### Features

* layout-engine and config packages with tests ([#410](https://github.com/Waitroom/rumi.ai/issues/410)) ([815c604](https://github.com/Waitroom/waitroom/commit/815c604a443a657e8f4db16e3a27ba71553f9c01))

# @waitroom/models [1.1.0](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.0.1...@waitroom/models@1.1.0) (2021-09-17)


### Features

* user settings selectors, fixed publish command ([ed8ca64](https://github.com/Waitroom/waitroom/commit/ed8ca648e0e7fc60617cf2b18d971ff768280a88))

## @waitroom/models [1.0.1](https://github.com/Waitroom/waitroom/compare/@waitroom/models@1.0.0...@waitroom/models@1.0.1) (2021-09-15)


### Bug Fixes

* fixed publish ([7ef8fe6](https://github.com/Waitroom/waitroom/commit/7ef8fe680ceec57191f2f7faadc3cd50aeb8e49c))

# @waitroom/models 1.0.0 (2021-09-15)


### Bug Fixes

* fixed test coverage ([91d7692](https://github.com/Waitroom/waitroom/commit/91d7692c29a2ff8ff4ab2a17325a00a4e18cbf17))


### Features

* split common package ([#405](https://github.com/Waitroom/rumi.ai/issues/405)) ([19222cc](https://github.com/Waitroom/waitroom/commit/19222ccf9c7c5d425ee7201c272738a12c659ac3))
