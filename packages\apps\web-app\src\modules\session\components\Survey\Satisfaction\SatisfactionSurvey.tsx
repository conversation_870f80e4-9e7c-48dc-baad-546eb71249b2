import { Box, CloseButton, Fade, FadeProps, Flex, Text } from '@chakra-ui/react';
import { Session } from '@waitroom/models';
import { useTranslation } from 'react-i18next';
import { Feedback } from './Feedback/Feedback';
import { Mood } from './Mood/Mood';
import { animLength, useComponent } from './useComponent';

const fadeProps: FadeProps = {
  transition: {
    enter: { duration: 0 },
    exit: { duration: animLength / 1000 },
  },
  unmountOnExit: true,
};
const ns = 'session.satisfactionSurvey.';
export type SatisfactionSurveyProps = {
  sessionId: Session['sessionID'];
  recurrenceId: Session['sessionRecurrenceID'];
};

export const SatisfactionSurvey = ({ sessionId, recurrenceId }: SatisfactionSurveyProps) => {
  const { t } = useTranslation();
  const { isOpen, onClose, stage, nextStage } = useComponent();

  if (!isOpen) return null;
  return (
    <Box
      data-testid="survey"
      position={'fixed'}
      zIndex={'popover'}
      bottom={0}
      right={6}
      roundedTop={'3xl'}
      borderWidth={'1px'}
      borderBottom={'none'}
      borderColor={'gray.800'}
      bgColor={'gray.900'}
      opacity={0.96}
      minW={[275, 360, 380]}
      pb={1}
    >
      {stage === 0 && (
        <Mood
          onClose={onClose}
          next={nextStage}
          sessionId={sessionId}
          recurrenceId={recurrenceId}
        />
      )}
      <Fade in={stage === 1} {...fadeProps}>
        <Feedback
          onClose={onClose}
          next={nextStage}
          sessionId={sessionId}
          recurrenceId={recurrenceId}
        />
      </Fade>
      <Fade in={stage === 2} {...fadeProps}>
        <Flex justify={'center'} align={'center'} gap={3} px={4} mt={6} mb={4}>
          <Text fontSize={'lg'} fontWeight={'bold'} data-testid="survey-stage-2">
            {t(`${ns}thankYou`)}
          </Text>
          <CloseButton size={'sm'} onClick={onClose} />
        </Flex>
      </Fade>
    </Box>
  );
};
