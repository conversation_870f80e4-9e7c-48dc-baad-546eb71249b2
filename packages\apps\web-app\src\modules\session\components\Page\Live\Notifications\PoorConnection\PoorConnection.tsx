import { Button, <PERSON>Button, Flex } from '@chakra-ui/react';
import LogoLoader from '@core/components/Loader/Logo/LogoLoader';
import { SessionModalType, setSessionModal } from '@waitroom/common';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { useAudioOnly } from '../../../../../hooks/useAudioOnly';
import { removeNotification } from '../../../store';
import { CONNECTION } from '../constants';

const PoorConnectionNotification = (): ReactElement | null => {
  const { t } = useTranslation();
  const { onToggle, enabled: isAudioOnly } = useAudioOnly();

  return (
    <Flex
      p={2}
      wrap={'wrap'}
      bg={'whiteAlpha.800'}
      rounded={'lg'}
      fontSize={{ base: 'xs', sm: 'sm' }}
      align={'center'}
      color={'gray.900'}
      columnGap={2}
      rowGap={1}
      maxW={[300, 700]}
      fontWeight={'bold'}
      lineHeight={'shorter'}
      textAlign={'left'}
    >
      <LogoLoader size={32} mr={2} />
      <p>{t('global.poorConnection')}</p>
      {!isAudioOnly && (
        <Button
          size={{ base: '2xs', sm: 'xs' }}
          fontSize={'xs'}
          px={3}
          colorScheme={'black'}
          onClick={() => onToggle(true)}
        >
          {t('session.tryAudioOnly')}
        </Button>
      )}
      <Button
        variant={'outline'}
        colorScheme={'black'}
        size={{ base: '2xs', sm: 'xs' }}
        fontSize={'xs'}
        px={3}
        onClick={() =>
          setSessionModal({
            type: SessionModalType.TROUBLESHOOT,
          })
        }
      >
        {t('session.troubleshootBtn')}
      </Button>
      <CloseButton
        size={'sm'}
        fontSize={'2xs'}
        color={'gray.700'}
        onClick={() => removeNotification(CONNECTION)}
      />
    </Flex>
  );
};
export default PoorConnectionNotification;
