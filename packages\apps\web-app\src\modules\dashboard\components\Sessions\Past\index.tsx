import { Box, Container, Flex, Image, Text } from '@chakra-ui/react';
import CardLoader from '@core/components/Loader/Card/CardLoader';
import { CDN_IMAGES_URL } from '@core/config';
import { sessionIdsKey } from '@waitroom/common';
import { repeat } from '@waitroom/react-utils';
import { useEffect } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { analyticsService } from '../../../../analytics/services';
import DashboardPastSessionCard from '../../../../session/components/Card/Dashboard/Past/DashboardPastCard';
import CreateButtons from '../RightSideMenu/CreateButtons';
import { useComponent } from '../useComponent';
import { usePastSessionList } from '../useSessionList';

type PastProps = {
  isCreateButtons?: boolean;
};

const Past = ({ isCreateButtons }: PastProps) => {
  const { t } = useTranslation();
  useEffect(() => {
    analyticsService.page('DASHBOARD', {
      state: 'past',
    });
  }, []);

  const { onManage, hasANotetakerConnected } = useComponent();
  const { data, isPending, isFetching } = usePastSessionList();
  const sessions = data?.pages?.[0]?.data?.data?.sessions;

  if (isPending) {
    return <Box mt={6}>{repeat(<CardLoader header={false} lines={2} />)}</Box>;
  }
  if (!sessions?.length) {
    return (
      <Flex py={12} direction="column" justify="center" align="center">
        <Image
          src={`${CDN_IMAGES_URL}/speaker.png`}
          htmlWidth="476"
          htmlHeight="440"
          h={{ base: '175px', sm: '216px' }}
          w="auto"
          alt={'No recordings'}
          mb={8}
        />
        <Text fontSize="3xl" fontWeight={800} mb={2}>
          {t('dashboard.noPastMeetingsTitle')}
        </Text>
        <Text color="gray.700" mb={8} textAlign="center">
          <Trans
            i18nKey={`dashboard.${hasANotetakerConnected ? 'welcomeDescWithBots' : 'noPastMeetingsDesc'}`}
            components={{ strong: <strong /> }}
          />
        </Text>
        <Container maxW="container.sm" px={10}>
          {hasANotetakerConnected && (
            <>
              <Text textAlign="center" color="gray.700" fontSize="md" mt={8}>
                {t('dashboard.orRTryHostingText')}
              </Text>
              <CreateButtons />
            </>
          )}
        </Container>
      </Flex>
    );
  }
  return (
    <>
      {isCreateButtons && <CreateButtons />}
      <Box minH={5} mt={3}>
        {isFetching && (
          <Text fontSize={'sm'} color={'gray.500'}>
            {t('global.updating')}...
          </Text>
        )}
      </Box>
      {sessions.map((session) => (
        <DashboardPastSessionCard
          key={sessionIdsKey(session)}
          session={session}
          onManage={onManage}
        />
      ))}
    </>
  );
};
export default Past;
