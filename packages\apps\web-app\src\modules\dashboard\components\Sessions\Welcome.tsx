import { Box, Button, Container, Image, Text, VStack } from '@chakra-ui/react';
import { CDN_IMAGES_URL } from '@core/config';
import { selectCurrentUser, useAuthStore } from '@waitroom/auth';
import { Trans, useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { routes } from '../../../../constants/routes';
import { useNotetaker } from '../../../core/hooks/useNotetaker';
import CreateButtons from './RightSideMenu/CreateButtons';

type WelcomeProps = {
  isCompleted: boolean;
};

export const Welcome = ({ isCompleted }: WelcomeProps) => {
  const currentUser = useAuthStore(selectCurrentUser);
  const { t } = useTranslation();

  const { hasANotetakerConnected } = useNotetaker();
  const title = isCompleted
    ? t('dashboard.noUpcomingMeetingsTitle')
    : `👋 ${t('dashboard.welcome{name}', {
        name: currentUser?.firstName,
      })}`;
  const desc = hasANotetakerConnected ? 'dashboard.welcomeDescWithBots' : 'dashboard.welcomeDesc';

  return (
    <VStack py={12} direction="column" gap={8}>
      <Image
        src={`${CDN_IMAGES_URL}/${isCompleted ? 'dashboard-no-upcoming.svg' : 'no-meetings.png'}`}
        htmlWidth="476"
        htmlHeight="440"
        h={{ base: '175px', sm: '216px' }}
        w="auto"
        alt={title}
      />
      <Box textAlign="center">
        <Text fontSize="3xl" fontWeight={800} mb={2}>
          {title}
        </Text>
        <Text color="gray.400">
          <Trans i18nKey={desc} components={{ strong: <strong /> }} />
        </Text>
      </Box>
      <Box w="full">
        <Container maxW="container.sm" px={10}>
          {hasANotetakerConnected ? (
            <>
              <Text textAlign="center" color="gray.400" fontSize="md" mt={8}>
                {t('dashboard.orRTryHostingText')}
              </Text>
              <CreateButtons />
            </>
          ) : (
            <>
              <Button
                as={Link}
                to={`${routes.DASHBOARD.INTEGRATIONS.link}#notetaker`}
                variant={'outline'}
                colorScheme={'black'}
                w={'full'}
                size={'def'}
              >
                {t('dashboard.onboarding.intro.notetakerBtn')}
              </Button>
            </>
          )}
        </Container>
      </Box>
    </VStack>
  );
};
