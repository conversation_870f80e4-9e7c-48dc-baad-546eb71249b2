import { cronToFrequency, frequencyToCron } from './helpers';

describe('frequencyToCron & cronToFrequency', () => {
  it('should convert daily frequency to cron and back', () => {
    const freq = {
      type: 'daily' as const,
      time: new Date(2023, 0, 1, 8, 30, 0, 0), // Jan 1, 2023, 08:30 local
    };
    const cron = frequencyToCron(freq, false);
    expect(cron).toBe('30 8 * * *');
    const parsed = cronToFrequency(cron, false);
    expect(parsed.type).toBe('daily');
    expect(parsed.time.getHours()).toBe(8);
    expect(parsed.time.getMinutes()).toBe(30);
  });

  it('should convert weekly frequency with days to cron and back', () => {
    const freq = {
      type: 'weekly' as const,
      time: new Date(2023, 0, 1, 10, 15, 0, 0), // Jan 1, 2023, 10:15 local
      days: [1, 3, 5],
    };
    const cron = frequencyToCron(freq, false);
    expect(cron).toBe('15 10 * * 1,3,5');
    const parsed = cronToFrequency(cron, false);
    expect(parsed.type).toBe('weekly');
    expect(parsed.days).toEqual([1, 3, 5]);
    expect(parsed.time.getHours()).toBe(10);
    expect(parsed.time.getMinutes()).toBe(15);
  });

  it('should convert monthly frequency with dates to cron and back', () => {
    const freq = {
      type: 'monthly' as const,
      time: new Date(2023, 0, 1, 22, 0, 0, 0), // Jan 1, 2023, 22:00 local
      dates: [1, 15, 30],
    };
    const cron = frequencyToCron(freq, false);
    expect(cron).toBe('0 22 1,15,30 * *');
    const parsed = cronToFrequency(cron, false);
    expect(parsed.type).toBe('monthly');
    expect(parsed.dates).toEqual([1, 15, 30]);
    expect(parsed.time.getHours()).toBe(22);
    expect(parsed.time.getMinutes()).toBe(0);
  });

  it('should handle weekly with no days as wildcard', () => {
    const freq = {
      type: 'weekly' as const,
      time: new Date(2023, 0, 1, 7, 0, 0, 0), // Jan 1, 2023, 07:00 local
      days: [],
    };
    const cron = frequencyToCron(freq, false);
    expect(cron).toBe('0 7 * * *'); // Should fallback to daily cron
    const parsed = cronToFrequency(cron, false);
    expect(parsed.type).toBe('daily');
  });

  it('should handle monthly with no dates as wildcard', () => {
    const freq = {
      type: 'monthly' as const,
      time: new Date(2023, 0, 1, 7, 0, 0, 0), // Jan 1, 2023, 07:00 local
      dates: [],
    };
    const cron = frequencyToCron(freq, false);
    expect(cron).toBe('0 7 * * *'); // Should fallback to daily cron
    const parsed = cronToFrequency(cron, false);
    expect(parsed.type).toBe('daily');
  });

  it('should fallback to daily for invalid cron', () => {
    const parsed = cronToFrequency('invalid cron string');
    expect(parsed.type).toBe('daily');
  });

  it('should handle all days of week', () => {
    const freq = {
      type: 'weekly' as const,
      time: new Date(2023, 0, 1, 12, 0, 0, 0), // Jan 1, 2023, 12:00 local
      days: [0, 1, 2, 3, 4, 5, 6],
    };
    const cron = frequencyToCron(freq, false);
    expect(cron).toBe('0 12 * * 0,1,2,3,4,5,6');
    const parsed = cronToFrequency(cron, false);
    expect(parsed.type).toBe('weekly');
    expect(parsed.days).toEqual([0, 1, 2, 3, 4, 5, 6]);
  });
});

describe('utc option in frequencyToCron & cronToFrequency', () => {
  it('should convert daily frequency to cron using UTC', () => {
    // 8:30 local, but we want UTC
    const freq = {
      type: 'daily' as const,
      time: new Date(Date.UTC(2023, 0, 1, 8, 30, 0, 0)), // 08:30 UTC
    };
    const cron = frequencyToCron(freq, true);
    expect(cron).toBe('30 8 * * *');
    const parsed = cronToFrequency(cron, true);
    expect(parsed.type).toBe('daily');
    expect(parsed.time.getUTCHours()).toBe(8);
    expect(parsed.time.getUTCMinutes()).toBe(30);
  });

  it('should convert daily frequency to cron using local time', () => {
    // 8:30 local time
    const freq = {
      type: 'daily' as const,
      time: new Date(2023, 0, 1, 8, 30, 0, 0), // 08:30 local
    };
    const cron = frequencyToCron(freq, false);
    expect(cron).toBe('30 8 * * *');
    const parsed = cronToFrequency(cron, false);
    expect(parsed.type).toBe('daily');
    expect(parsed.time.getHours()).toBe(8);
    expect(parsed.time.getMinutes()).toBe(30);
  });

  it('should produce different cron for same instant in local vs UTC', () => {
    // Create a date that is 8:30 local, but check what UTC is
    const localDate = new Date(2023, 0, 1, 8, 30, 0, 0);
    const freq = {
      type: 'daily' as const,
      time: localDate,
    };
    const cronLocal = frequencyToCron(freq, false);
    const cronUTC = frequencyToCron(freq, true);
    // The hour/minute will differ if local timezone is not UTC
    expect(cronLocal).not.toBe(cronUTC);
  });

  it('should parse cron string to correct time in UTC and local', () => {
    const cron = '15 10 * * *';
    const parsedUTC = cronToFrequency(cron, true);
    const parsedLocal = cronToFrequency(cron, false);
    // The hour/minute fields should match the input, but in different time zones
    expect(parsedUTC.time.getUTCHours()).toBe(10);
    expect(parsedUTC.time.getUTCMinutes()).toBe(15);
    expect(parsedLocal.time.getHours()).toBe(10);
    expect(parsedLocal.time.getMinutes()).toBe(15);
  });
});
