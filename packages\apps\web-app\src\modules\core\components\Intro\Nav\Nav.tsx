import { Button } from '@chakra-ui/react';

const selectedTabWidth = { base: 12, lg: 32 };
const unselectedTabWidth = { base: 10, sm: 8, lg: 10 };
export const Nav = ({ isSelected }: { isSelected?: boolean }) => {
  return (
    <Button
      _hover={{
        color: 'transparent',
      }}
      bgColor={isSelected ? 'whiteAlpha.800' : 'whiteAlpha.100'}
      h={4}
      w={isSelected ? selectedTabWidth : unselectedTabWidth}
      minW={isSelected ? selectedTabWidth : unselectedTabWidth}
      p={0}
      transition="all 0.5s "
      cursor="default"
    />
  );
};
