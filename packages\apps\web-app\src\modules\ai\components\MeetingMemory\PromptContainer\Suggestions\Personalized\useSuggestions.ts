import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { DefaultApiResponse, MeetingsApiService, meetingsService } from '@waitroom/common-api';
import { MeetingMemory } from '@waitroom/models';
import {
  getApiResponseCode,
  getPersonalizedSuggestionsQueryKey,
  inactiveOptions,
} from '@waitroom/react-query';
import { useEffect, useMemo, useState } from 'react';

type UseSuggestionsProps = {
  sessionId: string;
  recurrenceId: string;
  endTimestamp?: number;
};

type UseSuggestionsReturn = {
  suggestions: MeetingMemory.PersonalizedSuggestion[] | undefined;
  isLoading: boolean;
};
export const waitAfterEndTimestamp = 10000;
// still generating error code
const retryErrorCode = 202;
export const useSuggestions = ({
  sessionId,
  recurrenceId,
  endTimestamp,
}: UseSuggestionsProps): UseSuggestionsReturn => {
  const [isQueryEnabled, setIsQueryEnabled] = useState(false);
  const queryOptions: UseQueryOptions<MeetingsApiService.GetPersonalizedSuggestions['response']> =
    useMemo(
      () => ({
        ...inactiveOptions['1h'],
        queryKey: getPersonalizedSuggestionsQueryKey(sessionId, recurrenceId),
        queryFn: async () => {
          const response = await meetingsService.getPersonalizedSuggestions(
            sessionId,
            recurrenceId,
          );
          // throw in case of missing suggestions
          if (response.success && !response.data?.data?.suggestions?.length) {
            throw {
              code: retryErrorCode,
              success: false,
              data: {
                code: retryErrorCode,
                success: false,
                data: undefined,
              },
            } satisfies DefaultApiResponse;
          }
          return response;
        },
        ...inactiveOptions['7d'],
        retry: (count, error) => {
          const code = getApiResponseCode(error as unknown as DefaultApiResponse);
          // retry on 202 (still generating) and skip on 404
          return code === retryErrorCode ? count < 6 : code !== 404;
        },
        retryDelay: (_, error) =>
          (getApiResponseCode(error as unknown as DefaultApiResponse) || 0) === retryErrorCode
            ? 7000
            : 100,
        enabled: isQueryEnabled,
      }),
      [recurrenceId, sessionId, isQueryEnabled],
    );

  // enable query after 10 seconds after session end time
  useEffect(() => {
    if (endTimestamp) {
      const currentTime = Date.now();
      const targetTime = endTimestamp * 1000 + waitAfterEndTimestamp;
      const timeUntilTarget = Math.max(0, targetTime - currentTime);

      const timeoutId = setTimeout(() => {
        setIsQueryEnabled(true);
      }, timeUntilTarget);

      return () => clearTimeout(timeoutId);
    }
    return undefined;
  }, [endTimestamp]);
  const { data, isLoading } = useQuery(queryOptions);

  const suggestions = data?.data?.data?.suggestions;

  return {
    suggestions,
    isLoading,
  };
};
