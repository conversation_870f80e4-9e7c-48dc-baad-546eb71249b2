import { Box, ButtonProps, Text, VStack } from '@chakra-ui/react';
import { userOnboarding } from '@waitroom/models';
import { Trans } from 'react-i18next';
import { NotetakerButtons } from '../../../../core/components/Intro/components/Notetaker/Buttons';
import { checklistVideos, ns } from '../config';
import { DefaultComponentProps } from '../types';

const buttonProps: ButtonProps = {
  size: 'sm',
  fontSize: 'md',
  variant: 'outline',
  colorScheme: 'gray.900',
  borderColor: 'gray.300',
  w: 'full',
  mt: 'auto',
  iconSpacing: 3,
};
const key = userOnboarding.noteTaker;
const Notetaker = (_: DefaultComponentProps) => {
  return (
    <VStack gap={3}>
      <Text fontSize="sm">
        <Trans i18nKey={`${ns}${key}-desc`} components={{ strong: <strong /> }} />
      </Text>
      <Box
        as="video"
        w="full"
        h="auto"
        playsInline
        loop
        autoPlay
        muted
        rounded="2xl"
        src={checklistVideos[key]}
      />

      <NotetakerButtons buttonProps={buttonProps} iconProps={{ w: 'auto', h: 4 }} />
        color={'gray.900!'}
    </VStack>
  );
};

export default Notetaker;
