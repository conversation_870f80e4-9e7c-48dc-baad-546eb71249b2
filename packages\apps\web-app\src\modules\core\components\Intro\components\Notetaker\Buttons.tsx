import { Button, ButtonProps, Image, ImageProps } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { useRecallIntegration } from '../../../../../integrations/hooks/useRecallIntegration';
import { CDN_IMAGES_URL } from '../../../../config';
import { useAddTranslations } from '../../../../hooks/useAddTranslations';
import { transInt } from '../../local';

type NotetakerButtonsProps = {
  buttonProps: ButtonProps;
  iconProps?: ImageProps;
  onNext?: () => void;
  isSkipButton?: boolean;
};

const ns = 'integrations.recallCalendar';
export const NotetakerButtons = ({
  buttonProps,
  iconProps,
  onNext,
  isSkipButton,
}: NotetakerButtonsProps) => {
  useAddTranslations(transInt);
  const { t } = useTranslation();
  const recallCalendar = useRecallIntegration();
  return (
    <>
      <Button
        {...buttonProps}
        onClick={() => recallCalendar.handleConnectCalendar()}
        leftIcon={
          <Image
            src={`${CDN_IMAGES_URL}/google-calendar.svg`}
            alt="calendar"
            w="auto"
            h={6}
            {...iconProps}
          />
        }
      >
        {t(`${ns}.btnGoogle`)}
      </Button>
      <Button
        {...buttonProps}
        onClick={() => recallCalendar.handleConnectMicrosoftCalendar()}
        leftIcon={
          <Image
            src={`${CDN_IMAGES_URL}/outlook-calendar.svg`}
            alt="calendar"
            w="auto"
            h={6}
            {...iconProps}
          />
        }
      >
        {t(`${ns}.btnMicrosoft`)}
      </Button>
      {isSkipButton && (
        <Button variant="link" onClick={onNext} fontSize="sm">
          {t(`${ns}.btnSkip`)}
        </Button>
      )}
    </>
  );
};
