import { Box, Button, Center, HStack, Text, VStack } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { faChevronRight } from '@fortawesome/pro-regular-svg-icons';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Nav } from '../Nav/Nav';
import { Video } from '../Video/Video';

const headerFooterHeight = 12;
const ns = 'dashboard.onboarding.intro.';

type RightProps = {
  video: string;
  description?: string;
  onNext: () => void;
  currentStep: number;
  totalSteps: number;
  isLoading: boolean;
};

export const Right = ({
  video,
  description,
  onNext,
  currentStep,
  totalSteps,
  isLoading,
}: RightProps) => {
  const { t } = useTranslation();
  const steps = useMemo(() => new Array(totalSteps).fill(1), [totalSteps]);

  return (
    <Box h="full" w="full">
      <VStack align="center" spacing={4} justify="space-around" h="full">
        <Box h={headerFooterHeight}></Box>
        <Center h="full" maxW="xl" mx="auto">
          <VStack gap={10}>
            <Video src={video} />
            {!!description && (
              <Text textAlign="center" fontSize="xl">
                {t(`${ns}${description}`)}
              </Text>
            )}
          </VStack>
        </Center>
        <HStack h={headerFooterHeight} w="full" justify="space-between">
          <HStack gap={4}>
            {steps.map((_, index) => (
              <Nav key={index} isSelected={index === currentStep} />
            ))}
          </HStack>
          <Button
            size="sm"
            colorScheme="red"
            rightIcon={<Icon icon={faChevronRight} />}
            onClick={onNext}
            isLoading={isLoading}
          >
            {t(currentStep + 1 === totalSteps ? 'global.getStarted' : 'global.next')}
          </Button>
        </HStack>
      </VStack>
    </Box>
  );
};
