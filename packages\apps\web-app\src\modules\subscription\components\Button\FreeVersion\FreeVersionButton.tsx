import { Button, ButtonProps, Text, Tooltip } from '@chakra-ui/react';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, LinkProps } from 'react-router-dom';
import { routes } from '../../../../../constants/routes';
import FreeVersionBadge from '../../../../session/components/Badge/FreeVersion/FreeVersionBadge';

const linkProps = {
  as: Link,
  target: '_blank',
  rel: 'noopener noreferrer',
};

export type FreeVersionButtonProps = ButtonProps &
  Partial<LinkProps> & {
    isOrganizer?: boolean;
    responsive?: boolean;
  };

const FreeVersionButton = ({
  isOrganizer,
  responsive,
  ...rest
}: FreeVersionButtonProps): ReactElement | null => {
  const { t } = useTranslation();
  const btn = (
    <Button
      rounded="md"
      size="3xs"
      variant="outline"
      colorScheme="gray.600"
      color="gray.400"
      textTransform="uppercase"
      px={2}
      title={t('global.freeVersion')}
      to={isOrganizer ? routes.PRICING : undefined}
      {...(isOrganizer ? linkProps : undefined)}
      {...rest}
    >
      <Text as="span" display={responsive ? ['none', 'initial'] : undefined}>
        {t('global.freeVersion')}
      </Text>
      {!!responsive && (
        <Text as="span" display={['initial', 'none']}>
          F
        </Text>
      )}
    </Button>
  );
  return (
    <Tooltip label={isOrganizer ? t('pricing.freeHostTooltip') : t('pricing.freeViewerTooltip')}>
      <span>{isOrganizer ? btn : <FreeVersionBadge sx={rest.sx} responsive={responsive} />}</span>
    </Tooltip>
  );
};
export default FreeVersionButton;
