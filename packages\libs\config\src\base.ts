const notionUrl = 'https://rumiai.notion.site';
const experience = `${notionUrl}/The-Waitroom-Experience-e859705ac3284030b05d20b0d2b2b74c`;
export const config = {
  routes: {
    home: '/',
    host: '/host',
    notFound: '/404',
  },
  facebook: {
    appId: '',
  },
  company: {
    name: 'R<PERSON>',
    domain: 'rumi.ai',
    emails: {
      default: '<EMAIL>',
      contact: '<EMAIL>',
      support: '<EMAIL>',
      deleteAccount: '<EMAIL>',
      community: '<EMAIL>',
      sales: '<EMAIL>',
    },
    socials: {
      twitter: 'https://twitter.com/rumidotai',
      youtube: 'https://www.youtube.com/@RumiDotAI',
      linkedin: 'https://www.linkedin.com/company/rumiai',
      instagram: 'https://www.instagram.com/rumidotai',
    },
  },
  maxImageSize: 5242880, // 5MB
  links: {
    notion: notionUrl,
    onboarding: 'https://calendly.com/aroder-rumi',
    help: 'https://meetings-na2.hubspot.com/meetings/danny-sheehan/x-ray-setup',
    googleCalendar: 'https://workspace.google.com/u/0/marketplace/app/waitroom/************',
    outlookCalendar: 'https://appsource.microsoft.com/en-us/product/Office365/WA200007671',
    chromeExtension:
      'https://chromewebstore.google.com/detail/rumi-chrome-extension/hjikepnkfllnjjphbbobfppkegimelpn',
    paddle: {
      support:
        'https://help.paddle.com/hc/en-us/articles/************-How-do-I-get-in-touch-with-Paddle-support-',
    },
    knowledgebase: {
      default: `${notionUrl}/Rumi-Knowledgebase-2c2fb04d0aba418987c553813d041fb6`,
      pressKit: `${notionUrl}/Rumi-brand-assets-7ed58925f54c4196937a70ea4216ebbe?pvs=4`,
      hostsInfo: `${notionUrl}/Guide-to-Hosting-on-Waitroom-1157fe7389b24c57ab40e3b8f0cae0f0`,
      acknowledgements: `${notionUrl}/Acknowledgements-583bf312c0574df7840bf5327247a53e`,
      gettingStarted: `${notionUrl}/What-to-expect-when-hosting-your-first-Waitroom-59ca069525f0430d8aee2bd1fff5871b`,
      privateSessions: `${notionUrl}/Private-sessions-on-Waitroom-97a6490c58f6415a90d7fe00db6403b6`,
      experience: experience,
      producerSafePlace: `${experience}#be7c0efb471f40af9340c642eaa4ff08`,
      timeWithHost: `${experience}#0705f6c63118414d95767181ba8a55e3`,
      timeAndQueue: `${experience}#3b8b16cde2ed4388b54c58b6e5fd7e8a`,
      chatClipping: `${experience}#59b3bd0d283e4ab987e2c7f907f5b504`,
      useWithCalendly: `${notionUrl}/Using-your-Rumi-ai-Lobby-link-with-Calendly-1ad5c558bea38079bfade2c735de1efb`,
      notetaker: `${notionUrl}/RumiBot-For-Google-Outlook-Calendar-1925c558bea38022b3abe723873e3f58`,
    },
    affiliate: '/landing/affiliate-program',
    terms: `/landing/terms`,
    privacy: `/landing/privacy`,
    careers: '/landing/careers',
    feedback: 'https://rumiai.typeform.com/to/hTMnmlON',
    chrome: 'https://www.google.com/chrome/',
    firefox: 'https://www.mozilla.org/en-US/firefox/new/',
    edge: 'https://www.microsoft.com/en-us/edge',
    apps: {
      googlePlay: 'https://play.google.com/store/apps/details?id=com.waitroom',
      appleApp: 'https://apps.apple.com/us/app/waitroom/id1561696428',
    },
  },
  sanity: {
    appId: '6diaohap',
    apiUrl: 'https://6diaohap.api.sanity.io/v2023-01-19/data/query/production',
    cdnUrl: 'https://cdn.sanity.io',
  },
  segment: {
    trackUrl: 'https://api.segment.io/v1/track',
  },
  paddle: {
    id: 178351,
  },
};
