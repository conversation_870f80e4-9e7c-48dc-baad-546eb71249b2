import { But<PERSON> } from '@chakra-ui/react';
import { Meta, StoryObj } from '@storybook/react';
import { HookWrapper } from '@waitroom/stories';
import { StorybookProvidersDecorator } from '../../../stories/utils/Providers';
import { useClipboard } from './useClipboard';

const meta: Meta<{ value: string; id?: string }> = {
  title: 'web-app/hooks/useClipboard',
  tags: ['autodocs'],
  decorators: [StorybookProvidersDecorator],
};

export default meta;
type Story = StoryObj<{ value: string; id?: string }>;

export const Template: Story = {
  args: {
    value: 'Hello!',
    id: 'id',
  },
  render: (args) => (
    <HookWrapper<typeof useClipboard>
      name="useClipboard"
      hook={useClipboard}
      props={[args.value, args.id]}
    >
      {({ onCopy }) => (
        <Button size="xs" onClick={() => onCopy()} boxShadow="sm" mt={6}>
          Copy
        </Button>
      )}
    </HookWrapper>
  ),
};
