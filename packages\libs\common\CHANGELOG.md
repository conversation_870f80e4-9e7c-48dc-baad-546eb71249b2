# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [1.262.0](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.261.2...@waitroom/common@1.262.0) (2025-07-09)


### Features

* user onboarding updates ([#38](https://github.com/Waitroom/rumi.ai/issues/38)) ([7f9f8c7](https://github.com/Waitroom/rumi.ai/commit/7f9f8c7739fb7cc7d6213eb899bc56bbef55e39f))

## [1.261.2](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.261.1...@waitroom/common@1.261.2) (2025-07-01)


### Bug Fixes

* auto-scroll issue ([#31](https://github.com/Waitroom/rumi.ai/issues/31)) ([b4091ef](https://github.com/Waitroom/rumi.ai/commit/b4091efca86a6cb5701a5da82b5bdcf49ecb2793))
* import lint issue ([b3df94f](https://github.com/Waitroom/rumi.ai/commit/b3df94f8fe71cca0712f0f787a9280001023fa63))

## [1.261.1](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.261.0...@waitroom/common@1.261.1) (2025-07-01)


### Bug Fixes

* improve personalized mm suggestions ([#32](https://github.com/Waitroom/rumi.ai/issues/32)) ([552d5ef](https://github.com/Waitroom/rumi.ai/commit/552d5ef7bb9a4bc3c1b010761b2556fca71069a0))

## [1.261.0](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.260.0...@waitroom/common@1.261.0) (2025-06-26)


### Features

*  personalized meeting memory suggestions ([#27](https://github.com/Waitroom/rumi.ai/issues/27)) ([779dae5](https://github.com/Waitroom/rumi.ai/commit/779dae5ef69a33200713f8f20fae6fd9333a471c))

## [1.260.0](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.259.1...@waitroom/common@1.260.0) (2025-06-24)


### Features

* WEB-2437 upgrade to react 19 ([#9](https://github.com/Waitroom/rumi.ai/issues/9)) ([465dae5](https://github.com/Waitroom/rumi.ai/commit/465dae527ff8f2ebfe9f3914676cd41e56af982e))

## [1.259.1](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.259.0...@waitroom/common@1.259.1) (2025-06-19)


### Bug Fixes

* outlook bot integration text and icons ([#18](https://github.com/Waitroom/rumi.ai/issues/18)) ([efaee1d](https://github.com/Waitroom/rumi.ai/commit/efaee1dcb2b1554db055936593ad9ef1f57c6a76))

## [1.259.0](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.258.2...@waitroom/common@1.259.0) (2025-06-18)


### Features

* now meeting console can receive mm prompts via url ([#16](https://github.com/Waitroom/rumi.ai/issues/16)) ([72f1cc8](https://github.com/Waitroom/rumi.ai/commit/72f1cc8f2b16b88dd672e3a58e6e46950fc35dbc))

## [1.258.2](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.258.1...@waitroom/common@1.258.2) (2025-06-17)


### Bug Fixes

* notetaker labels ([#12](https://github.com/Waitroom/rumi.ai/issues/12)) ([51d6aa1](https://github.com/Waitroom/rumi.ai/commit/51d6aa1d641deb43593ae564ec22eb9af4b88441))

## [1.258.1](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.258.0...@waitroom/common@1.258.1) (2025-06-14)

## [1.258.0](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.257.0...@waitroom/common@1.258.0) (2025-06-13)


### Features

* recall calendar integrations ([#3](https://github.com/Waitroom/rumi.ai/issues/3)) ([fa3be19](https://github.com/Waitroom/rumi.ai/commit/fa3be190998b6d4016d18beca1a4e551c7ca9faf))

## [1.257.0](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.256.5...@waitroom/common@1.257.0) (2025-06-11)


### Features

* prevent unsaved session changes ([#2757](https://github.com/Waitroom/rumi.ai/issues/2757)) ([6c8706e](https://github.com/Waitroom/rumi.ai/commit/6c8706e74f385a01ae5e5b991a04ab07109a1333))

## [1.256.5](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.256.4...@waitroom/common@1.256.5) (2025-06-05)

## [1.256.4](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.256.3...@waitroom/common@1.256.4) (2025-06-02)

## [1.256.3](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.256.2...@waitroom/common@1.256.3) (2025-06-02)


### Bug Fixes

* hide restart button for listening mode sessions ([3ca1b70](https://github.com/Waitroom/rumi.ai/commit/3ca1b708769621cc39238574f7c22ecacecaf54d))

## [1.256.2](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.256.1...@waitroom/common@1.256.2) (2025-05-29)

## [1.256.1](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.256.0...@waitroom/common@1.256.1) (2025-05-29)


### Bug Fixes

* ended banner ([46d1ef7](https://github.com/Waitroom/rumi.ai/commit/46d1ef7f59a21d4e380c57829bc20ad6b97b3a4f))

## [1.256.0](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.255.3...@waitroom/common@1.256.0) (2025-05-29)


### Features

* update end console banner ([56d8064](https://github.com/Waitroom/rumi.ai/commit/56d80642648d061afe0ed2c7d26dc3b79c8037ab))

## [1.255.3](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.255.2...@waitroom/common@1.255.3) (2025-05-28)

## [1.255.2](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.255.1...@waitroom/common@1.255.2) (2025-05-28)


### Bug Fixes

* multi screen share ([#2728](https://github.com/Waitroom/rumi.ai/issues/2728)) ([cf8bee8](https://github.com/Waitroom/rumi.ai/commit/cf8bee835b3fa04aedb6982c83fcb47cf7ecb33a))

## [1.255.1](https://github.com/Waitroom/rumi.ai/compare/@waitroom/common@1.255.0...@waitroom/common@1.255.1) (2025-05-27)

## [1.255.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.254.1...@waitroom/common@1.255.0) (2025-05-26)


### Features

* leave meeting screen ([#2713](https://github.com/Waitroom/waitroom/issues/2713)) ([197a34c](https://github.com/Waitroom/waitroom/commit/197a34c9c2fbd273cd0c448ad74e65e8b01fc98c))

## [1.254.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.254.0...@waitroom/common@1.254.1) (2025-05-22)

## [1.254.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.253.10...@waitroom/common@1.254.0) (2025-05-21)


### Features

* complete some TODOs around the app ([#2708](https://github.com/Waitroom/waitroom/issues/2708)) ([23c590d](https://github.com/Waitroom/waitroom/commit/23c590d191e53f7f8c412cadf1e7d84c2f15afaf))

## [1.253.10](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.253.9...@waitroom/common@1.253.10) (2025-05-19)

## [1.253.9](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.253.8...@waitroom/common@1.253.9) (2025-05-19)


### Bug Fixes

* recordings link, remove test ai feed component ([#2703](https://github.com/Waitroom/waitroom/issues/2703)) ([fcb035b](https://github.com/Waitroom/waitroom/commit/fcb035bca8cd0771233e4f6b2ff908b1079ed6d9))

## [1.253.8](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.253.7...@waitroom/common@1.253.8) (2025-05-15)


### Bug Fixes

* WEB-2745 hide team memory switch for non team members ([#2696](https://github.com/Waitroom/waitroom/issues/2696)) ([c6a9cad](https://github.com/Waitroom/waitroom/commit/c6a9cadb8fb9e29875dd15d309f48c334017bf59))

## [1.253.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.253.6...@waitroom/common@1.253.7) (2025-05-14)

## [1.253.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.253.5...@waitroom/common@1.253.6) (2025-05-14)

## [1.253.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.253.4...@waitroom/common@1.253.5) (2025-05-14)

## [1.253.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.253.3...@waitroom/common@1.253.4) (2025-05-08)


### Bug Fixes

* onboarding saving ([#2679](https://github.com/Waitroom/waitroom/issues/2679)) ([b582914](https://github.com/Waitroom/waitroom/commit/b58291451a9f5ea262173414a781736516732ec4))

## [1.253.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.253.2...@waitroom/common@1.253.3) (2025-05-07)

## [1.253.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.253.1...@waitroom/common@1.253.2) (2025-05-07)


### Bug Fixes

* auth issues on session page ([d263408](https://github.com/Waitroom/waitroom/commit/d2634084cae349445671b00a7ba00472126476ea))

## [1.253.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.253.0...@waitroom/common@1.253.1) (2025-05-07)


### Bug Fixes

* mic not being initialized ([#2674](https://github.com/Waitroom/waitroom/issues/2674)) ([42940d9](https://github.com/Waitroom/waitroom/commit/42940d9b811f91dc42276c868fe83c0861e7ec34))

## [1.253.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.252.5...@waitroom/common@1.253.0) (2025-05-07)


### Features

* web 2590 onboarding update 33 optional show popup for existing users ([#2630](https://github.com/Waitroom/waitroom/issues/2630)) ([6b722de](https://github.com/Waitroom/waitroom/commit/6b722de59386b66e1161bebb8051919b8fac65f6))

## [1.252.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.252.4...@waitroom/common@1.252.5) (2025-05-06)


### Bug Fixes

* redirect issues, end summary loading, fix types ([b0a9549](https://github.com/Waitroom/waitroom/commit/b0a9549bfb3d90b889c4feda17d9900d67bdd1e3))

## [1.252.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.252.3...@waitroom/common@1.252.4) (2025-04-30)


### Bug Fixes

* player credentials, package versions ([8606cf0](https://github.com/Waitroom/waitroom/commit/8606cf0ae3be75b089ac8d155709ff705ba5be99))
* release versions ([3d66654](https://github.com/Waitroom/waitroom/commit/3d66654f6ca27fe13eb1a4fb7afc46f39da0d0fd))

## 1.252.3 (2025-04-30)

### Features

* lobbies ([#2308](https://github.com/Waitroom/rumi.ai/issues/2308)) ([7ba45cf](https://github.com/Waitroom/waitroom/commit/7ba45cf4db4cb73e5893c435c11ee5315aa4f547)), closes [#2498](https://github.com/Waitroom/rumi.ai/issues/2498) [#2508](https://github.com/Waitroom/rumi.ai/issues/2508) [#2517](https://github.com/Waitroom/rumi.ai/issues/2517) [#2525](https://github.com/Waitroom/rumi.ai/issues/2525) [#2527](https://github.com/Waitroom/rumi.ai/issues/2527) [#2529](https://github.com/Waitroom/rumi.ai/issues/2529) [#2536](https://github.com/Waitroom/rumi.ai/issues/2536) [#2539](https://github.com/Waitroom/rumi.ai/issues/2539) [#2542](https://github.com/Waitroom/rumi.ai/issues/2542) [#2543](https://github.com/Waitroom/rumi.ai/issues/2543) [#2547](https://github.com/Waitroom/rumi.ai/issues/2547) [#2562](https://github.com/Waitroom/rumi.ai/issues/2562) [#2567](https://github.com/Waitroom/rumi.ai/issues/2567) [#2569](https://github.com/Waitroom/rumi.ai/issues/2569) [#2571](https://github.com/Waitroom/rumi.ai/issues/2571) [#2573](https://github.com/Waitroom/rumi.ai/issues/2573) [#2579](https://github.com/Waitroom/rumi.ai/issues/2579) [#2581](https://github.com/Waitroom/rumi.ai/issues/2581) [#2598](https://github.com/Waitroom/rumi.ai/issues/2598) [#2604](https://github.com/Waitroom/rumi.ai/issues/2604) [#2601](https://github.com/Waitroom/rumi.ai/issues/2601) [#2607](https://github.com/Waitroom/rumi.ai/issues/2607) [#2608](https://github.com/Waitroom/rumi.ai/issues/2608)
* lobbies access refactor WEB-2629 ([#2636](https://github.com/Waitroom/rumi.ai/issues/2636)) ([bb6f4ba](https://github.com/Waitroom/waitroom/commit/bb6f4bacd68cc00c49894855a6eaeb2428bfad5d))
* lobby branch libs updates ([#2600](https://github.com/Waitroom/rumi.ai/issues/2600)) ([6f121c6](https://github.com/Waitroom/waitroom/commit/6f121c628ef868e64b2c55f3bd782b1bbd7fbb87))
* meeting selector ([#2538](https://github.com/Waitroom/rumi.ai/issues/2538)) ([b7c072b](https://github.com/Waitroom/waitroom/commit/b7c072b2f57c92f6683e5938d33603257ba732eb))
* troubleshoot modal ([#2602](https://github.com/Waitroom/rumi.ai/issues/2602)) ([a462a17](https://github.com/Waitroom/waitroom/commit/a462a171f3ca18d5f88c24e562f42afee60839fc))
* updated emoji reactions ([8ba8efe](https://github.com/Waitroom/waitroom/commit/8ba8efea5ea15c739ce28fbe8d66348650720496))


### Bug Fixes

* dashboard card team memory badge ui ([#2587](https://github.com/Waitroom/rumi.ai/issues/2587)) ([88ffdf2](https://github.com/Waitroom/waitroom/commit/88ffdf2c4e18a82ade8c5206e80eb8a0bc978bca))
* disable integrations query if unauthed or guest ([#2632](https://github.com/Waitroom/rumi.ai/issues/2632)) ([aa30c52](https://github.com/Waitroom/waitroom/commit/aa30c522d11fef66d6f956ec2b1334e414c76a92))
* fixed missing plan config ([#2609](https://github.com/Waitroom/rumi.ai/issues/2609)) ([91b5872](https://github.com/Waitroom/waitroom/commit/91b58722b82df6d97a9ce8e3ada4a448a5c67dc7))
* translation nesting ([97d7f19](https://github.com/Waitroom/waitroom/commit/97d7f193b077952831445d83d16578948bca627f))

## [1.252.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.252.2...@waitroom/common@1.252.3) (2025-04-25)

## [1.252.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.252.1...@waitroom/common@1.252.2) (2025-04-25)


### Bug Fixes

* translation nesting ([97d7f19](https://github.com/Waitroom/waitroom/commit/97d7f193b077952831445d83d16578948bca627f))

## [1.252.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.252.0...@waitroom/common@1.252.1) (2025-04-25)

## [1.252.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.251.1...@waitroom/common@1.252.0) (2025-04-17)


### Features

* lobbies access refactor WEB-2629 ([#2636](https://github.com/Waitroom/rumi.ai/issues/2636)) ([bb6f4ba](https://github.com/Waitroom/waitroom/commit/bb6f4bacd68cc00c49894855a6eaeb2428bfad5d))

## [1.251.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.251.0...@waitroom/common@1.251.1) (2025-04-15)


### Bug Fixes

* disable integrations query if unauthed or guest ([#2632](https://github.com/Waitroom/rumi.ai/issues/2632)) ([aa30c52](https://github.com/Waitroom/waitroom/commit/aa30c522d11fef66d6f956ec2b1334e414c76a92))

## [1.251.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.250.0...@waitroom/common@1.251.0) (2025-04-10)


### Features

* updated emoji reactions ([8ba8efe](https://github.com/Waitroom/waitroom/commit/8ba8efea5ea15c739ce28fbe8d66348650720496))

## [1.250.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.249.0...@waitroom/common@1.250.0) (2025-04-10)


### Features

* lobbies ([#2308](https://github.com/Waitroom/rumi.ai/issues/2308)) ([7ba45cf](https://github.com/Waitroom/waitroom/commit/7ba45cf4db4cb73e5893c435c11ee5315aa4f547)), closes [#2498](https://github.com/Waitroom/rumi.ai/issues/2498) [#2508](https://github.com/Waitroom/rumi.ai/issues/2508) [#2517](https://github.com/Waitroom/rumi.ai/issues/2517) [#2525](https://github.com/Waitroom/rumi.ai/issues/2525) [#2527](https://github.com/Waitroom/rumi.ai/issues/2527) [#2529](https://github.com/Waitroom/rumi.ai/issues/2529) [#2536](https://github.com/Waitroom/rumi.ai/issues/2536) [#2539](https://github.com/Waitroom/rumi.ai/issues/2539) [#2542](https://github.com/Waitroom/rumi.ai/issues/2542) [#2543](https://github.com/Waitroom/rumi.ai/issues/2543) [#2547](https://github.com/Waitroom/rumi.ai/issues/2547) [#2562](https://github.com/Waitroom/rumi.ai/issues/2562) [#2567](https://github.com/Waitroom/rumi.ai/issues/2567) [#2569](https://github.com/Waitroom/rumi.ai/issues/2569) [#2571](https://github.com/Waitroom/rumi.ai/issues/2571) [#2573](https://github.com/Waitroom/rumi.ai/issues/2573) [#2579](https://github.com/Waitroom/rumi.ai/issues/2579) [#2581](https://github.com/Waitroom/rumi.ai/issues/2581) [#2598](https://github.com/Waitroom/rumi.ai/issues/2598) [#2604](https://github.com/Waitroom/rumi.ai/issues/2604) [#2601](https://github.com/Waitroom/rumi.ai/issues/2601) [#2607](https://github.com/Waitroom/rumi.ai/issues/2607) [#2608](https://github.com/Waitroom/rumi.ai/issues/2608)

## [1.249.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.248.1...@waitroom/common@1.249.0) (2025-04-07)


### Features

* troubleshoot modal ([#2602](https://github.com/Waitroom/rumi.ai/issues/2602)) ([a462a17](https://github.com/Waitroom/waitroom/commit/a462a171f3ca18d5f88c24e562f42afee60839fc))

## [1.248.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.248.0...@waitroom/common@1.248.1) (2025-04-07)


### Bug Fixes

* fixed missing plan config ([#2609](https://github.com/Waitroom/rumi.ai/issues/2609)) ([91b5872](https://github.com/Waitroom/waitroom/commit/91b58722b82df6d97a9ce8e3ada4a448a5c67dc7))

## [1.248.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.247.2...@waitroom/common@1.248.0) (2025-04-02)


### Features

* lobby branch libs updates ([#2600](https://github.com/Waitroom/rumi.ai/issues/2600)) ([6f121c6](https://github.com/Waitroom/waitroom/commit/6f121c628ef868e64b2c55f3bd782b1bbd7fbb87))

## [1.247.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.247.1...@waitroom/common@1.247.2) (2025-03-28)

## [1.247.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.247.0...@waitroom/common@1.247.1) (2025-03-27)


### Bug Fixes

* dashboard card team memory badge ui ([#2587](https://github.com/Waitroom/rumi.ai/issues/2587)) ([88ffdf2](https://github.com/Waitroom/waitroom/commit/88ffdf2c4e18a82ade8c5206e80eb8a0bc978bca))

## [1.247.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.246.7...@waitroom/common@1.247.0) (2025-03-27)


### Features

* meeting selector ([#2538](https://github.com/Waitroom/rumi.ai/issues/2538)) ([b7c072b](https://github.com/Waitroom/waitroom/commit/b7c072b2f57c92f6683e5938d33603257ba732eb))

## [1.246.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.246.6...@waitroom/common@1.246.7) (2025-03-27)


### Bug Fixes

* crying emoji ([a4c7b5c](https://github.com/Waitroom/waitroom/commit/a4c7b5cdf0c3df42d18071d783ef068a5f08f9b7))

## [1.246.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.246.5...@waitroom/common@1.246.6) (2025-03-27)


### Bug Fixes

* discover the primary host data ([#2582](https://github.com/Waitroom/rumi.ai/issues/2582)) ([b7d2cb7](https://github.com/Waitroom/waitroom/commit/b7d2cb7f753d171255a1618b5f8e4066577c7d4f))

## [1.246.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.246.4...@waitroom/common@1.246.5) (2025-03-27)

## [1.246.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.246.3...@waitroom/common@1.246.4) (2025-03-26)

## [1.246.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.246.2...@waitroom/common@1.246.3) (2025-03-24)

## [1.246.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.246.1...@waitroom/common@1.246.2) (2025-03-19)

## [1.246.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.246.0...@waitroom/common@1.246.1) (2025-03-18)

## [1.246.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.245.0...@waitroom/common@1.246.0) (2025-03-18)


### Features

* sync lib changes for lobbies onto develop ([#2570](https://github.com/Waitroom/rumi.ai/issues/2570)) ([6f97dd2](https://github.com/Waitroom/waitroom/commit/6f97dd25fe605aca0a4626481dec170b32f286e8))

## [1.245.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.244.2...@waitroom/common@1.245.0) (2025-03-04)


### Features

* lobbies lib package updates ([#2540](https://github.com/Waitroom/rumi.ai/issues/2540)) ([106cebc](https://github.com/Waitroom/waitroom/commit/106cebc838ee1b4a95673638fbb51d62a11436ca))

## [1.244.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.244.1...@waitroom/common@1.244.2) (2025-02-27)


### Bug Fixes

* missing guest reactions ([#2532](https://github.com/Waitroom/rumi.ai/issues/2532)) ([6555488](https://github.com/Waitroom/waitroom/commit/655548883467d7fd2e2d2e2b2f57a14d60246de2))

## [1.244.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.244.0...@waitroom/common@1.244.1) (2025-02-25)


### Bug Fixes

* camera effect issue #WEB-2518 ([12b243d](https://github.com/Waitroom/waitroom/commit/12b243d8544033375febbe031cef12567d43c372)), closes [#WEB-2518](https://github.com/Waitroom/rumi.ai/issues/WEB-2518)

## [1.244.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.243.3...@waitroom/common@1.244.0) (2025-02-22)


### Features

* web 2470 improve ai feed performance ([#2476](https://github.com/Waitroom/rumi.ai/issues/2476)) ([c561e16](https://github.com/Waitroom/waitroom/commit/c561e1625dc69472234d62573ac45623c5af1bd2))

## [1.243.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.243.2...@waitroom/common@1.243.3) (2025-02-21)

## [1.243.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.243.1...@waitroom/common@1.243.2) (2025-02-20)


### Bug Fixes

* check user belongs to a team ([#2516](https://github.com/Waitroom/rumi.ai/issues/2516)) ([82aac23](https://github.com/Waitroom/waitroom/commit/82aac23c8d59a17a4c16f8313ada1286e1336612))

## [1.243.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.243.0...@waitroom/common@1.243.1) (2025-02-18)

## [1.243.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.242.5...@waitroom/common@1.243.0) (2025-02-17)


### Features

* WEB-2484 add host controls for team memory visibility during meetings ([#2470](https://github.com/Waitroom/rumi.ai/issues/2470)) ([f7ae25f](https://github.com/Waitroom/waitroom/commit/f7ae25f58048413ee2a8d69b21382e770de4e95e))

## [1.242.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.242.4...@waitroom/common@1.242.5) (2025-02-17)

## [1.242.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.242.3...@waitroom/common@1.242.4) (2025-02-10)

## [1.242.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.242.2...@waitroom/common@1.242.3) (2025-02-07)

## [1.242.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.242.1...@waitroom/common@1.242.2) (2025-02-07)

## [1.242.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.242.0...@waitroom/common@1.242.1) (2025-02-07)

## [1.242.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.241.7...@waitroom/common@1.242.0) (2025-02-06)


### Features

* WEB-2402 enable user to bind crm record when scheduling a meeting ([#2457](https://github.com/Waitroom/rumi.ai/issues/2457)) ([743fb24](https://github.com/Waitroom/waitroom/commit/743fb241f8429c9f4f1e6ae295e8fa7fb9cbcee0))

## [1.241.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.241.6...@waitroom/common@1.241.7) (2025-02-06)


### Bug Fixes

* small issue on resetting active session ([ff7d8bc](https://github.com/Waitroom/waitroom/commit/ff7d8bc9404fe4049da40afe5d658ec629ec3a8c))

## [1.241.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.241.5...@waitroom/common@1.241.6) (2025-02-05)

## [1.241.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.241.4...@waitroom/common@1.241.5) (2025-02-03)

## [1.241.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.241.3...@waitroom/common@1.241.4) (2025-02-03)

## [1.241.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.241.2...@waitroom/common@1.241.3) (2025-01-29)


### Bug Fixes

* WEB-2489 remove clickcease tracking code ([#2459](https://github.com/Waitroom/rumi.ai/issues/2459)) ([1c54eb4](https://github.com/Waitroom/waitroom/commit/1c54eb468f0049a07c392793122b12c0789cb63e))

## [1.241.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.241.1...@waitroom/common@1.241.2) (2025-01-27)


### Bug Fixes

* web 2462 presence on prescheduled screen is sometimes failing ([#2448](https://github.com/Waitroom/rumi.ai/issues/2448)) ([162c774](https://github.com/Waitroom/waitroom/commit/162c774e5ff506e701277f69c9ad47540a7f2e22))

## [1.241.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.241.0...@waitroom/common@1.241.1) (2025-01-23)


### Bug Fixes

* initialized flag ([#2454](https://github.com/Waitroom/rumi.ai/issues/2454)) ([a147e1e](https://github.com/Waitroom/waitroom/commit/a147e1ec4dbc3a834100886da510c00c76c9f50b))

## [1.241.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.240.2...@waitroom/common@1.241.0) (2025-01-22)


### Features

* **notetaker:** Adds Notetaker UI Cards ([#2435](https://github.com/Waitroom/rumi.ai/issues/2435)) ([7255c07](https://github.com/Waitroom/waitroom/commit/7255c07db048a44f08cbf57f62f30426921390ce))

## [1.240.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.240.1...@waitroom/common@1.240.2) (2025-01-21)

## [1.240.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.240.0...@waitroom/common@1.240.1) (2025-01-20)


### Reverts

* "feat: web 2462 presence on prescheduled screen is sometimes failing ([#2445](https://github.com/Waitroom/rumi.ai/issues/2445))" ([e710c29](https://github.com/Waitroom/waitroom/commit/e710c29851de8231c421af0260f53122906fde8d))

## [1.240.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.239.0...@waitroom/common@1.240.0) (2025-01-20)


### Features

* web 2462 presence on prescheduled screen is sometimes failing ([#2445](https://github.com/Waitroom/rumi.ai/issues/2445)) ([5a49bb3](https://github.com/Waitroom/waitroom/commit/5a49bb35e62775ff470931dcac0e5fcfd3fa387b))

## [1.239.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.238.0...@waitroom/common@1.239.0) (2025-01-17)


### Features

* update livekit ([e6bc163](https://github.com/Waitroom/waitroom/commit/e6bc163ef1b5fdae4c3b3f350bb0adcd593c642b))

## [1.238.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.237.3...@waitroom/common@1.238.0) (2025-01-16)


### Features

* meeting memory redesign, added container to sessions page ([17d63a5](https://github.com/Waitroom/waitroom/commit/17d63a5ab8ff65cf61a39dd5712a68894165d33a))

## [1.237.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.237.2...@waitroom/common@1.237.3) (2025-01-14)


### Reverts

* Revert "fix: race condition between onPatch and react-query queryFn promise resolve (#2444)" ([32fee7d](https://github.com/Waitroom/waitroom/commit/32fee7ddd6bc27bd3dca037ff96a8500217da07c)), closes [#2444](https://github.com/Waitroom/rumi.ai/issues/2444)

## [1.237.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.237.1...@waitroom/common@1.237.2) (2025-01-14)


### Bug Fixes

* race condition between onPatch and react-query queryFn promise resolve ([#2444](https://github.com/Waitroom/rumi.ai/issues/2444)) ([d82e57d](https://github.com/Waitroom/waitroom/commit/d82e57db9cb9563df7b2be4aefe3939b1d031d8d))

## [1.237.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.237.0...@waitroom/common@1.237.1) (2025-01-10)

## [1.237.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.236.5...@waitroom/common@1.237.0) (2025-01-09)


### Features

* added auth refresh action ([af87a5c](https://github.com/Waitroom/waitroom/commit/af87a5c767c33d462d4163c925012c3c1875033f))

## [1.236.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.236.4...@waitroom/common@1.236.5) (2025-01-08)


### Bug Fixes

* modify ai-stream request payload ([#2437](https://github.com/Waitroom/rumi.ai/issues/2437)) ([25109c5](https://github.com/Waitroom/waitroom/commit/25109c52aa2ec2286b3da88e867fe53da81744c8))

## [1.236.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.236.3...@waitroom/common@1.236.4) (2025-01-08)


### Bug Fixes

* setting session initialized flag with auth ([#2436](https://github.com/Waitroom/rumi.ai/issues/2436)) ([6c48fce](https://github.com/Waitroom/waitroom/commit/6c48fcea6bd0a026a30c231650289b3b706e3619))

## [1.236.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.236.2...@waitroom/common@1.236.3) (2025-01-06)


### Bug Fixes

* session page UI issues ([#2434](https://github.com/Waitroom/rumi.ai/issues/2434)) ([4d26cf7](https://github.com/Waitroom/waitroom/commit/4d26cf7f51bb2e7b13e7ec401f1e89c3bfc45c7b))

## [1.236.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.236.1...@waitroom/common@1.236.2) (2025-01-06)

## [1.236.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.236.0...@waitroom/common@1.236.1) (2024-12-31)

## [1.236.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.235.5...@waitroom/common@1.236.0) (2024-12-31)


### Features

* update session page meta title ([#2429](https://github.com/Waitroom/rumi.ai/issues/2429)) ([7a13769](https://github.com/Waitroom/waitroom/commit/7a13769ea447a31d536f71adb297c8032eff429f))

## [1.235.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.235.4...@waitroom/common@1.235.5) (2024-12-18)

## [1.235.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.235.3...@waitroom/common@1.235.4) (2024-12-11)

## [1.235.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.235.2...@waitroom/common@1.235.3) (2024-12-11)


### Bug Fixes

* braid service retry logic ([2309de2](https://github.com/Waitroom/waitroom/commit/2309de2e22adb8d310816405f2fe73c1af157173))

## [1.235.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.235.1...@waitroom/common@1.235.2) (2024-12-10)

## [1.235.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.235.0...@waitroom/common@1.235.1) (2024-12-09)

## [1.235.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.234.0...@waitroom/common@1.235.0) (2024-12-06)


### Features

* updated effects UI ([#2402](https://github.com/Waitroom/rumi.ai/issues/2402)) ([aa72e48](https://github.com/Waitroom/waitroom/commit/aa72e488ded21b4025f7c8b103d87b3552e09baf))

## [1.234.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.233.3...@waitroom/common@1.234.0) (2024-12-05)


### Features

* web 2348 update pricing table to include crm ([176d9e1](https://github.com/Waitroom/waitroom/commit/176d9e11a6ab30aae9e25c4115ef93d6ae594571))

## [1.233.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.233.2...@waitroom/common@1.233.3) (2024-12-04)

## [1.233.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.233.1...@waitroom/common@1.233.2) (2024-12-04)

## [1.233.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.233.0...@waitroom/common@1.233.1) (2024-12-04)


### Bug Fixes

* mm on staging ([#2394](https://github.com/Waitroom/rumi.ai/issues/2394)) ([2954f63](https://github.com/Waitroom/waitroom/commit/2954f63c554fd1adcfe87a5c384fe61b949c8912))

## [1.233.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.232.2...@waitroom/common@1.233.0) (2024-12-03)


### Features

* improvements to MM on end screen and recordings screen ([#2382](https://github.com/Waitroom/rumi.ai/issues/2382)) ([6559beb](https://github.com/Waitroom/waitroom/commit/6559bebb791719067418d683b05b62674cae3144))

## [1.232.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.232.1...@waitroom/common@1.232.2) (2024-12-02)

## [1.232.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.232.0...@waitroom/common@1.232.1) (2024-12-02)


### Bug Fixes

* undefined track check, refactored cam and mic init, updated packages ([81fb7c1](https://github.com/Waitroom/waitroom/commit/81fb7c1bc7ce6e713272da1632bf86faea0e849c))

## [1.232.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.231.0...@waitroom/common@1.232.0) (2024-11-29)


### Features

* adds sending tz body field in ask ai reqs ([#2377](https://github.com/Waitroom/rumi.ai/issues/2377)) ([f840b3e](https://github.com/Waitroom/waitroom/commit/f840b3e15e81c6e51a03c6c39772fd4f8950785f))

## [1.231.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.230.2...@waitroom/common@1.231.0) (2024-11-28)


### Features

* effects sdk ([#2372](https://github.com/Waitroom/rumi.ai/issues/2372)) ([fe8a8fe](https://github.com/Waitroom/waitroom/commit/fe8a8fe237f79bd663980c6c8fdbb937858e8d1a))

## [1.230.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.230.1...@waitroom/common@1.230.2) (2024-11-26)


### Bug Fixes

* failing braid patch and race condition ([#2369](https://github.com/Waitroom/rumi.ai/issues/2369)) ([88aa3c4](https://github.com/Waitroom/waitroom/commit/88aa3c46af79aff3d3565f47dc46eabfd148be7e))

## [1.230.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.230.0...@waitroom/common@1.230.1) (2024-11-21)

## [1.230.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.229.4...@waitroom/common@1.230.0) (2024-11-19)


### Features

* added outlook calendar to integrations ([#2352](https://github.com/Waitroom/rumi.ai/issues/2352)) ([78a20b6](https://github.com/Waitroom/waitroom/commit/78a20b6c3319f7fb334472dbc0d607d74ba125ac))

## [1.229.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.229.3...@waitroom/common@1.229.4) (2024-11-19)

## [1.229.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.229.2...@waitroom/common@1.229.3) (2024-11-18)


### Bug Fixes

* enhance MeetingMemory component with loading states and refetching ([#2349](https://github.com/Waitroom/rumi.ai/issues/2349)) ([28f4782](https://github.com/Waitroom/waitroom/commit/28f4782dd8b131581b88edc2a501cb3165d69d42))

## [1.229.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.229.1...@waitroom/common@1.229.2) (2024-11-14)


### Bug Fixes

* refetch connection query instead of invalidation ([#2344](https://github.com/Waitroom/rumi.ai/issues/2344)) ([e2e53ab](https://github.com/Waitroom/waitroom/commit/e2e53ab114591c026526484d40664a16800c8596))

## [1.229.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.229.0...@waitroom/common@1.229.1) (2024-11-12)


### Bug Fixes

* translations ([#2342](https://github.com/Waitroom/rumi.ai/issues/2342)) ([8a8a374](https://github.com/Waitroom/waitroom/commit/8a8a3742829ffb6f39eebd3e11db5a547d9711ad))

## [1.229.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.228.1...@waitroom/common@1.229.0) (2024-11-12)


### Features

* crm contact cards ([#2246](https://github.com/Waitroom/rumi.ai/issues/2246)) ([c18da67](https://github.com/Waitroom/waitroom/commit/c18da675545e2a30c18a1b5d432270d29f2dea8a))

## [1.228.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.228.0...@waitroom/common@1.228.1) (2024-11-11)

## [1.228.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.227.3...@waitroom/common@1.228.0) (2024-11-05)


### Features

* **subscriptions:** add support for 30 day trial ([#2328](https://github.com/Waitroom/rumi.ai/issues/2328)) ([a20e0ad](https://github.com/Waitroom/waitroom/commit/a20e0ad212ed9fc76395e94ea951982826a8e30b))

## [1.227.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.227.2...@waitroom/common@1.227.3) (2024-11-01)


### Bug Fixes

* logger shared instance ([#2320](https://github.com/Waitroom/rumi.ai/issues/2320)) ([86d7b1b](https://github.com/Waitroom/waitroom/commit/86d7b1b29c08d8f31526595019f620874927e014))

## [1.227.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.227.1...@waitroom/common@1.227.2) (2024-10-31)

## [1.227.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.227.0...@waitroom/common@1.227.1) (2024-10-24)


### Bug Fixes

* session timer ([#2306](https://github.com/Waitroom/rumi.ai/issues/2306)) ([fb7240f](https://github.com/Waitroom/waitroom/commit/fb7240fd4a727e1db0a5d9774752233d6cb802c9))

## [1.227.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.226.1...@waitroom/common@1.227.0) (2024-10-24)


### Features

* team support  ([#2223](https://github.com/Waitroom/rumi.ai/issues/2223)) ([a80b450](https://github.com/Waitroom/waitroom/commit/a80b450dca98cfa78c60634dec5eea631538a7d9))

## [1.226.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.226.0...@waitroom/common@1.226.1) (2024-10-23)


### Bug Fixes

* show a toast warning when an expired connection found ([#2300](https://github.com/Waitroom/rumi.ai/issues/2300)) ([86a6819](https://github.com/Waitroom/waitroom/commit/86a68195e56889bf11abaa449ec68afdcbc19f75))

## [1.226.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.225.0...@waitroom/common@1.226.0) (2024-10-22)


### Features

* rerelease packages ([573ab7c](https://github.com/Waitroom/waitroom/commit/573ab7c131b0f5c9dab2872e706ae7d677e46d60))

## [1.225.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.224.6...@waitroom/common@1.225.0) (2024-10-22)


### Features

* rerelease all packages ([ec0b06c](https://github.com/Waitroom/waitroom/commit/ec0b06cd0e5795a9bfa3f19bc6e1c2975975b346))

## [1.224.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.224.5...@waitroom/common@1.224.6) (2024-10-22)

## [1.224.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.224.4...@waitroom/common@1.224.5) (2024-10-22)


### Bug Fixes

* add state and code challenge params to auth requests ([33ce9ec](https://github.com/Waitroom/waitroom/commit/33ce9ecf8d684c0ff32d2cb6deaff59ca347d234))

## [1.224.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.224.3...@waitroom/common@1.224.4) (2024-10-21)


### Bug Fixes

* session timer ([#2294](https://github.com/Waitroom/rumi.ai/issues/2294)) ([6ab2336](https://github.com/Waitroom/waitroom/commit/6ab2336ff18648635dd2959dbcb6d128be9d8b8b))

## [1.224.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.224.2...@waitroom/common@1.224.3) (2024-10-21)


### Bug Fixes

* request approval cache updating ([#2291](https://github.com/Waitroom/rumi.ai/issues/2291)) ([6e5ec89](https://github.com/Waitroom/waitroom/commit/6e5ec898d00c372f836fbd65a9a2d9ee047ee528))

## [1.224.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.224.1...@waitroom/common@1.224.2) (2024-10-21)


### Bug Fixes

* common lib imported libraries ([31c3709](https://github.com/Waitroom/waitroom/commit/31c3709737eb61d6fc02c80719483056280ce1bc))

## [1.224.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.224.0...@waitroom/common@1.224.1) (2024-10-18)


### Bug Fixes

* meeting memory threads popup ([ecb5842](https://github.com/Waitroom/waitroom/commit/ecb58424582cd79bb8b4b88d3c823b2353de3420))

## [1.224.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.223.0...@waitroom/common@1.224.0) (2024-10-17)


### Features

* meeting memory threads UI update ([#2284](https://github.com/Waitroom/rumi.ai/issues/2284)) ([07ba86a](https://github.com/Waitroom/waitroom/commit/07ba86afde2a70bfa173bd12a56927109ec21e94))

## [1.223.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.222.2...@waitroom/common@1.223.0) (2024-10-17)


### Features

* reenable personalized suggestions ([#2280](https://github.com/Waitroom/rumi.ai/issues/2280)) ([70884fd](https://github.com/Waitroom/waitroom/commit/70884fd5b13c788bf5ad8fe64f0d50cf210c43bf))
* web 2256 new threads are not being created on suggestion click ([#2278](https://github.com/Waitroom/rumi.ai/issues/2278)) ([d4536a0](https://github.com/Waitroom/waitroom/commit/d4536a0ff26686b504070b21eb767055bdcee7d8))

## [1.222.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.222.1...@waitroom/common@1.222.2) (2024-10-11)


### Bug Fixes

* otp error message ([#2273](https://github.com/Waitroom/rumi.ai/issues/2273)) ([5828b40](https://github.com/Waitroom/waitroom/commit/5828b40138361b022b411c7b1c260b1d46225d13))

## [1.222.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.222.0...@waitroom/common@1.222.1) (2024-10-10)

## [1.222.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.221.2...@waitroom/common@1.222.0) (2024-10-09)


### Features

* add params to useLocalParticipantHasJoined props ([#2271](https://github.com/Waitroom/rumi.ai/issues/2271)) ([4ad4392](https://github.com/Waitroom/waitroom/commit/4ad439240f8b99f38df2da26c6a4ab1633392a7c))

## [1.221.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.221.1...@waitroom/common@1.221.2) (2024-10-09)

## [1.221.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.221.0...@waitroom/common@1.221.1) (2024-10-08)


### Bug Fixes

* off the record visibility ([462c150](https://github.com/Waitroom/waitroom/commit/462c15072a5a678276f5af6d062b23eae7392f25))

## [1.221.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.220.7...@waitroom/common@1.221.0) (2024-10-07)


### Features

* preload virtual background image ([77d92fe](https://github.com/Waitroom/waitroom/commit/77d92fee7c82eb85cc70dc2950b8036840c88087))

## [1.220.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.220.6...@waitroom/common@1.220.7) (2024-10-07)


### Bug Fixes

* json patching, updated release summary prompt ([cbb325a](https://github.com/Waitroom/waitroom/commit/cbb325a78bc0f8920ea9b98edf2ef22f6406b84b))

## [1.220.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.220.5...@waitroom/common@1.220.6) (2024-10-07)


### Bug Fixes

* improve local participant hook logic ([5419fca](https://github.com/Waitroom/waitroom/commit/5419fca02af22b18a1d4157983e9458d272a3803))

## [1.220.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.220.4...@waitroom/common@1.220.5) (2024-10-07)


### Bug Fixes

* release to staging ([6bca1f0](https://github.com/Waitroom/waitroom/commit/6bca1f056ec80bd941d019b3faf96d0b35fe199f))

## [1.220.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.220.3...@waitroom/common@1.220.4) (2024-10-07)

## [1.220.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.220.2...@waitroom/common@1.220.3) (2024-10-07)


### Bug Fixes

* guest joining indicator issues ([#2253](https://github.com/Waitroom/rumi.ai/issues/2253)) ([561b90b](https://github.com/Waitroom/waitroom/commit/561b90b8b9836a64daf4ce5f749f035209ad9b9d))

## [1.220.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.220.1...@waitroom/common@1.220.2) (2024-10-04)


### Bug Fixes

* stream ui, refactored redux helpers ([2714787](https://github.com/Waitroom/waitroom/commit/2714787dc33f0d3e92b9f004ff08fd5e1cebba47))

## [1.220.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.220.0...@waitroom/common@1.220.1) (2024-10-03)


### Bug Fixes

* session saving ([2f411e2](https://github.com/Waitroom/waitroom/commit/2f411e27576ee9e2c971a97e3ffdb61ae51b42d2))

## [1.220.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.219.2...@waitroom/common@1.220.0) (2024-10-03)


### Features

* adds new inactive session err code ([#2248](https://github.com/Waitroom/rumi.ai/issues/2248)) ([b226b8b](https://github.com/Waitroom/waitroom/commit/b226b8bc20c1dd4c001bdf8617740d19662487c6))

## [1.219.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.219.1...@waitroom/common@1.219.2) (2024-10-03)


### Bug Fixes

* profile links ([d163119](https://github.com/Waitroom/waitroom/commit/d163119890886e8e5c7e03afb6a42c027018153f))

## [1.219.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.219.0...@waitroom/common@1.219.1) (2024-10-01)


### Bug Fixes

* re rendering useQueryData hook ([#2245](https://github.com/Waitroom/rumi.ai/issues/2245)) ([f5a8eff](https://github.com/Waitroom/waitroom/commit/f5a8eff641a5218cb8d32ade5947522ac28e91df))

## [1.219.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.218.0...@waitroom/common@1.219.0) (2024-10-01)


### Features

* project frictionless guest access ([#2233](https://github.com/Waitroom/rumi.ai/issues/2233)) ([948ddca](https://github.com/Waitroom/waitroom/commit/948ddca1047ed6394c9b5744bd4a4e3d4295636d))

## [1.218.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.217.0...@waitroom/common@1.218.0) (2024-10-01)


### Features

* web 2177 allow users to browse their previous meeting memory threads ([#2224](https://github.com/Waitroom/rumi.ai/issues/2224)) ([9a3c11d](https://github.com/Waitroom/waitroom/commit/9a3c11dcf18a73785608dbd9c09d1927399bf917))

## [1.217.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.216.4...@waitroom/common@1.217.0) (2024-10-01)


### Features

* WEB-2205 bradify get all bindings endpoint ([#2241](https://github.com/Waitroom/rumi.ai/issues/2241)) ([04d3e44](https://github.com/Waitroom/waitroom/commit/04d3e4456e1c8cce753fb83ccd8f75fe11bf590b))

## [1.216.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.216.3...@waitroom/common@1.216.4) (2024-09-27)

## [1.216.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.216.2...@waitroom/common@1.216.3) (2024-09-13)


### Bug Fixes

* hubspot data models ([#2230](https://github.com/Waitroom/rumi.ai/issues/2230)) ([b99861e](https://github.com/Waitroom/waitroom/commit/b99861e0d24fac597742c44fd34eed0947e4b388))

## [1.216.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.216.1...@waitroom/common@1.216.2) (2024-09-12)


### Bug Fixes

* connecting to crm providers ([df38eff](https://github.com/Waitroom/waitroom/commit/df38effa220886727145d4b5ab4aaa5659647ddf))

## [1.216.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.216.0...@waitroom/common@1.216.1) (2024-09-12)

## [1.216.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.215.2...@waitroom/common@1.216.0) (2024-09-11)


### Features

* WEB-2183 create new components for hubspot integration ([#2205](https://github.com/Waitroom/rumi.ai/issues/2205)) ([6636529](https://github.com/Waitroom/waitroom/commit/663652973dfd117ab9bf8d728830181ee498512b))

## [1.215.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.215.1...@waitroom/common@1.215.2) (2024-09-10)

## [1.215.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.215.0...@waitroom/common@1.215.1) (2024-09-10)


### Bug Fixes

* re-added braid connection for subscribing to salesforce event data ([#2226](https://github.com/Waitroom/rumi.ai/issues/2226)) ([530caa2](https://github.com/Waitroom/waitroom/commit/530caa2bba85e34b1a98878377551f1e37b33526))

## [1.215.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.214.3...@waitroom/common@1.215.0) (2024-09-09)


### Features

* added faq title ([8772335](https://github.com/Waitroom/waitroom/commit/8772335273abde475a124123042d0e1acd245ec5))

## [1.214.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.214.2...@waitroom/common@1.214.3) (2024-09-04)


### Bug Fixes

* dev tools services list ([25cfac4](https://github.com/Waitroom/waitroom/commit/25cfac4f4cbfdaa1a758b3ad5e6bcec151860438))

## [1.214.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.214.1...@waitroom/common@1.214.2) (2024-09-04)

## [1.214.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.214.0...@waitroom/common@1.214.1) (2024-09-04)


### Bug Fixes

* braid models, updated logger ([0d7d3a7](https://github.com/Waitroom/waitroom/commit/0d7d3a722379da5a7f0bfba2b25513b20be9a078))
* peermetrics missing env key, braid types ([b391861](https://github.com/Waitroom/waitroom/commit/b39186159bd0f98c66dfda000ddc71077f1dc18b))

## [1.214.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.213.0...@waitroom/common@1.214.0) (2024-09-04)


### Features

* adds peermetric SDK to Livekit room ([#2197](https://github.com/Waitroom/rumi.ai/issues/2197)) ([5411842](https://github.com/Waitroom/waitroom/commit/541184245d04426e527ba53d2c7f4f6eeb9d0d08))

## [1.213.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.212.1...@waitroom/common@1.213.0) (2024-09-04)


### Features

* refactor salesforce component to occupy more crm apps ([#2200](https://github.com/Waitroom/rumi.ai/issues/2200)) ([af2e9ca](https://github.com/Waitroom/waitroom/commit/af2e9cac0a963e299d2b6b4df4430fcaa7024e7c))

## [1.212.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.212.0...@waitroom/common@1.212.1) (2024-09-03)


### Reverts

* braid service change ([a9501d9](https://github.com/Waitroom/waitroom/commit/a9501d9b2fff992c9ec4afb36a97a73310f7b7db))

## [1.212.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.211.0...@waitroom/common@1.212.0) (2024-09-03)


### Features

* personalized suggestions ([#2201](https://github.com/Waitroom/rumi.ai/issues/2201)) ([10fd13d](https://github.com/Waitroom/waitroom/commit/10fd13dd7afdece1feeb56b0b30f0a75949bb8d9))

## [1.211.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.210.0...@waitroom/common@1.211.0) (2024-09-02)


### Features

* replace add event with calndr ([bea5188](https://github.com/Waitroom/waitroom/commit/bea5188e41a364535aebdff76c1be66376d2983c))

## [1.210.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.209.0...@waitroom/common@1.210.0) (2024-08-29)


### Features

* web 2141 braid connections still closing sometimes ([#2195](https://github.com/Waitroom/rumi.ai/issues/2195)) ([632b1e0](https://github.com/Waitroom/waitroom/commit/632b1e0a7c91285015c093801cf87a1a335eafa8))

## [1.209.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.208.5...@waitroom/common@1.209.0) (2024-08-29)


### Features

* meeting memory enabled, pricing ui update ([#2189](https://github.com/Waitroom/rumi.ai/issues/2189)) ([228f5ee](https://github.com/Waitroom/waitroom/commit/228f5eea2bae30fae9a4248faebde1e87312c9f1))

## [1.208.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.208.4...@waitroom/common@1.208.5) (2024-08-28)


### Bug Fixes

* update salesforce caching and refetching strategies ([#2187](https://github.com/Waitroom/rumi.ai/issues/2187)) ([9c21ef1](https://github.com/Waitroom/waitroom/commit/9c21ef19062a7e8eab5dd34af5d64963eab4788a))

## [1.208.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.208.3...@waitroom/common@1.208.4) (2024-08-28)

## [1.208.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.208.2...@waitroom/common@1.208.3) (2024-08-28)


### Bug Fixes

* WEB-2162 UI fixes for salesforce integration ([#2181](https://github.com/Waitroom/rumi.ai/issues/2181)) ([380eeda](https://github.com/Waitroom/waitroom/commit/380eeda6480a32ab0d2cd0c4fedc0dd36bd1a6e9))

## [1.208.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.208.1...@waitroom/common@1.208.2) (2024-08-28)

## [1.208.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.208.0...@waitroom/common@1.208.1) (2024-08-26)


### Bug Fixes

* WEB-2153 even though the salesforce record is deleted it shows data ([#2177](https://github.com/Waitroom/rumi.ai/issues/2177)) ([c351c8f](https://github.com/Waitroom/waitroom/commit/c351c8fbf8617b13e5951b67ce53b27422e52225))

## [1.208.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.207.0...@waitroom/common@1.208.0) (2024-08-26)


### Features

* loading state ([#2160](https://github.com/Waitroom/rumi.ai/issues/2160)) ([10841f9](https://github.com/Waitroom/waitroom/commit/10841f9811b8cf4973e183a9ae7a31fb9dc039b2))

## [1.207.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.206.3...@waitroom/common@1.207.0) (2024-08-23)


### Features

* use braid to subscribe to latest SF event data ([#2175](https://github.com/Waitroom/rumi.ai/issues/2175)) ([9e1a1b5](https://github.com/Waitroom/waitroom/commit/9e1a1b53982fae14030061226ea3761bed0e7da8))

## [1.206.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.206.2...@waitroom/common@1.206.3) (2024-08-23)


### Bug Fixes

* listing contacts ([#2174](https://github.com/Waitroom/rumi.ai/issues/2174)) ([b6c3ba7](https://github.com/Waitroom/waitroom/commit/b6c3ba779ee2bc225371297a8f8f56218173522d))

## [1.206.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.206.1...@waitroom/common@1.206.2) (2024-08-22)


### Bug Fixes

* deploy ([4457654](https://github.com/Waitroom/waitroom/commit/4457654e51e8a24d69f8e6ce1438ad3d859c3c24))

## [1.206.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.206.0...@waitroom/common@1.206.1) (2024-08-22)


### Bug Fixes

* missing value ([44006e6](https://github.com/Waitroom/waitroom/commit/44006e62605e7fe84acdbfb048bc7675ed5640c4))

## [1.206.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.205.6...@waitroom/common@1.206.0) (2024-08-21)


### Features

* WEB-2073 sales force button ([#2147](https://github.com/Waitroom/rumi.ai/issues/2147)) ([bd5d3b4](https://github.com/Waitroom/waitroom/commit/bd5d3b4b8256b9aa8971f9643dc0665293508bd6))


### Bug Fixes

* trigger release ([7c0b515](https://github.com/Waitroom/waitroom/commit/7c0b515f9722bf90b2a36efa1bae0f463264fa72))

## [1.205.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.205.5...@waitroom/common@1.205.6) (2024-08-20)

## [1.205.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.205.4...@waitroom/common@1.205.5) (2024-08-20)

## [1.205.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.205.3...@waitroom/common@1.205.4) (2024-08-17)

## [1.205.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.205.2...@waitroom/common@1.205.3) (2024-08-15)

## [1.205.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.205.1...@waitroom/common@1.205.2) (2024-08-15)


### Bug Fixes

* tests ([07ecc97](https://github.com/Waitroom/waitroom/commit/07ecc97c81ab4a999e2caf21227f1febee530dfd))

## [1.205.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.205.0...@waitroom/common@1.205.1) (2024-08-15)


### Bug Fixes

* session access modal ([deb0ba8](https://github.com/Waitroom/waitroom/commit/deb0ba8a35f03b70d61f558de4bd18366b861b3a))

## [1.205.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.204.0...@waitroom/common@1.205.0) (2024-08-15)


### Features

* braid retry for 401 ([d364c41](https://github.com/Waitroom/waitroom/commit/d364c41a6689861f217fdb22b0acda049a25e123))

## [1.204.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.203.0...@waitroom/common@1.204.0) (2024-08-14)


### Features

* mm streaming ([#2148](https://github.com/Waitroom/rumi.ai/issues/2148)) ([5943da9](https://github.com/Waitroom/waitroom/commit/5943da912d1708a15cf8b45a48913f1d2d952d7d))

## [1.203.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.202.10...@waitroom/common@1.203.0) (2024-08-14)


### Features

* quality selector ([49ce726](https://github.com/Waitroom/waitroom/commit/49ce7267f2d2e36fd53d79eab8e7c300cdc4d59d))

## [1.202.10](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.202.9...@waitroom/common@1.202.10) (2024-08-12)


### Bug Fixes

* accessing properties from undefined objects ([#2150](https://github.com/Waitroom/rumi.ai/issues/2150)) ([e2bc470](https://github.com/Waitroom/waitroom/commit/e2bc47067b4f0f7748aa1458770bcb2daf47471b))

## [1.202.9](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.202.8...@waitroom/common@1.202.9) (2024-08-02)

## [1.202.8](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.202.7...@waitroom/common@1.202.8) (2024-08-02)


### Bug Fixes

* ai event logging ([ca12060](https://github.com/Waitroom/waitroom/commit/ca12060fb2c30c52b83d4c068927d13f4f225e60))

## [1.202.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.202.6...@waitroom/common@1.202.7) (2024-08-01)


### Bug Fixes

* recording page session loading ([b233fdc](https://github.com/Waitroom/waitroom/commit/b233fdc5d4f0ea33c5a5cd054df4309c5eacd1f0))

## [1.202.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.202.5...@waitroom/common@1.202.6) (2024-08-01)

## [1.202.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.202.4...@waitroom/common@1.202.5) (2024-07-31)


### Bug Fixes

* ai feed editing ([e69fe1d](https://github.com/Waitroom/waitroom/commit/e69fe1d1632338f4873edde621e9ffb63991be39))

## [1.202.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.202.3...@waitroom/common@1.202.4) (2024-07-30)


### Bug Fixes

* small ui fix, updated logging ([ea5bb04](https://github.com/Waitroom/waitroom/commit/ea5bb048f12d3881be99dd920cac1d9b5f1a221f))

## [1.202.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.202.2...@waitroom/common@1.202.3) (2024-07-26)

## [1.202.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.202.1...@waitroom/common@1.202.2) (2024-07-26)


### Bug Fixes

* responsive styles ([1c5158f](https://github.com/Waitroom/waitroom/commit/1c5158f4262174faf4be35b8996e860ec107b94b))

## [1.202.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.202.0...@waitroom/common@1.202.1) (2024-07-25)


### Bug Fixes

* social sign in error handling ([#2134](https://github.com/Waitroom/rumi.ai/issues/2134)) ([8609d86](https://github.com/Waitroom/waitroom/commit/8609d863f79afd5d670f8e0258d13f28a159b2aa))

## [1.202.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.201.2...@waitroom/common@1.202.0) (2024-07-25)


### Features

* WEB-1609 integrations dashboard updates ([#2125](https://github.com/Waitroom/rumi.ai/issues/2125)) ([250b65c](https://github.com/Waitroom/waitroom/commit/250b65c49f07c591c705e7a959a5531f9cec5aad))


### Bug Fixes

* trigger release ([5f38a8d](https://github.com/Waitroom/waitroom/commit/5f38a8d3b5c86a7dc2d4315a56838ec4b122739e))

## [1.201.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.201.1...@waitroom/common@1.201.2) (2024-07-25)


### Bug Fixes

* ux meeting memory ([#2122](https://github.com/Waitroom/rumi.ai/issues/2122)) ([6aabf4a](https://github.com/Waitroom/waitroom/commit/6aabf4a585d4c451af78da1fc01e605a2fa316a0))

## [1.201.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.201.0...@waitroom/common@1.201.1) (2024-07-24)


### Bug Fixes

* import issue ([0f89549](https://github.com/Waitroom/waitroom/commit/0f8954914924db9d1b2ccc7c1cb9649cda2e488b))

## [1.201.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.200.4...@waitroom/common@1.201.0) (2024-07-24)


### Features

* setTimeout instead of setInterval ([#2128](https://github.com/Waitroom/rumi.ai/issues/2128)) ([9aab675](https://github.com/Waitroom/waitroom/commit/9aab6754587a46bab69dcb95b0a83286c04fc911))

## [1.200.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.200.3...@waitroom/common@1.200.4) (2024-07-23)

## [1.200.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.200.2...@waitroom/common@1.200.3) (2024-07-23)


### Bug Fixes

* lint issues ([e2c082d](https://github.com/Waitroom/waitroom/commit/e2c082d155211934bf74222fb92089976fe3c85c))

## [1.200.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.200.1...@waitroom/common@1.200.2) (2024-07-22)

## [1.200.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.200.0...@waitroom/common@1.200.1) (2024-07-22)


### Bug Fixes

* access request ([3084d2a](https://github.com/Waitroom/waitroom/commit/3084d2ada7b6b36fed7bc44e3d504f8f51672c05))

## [1.200.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.199.2...@waitroom/common@1.200.0) (2024-07-19)


### Features

* braid query cancellation ([fe02cd9](https://github.com/Waitroom/waitroom/commit/fe02cd96621a6880a5375806ae57df7e4c024f94))

## [1.199.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.199.1...@waitroom/common@1.199.2) (2024-07-19)


### Bug Fixes

* aggregate emojis ([48fee62](https://github.com/Waitroom/waitroom/commit/48fee6236b771fb18b0793136cac0e5438b6d143))

## [1.199.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.199.0...@waitroom/common@1.199.1) (2024-07-18)

## [1.199.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.198.0...@waitroom/common@1.199.0) (2024-07-10)


### Features

* WEB-2013 render ai response ([#2117](https://github.com/Waitroom/rumi.ai/issues/2117)) ([2301be6](https://github.com/Waitroom/waitroom/commit/2301be6b2d0e479cb010f84bba9ee2893036c0b5))

## [1.198.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.197.0...@waitroom/common@1.198.0) (2024-07-10)


### Features

* WEB-2012 add functionality to ask ai ([#2116](https://github.com/Waitroom/rumi.ai/issues/2116)) ([9035e89](https://github.com/Waitroom/waitroom/commit/9035e89b7073cd51ed2662f23b839fa37993b72e))

## [1.197.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.196.3...@waitroom/common@1.197.0) (2024-07-09)


### Features

* web-2011 meeting memory landing page ([#2115](https://github.com/Waitroom/rumi.ai/issues/2115)) ([e86833f](https://github.com/Waitroom/waitroom/commit/e86833f075ef0308ef4840d43cea5dfa6bc989d7))

## [1.196.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.196.2...@waitroom/common@1.196.3) (2024-07-09)

## [1.196.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.196.1...@waitroom/common@1.196.2) (2024-07-02)


### Bug Fixes

* added general as the default user meeting type ([#2106](https://github.com/Waitroom/rumi.ai/issues/2106)) ([a3d9ab9](https://github.com/Waitroom/waitroom/commit/a3d9ab9ff0c489a84941d55b4a82c6767cba9ac6))

## [1.196.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.196.0...@waitroom/common@1.196.1) (2024-06-28)


### Bug Fixes

* removed old host opt-in page ([#2107](https://github.com/Waitroom/rumi.ai/issues/2107)) ([2c08527](https://github.com/Waitroom/waitroom/commit/2c08527c6e7d8952e04c6907d9de542db2a6f71f))

## [1.196.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.195.1...@waitroom/common@1.196.0) (2024-06-17)


### Features

* braid client ([ff4075c](https://github.com/Waitroom/waitroom/commit/ff4075ca2f73061c25828897c5b93ed1bd58132a))
* braid package ([910b7fd](https://github.com/Waitroom/waitroom/commit/910b7fd09832b55e91b0e87afdf80324844d4821))

* WEB-1951 add confetti animation ([#2088](https://github.com/Waitroom/rumi.ai/issues/2088)) ([b0400c3](https://github.com/Waitroom/waitroom/commit/b0400c39f925005ad7105777400e52a007e98611))

## [1.195.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.195.0...@waitroom/common@1.195.1) (2024-06-14)


### Bug Fixes

* disconnect issues ([2d8d896](https://github.com/Waitroom/waitroom/commit/2d8d89640c0801ab6a5c1c7a35e2e94f92671ad6))

## [1.195.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.194.3...@waitroom/common@1.195.0) (2024-06-11)


### Features

* dashboard sessions listing update ([#2076](https://github.com/Waitroom/rumi.ai/issues/2076)) ([327a7e3](https://github.com/Waitroom/waitroom/commit/327a7e321e018118e9063a113c11f00f840c216c))

## [1.194.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.194.2...@waitroom/common@1.194.3) (2024-06-07)


### Bug Fixes

* missing variable ([3696d67](https://github.com/Waitroom/waitroom/commit/3696d67a51a642f311013aba2139696f2ccf6537))
* tests ([3b0d3d3](https://github.com/Waitroom/waitroom/commit/3b0d3d3cdc11897516c84bfbf24a554a925e7ecd))

## [1.194.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.194.1...@waitroom/common@1.194.2) (2024-05-30)


### Bug Fixes

* off the record loading ([0acc0e6](https://github.com/Waitroom/waitroom/commit/0acc0e602ce111f806ba3e608f6d431adef6dd1f))

## [1.194.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.194.0...@waitroom/common@1.194.1) (2024-05-21)


### Bug Fixes

* auth state set ([4325498](https://github.com/Waitroom/waitroom/commit/432549802f0094f40c9987bcc8b38d0585d43a95))

## [1.194.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.193.12...@waitroom/common@1.194.0) (2024-05-21)


### Features

* migrate trixta OTR toggle from trixta to elio ([#2054](https://github.com/Waitroom/rumi.ai/issues/2054)) ([ae56d71](https://github.com/Waitroom/waitroom/commit/ae56d71f578dfc51452f5506a730b36f96433ba2))

## [1.193.12](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.193.11...@waitroom/common@1.193.12) (2024-05-21)

## [1.193.11](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.193.10...@waitroom/common@1.193.11) (2024-05-21)

## [1.193.10](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.193.9...@waitroom/common@1.193.10) (2024-05-21)

## [1.193.9](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.193.8...@waitroom/common@1.193.9) (2024-05-15)


### Bug Fixes

* csp ([ed2de33](https://github.com/Waitroom/waitroom/commit/ed2de337f02dfda227be6ccff0f2764ac090696e))

## [1.193.8](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.193.7...@waitroom/common@1.193.8) (2024-05-15)


### Bug Fixes

* host page, refactor browser service ([6426b6d](https://github.com/Waitroom/waitroom/commit/6426b6d9ff10440b0358003f3dadf1eb068dc0ac))

## [1.193.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.193.6...@waitroom/common@1.193.7) (2024-05-15)

## [1.193.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.193.5...@waitroom/common@1.193.6) (2024-05-14)

## [1.193.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.193.4...@waitroom/common@1.193.5) (2024-05-09)

## [1.193.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.193.3...@waitroom/common@1.193.4) (2024-05-09)


### Bug Fixes

* improve virtual background perf ([#2031](https://github.com/Waitroom/rumi.ai/issues/2031)) ([fdd2f4f](https://github.com/Waitroom/waitroom/commit/fdd2f4fd00ea12788381d1c35fd508c39556b999))

## [1.193.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.193.2...@waitroom/common@1.193.3) (2024-05-09)

## [1.193.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.193.1...@waitroom/common@1.193.2) (2024-05-08)

## [1.193.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.193.0...@waitroom/common@1.193.1) (2024-05-07)


### Bug Fixes

* text for meeting types and reorder ([eb43d90](https://github.com/Waitroom/waitroom/commit/eb43d903a2a234f56199c46a49f45de9f424fd67))

## [1.193.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.192.2...@waitroom/common@1.193.0) (2024-05-02)


### Features

* added general type as the default selection on meeting start screen ([#2017](https://github.com/Waitroom/rumi.ai/issues/2017)) ([25fdff3](https://github.com/Waitroom/waitroom/commit/25fdff3f13f7df687e888d199d3f1c779380b79a))

## [1.192.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.192.1...@waitroom/common@1.192.2) (2024-04-30)

## [1.192.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.192.0...@waitroom/common@1.192.1) (2024-04-22)

## [1.192.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.191.1...@waitroom/common@1.192.0) (2024-04-08)


### Features

* decrease the token expiry check tolerance on init ([#2001](https://github.com/Waitroom/rumi.ai/issues/2001)) ([3ef6236](https://github.com/Waitroom/waitroom/commit/3ef6236840e62252d182d894afcbf94fd9279e53))

## [1.191.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.191.0...@waitroom/common@1.191.1) (2024-03-25)


### Bug Fixes

* default device for cam and mic ([#1947](https://github.com/Waitroom/rumi.ai/issues/1947)) ([f6ff65b](https://github.com/Waitroom/waitroom/commit/f6ff65be79ebd9caa2106746275d574c7c00a28c))

## [1.191.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.190.4...@waitroom/common@1.191.0) (2024-03-19)


### Features

* userMeetingType implementation ([#1936](https://github.com/Waitroom/rumi.ai/issues/1936)) ([01b5e4b](https://github.com/Waitroom/waitroom/commit/01b5e4ba58777091e682920676df497c1f50afd6))

## [1.190.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.190.3...@waitroom/common@1.190.4) (2024-03-14)


### Bug Fixes

* release tags ([b9c0c0d](https://github.com/Waitroom/waitroom/commit/b9c0c0d8c2958a650fe6b27de55d73de9085b1cd))

## [1.190.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.190.2...@waitroom/common@1.190.3) (2024-03-14)

## [1.190.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.190.1...@waitroom/common@1.190.2) (2024-03-01)

## [1.190.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.190.0...@waitroom/common@1.190.1) (2024-02-29)

## [1.190.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.189.1...@waitroom/common@1.190.0) (2024-02-29)


### Features

* WEB-1644 UI bugs fix styling and other issues with firefox ([#1939](https://github.com/Waitroom/rumi.ai/issues/1939)) ([7845da4](https://github.com/Waitroom/waitroom/commit/7845da494b06ac50982a22483ae31f5718014bb5))

## [1.189.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.189.0...@waitroom/common@1.189.1) (2024-02-28)


### Bug Fixes

* session form ([2222453](https://github.com/Waitroom/waitroom/commit/22224531576aeeb0238dc78792b9331f2e8299ec))

## [1.189.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.188.1...@waitroom/common@1.189.0) (2024-02-28)


### Features

* WEB-1472 when a remote stream fallback we need to show the audio only ([#1853](https://github.com/Waitroom/rumi.ai/issues/1853)) ([1d0742a](https://github.com/Waitroom/waitroom/commit/1d0742ad380ae4c83c80237f60cb280705e88235))

## [1.188.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.188.0...@waitroom/common@1.188.1) (2024-02-27)

## [1.188.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.187.3...@waitroom/common@1.188.0) (2024-02-27)


### Features

* play a sound when new access request comes on start meeting page ([#1931](https://github.com/Waitroom/rumi.ai/issues/1931)) ([a731b18](https://github.com/Waitroom/waitroom/commit/a731b181562162fb7e5191f015e3e767c81b3372))

## [1.187.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.187.2...@waitroom/common@1.187.3) (2024-02-26)

## [1.187.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.187.1...@waitroom/common@1.187.2) (2024-02-22)


### Bug Fixes

* bug in token expiry check with tolerence ([#1932](https://github.com/Waitroom/rumi.ai/issues/1932)) ([3d22cff](https://github.com/Waitroom/waitroom/commit/3d22cffa69009ed77ef25309fec2374746112c8a))

## [1.187.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.187.0...@waitroom/common@1.187.1) (2024-02-22)


### Bug Fixes

* subscription page UI overflow ([596f00f](https://github.com/Waitroom/waitroom/commit/596f00f809db4b8cdc2063fbe0b8f084349895e2))

## [1.187.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.186.1...@waitroom/common@1.187.0) (2024-02-21)


### Features

* improved error handling in agora related stored ([#1928](https://github.com/Waitroom/rumi.ai/issues/1928)) ([d51562a](https://github.com/Waitroom/waitroom/commit/d51562abe1b45785f3526b336e9f528c2ed7dc6e))

## [1.186.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.186.0...@waitroom/common@1.186.1) (2024-02-20)

## [1.186.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.185.1...@waitroom/common@1.186.0) (2024-02-19)


### Features

* WEB-1341 re-render recordings page on auth state change ([#1922](https://github.com/Waitroom/rumi.ai/issues/1922)) ([bbb1685](https://github.com/Waitroom/waitroom/commit/bbb16850a124b8301cb52e0ef96e2fae59d157f6))

## [1.185.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.185.0...@waitroom/common@1.185.1) (2024-02-16)


### Bug Fixes

* trial days left ([3f365df](https://github.com/Waitroom/waitroom/commit/3f365dfa214278093a7a75bf3f5f78a6f67da274))

## [1.185.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.184.1...@waitroom/common@1.185.0) (2024-02-12)


### Features

* WEB-1463 custom searchable selector ([#1874](https://github.com/Waitroom/rumi.ai/issues/1874)) ([1db99a2](https://github.com/Waitroom/waitroom/commit/1db99a2e29d37bc0ae69f3019b0faf48c188e587))

## [1.184.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.184.0...@waitroom/common@1.184.1) (2024-02-12)


### Bug Fixes

* showing loading screen infinitly for unauthed user ([#1854](https://github.com/Waitroom/rumi.ai/issues/1854)) ([7f110f7](https://github.com/Waitroom/waitroom/commit/7f110f7fd98a8dac796f6f665d81d1ee916b2057))

## [1.184.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.183.1...@waitroom/common@1.184.0) (2024-02-07)


### Features

* project chat UI refactor v1 ([#1906](https://github.com/Waitroom/rumi.ai/issues/1906)) ([83f67be](https://github.com/Waitroom/waitroom/commit/83f67be08b8ad783083fadc381c52d475448f23d))

## [1.183.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.183.0...@waitroom/common@1.183.1) (2024-02-06)

## [1.183.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.182.4...@waitroom/common@1.183.0) (2024-02-05)


### Features

* web 1547 add integrations component to sanity ([#1904](https://github.com/Waitroom/rumi.ai/issues/1904)) ([1c86c39](https://github.com/Waitroom/waitroom/commit/1c86c391145da42a76d67c2e9fc0dc5a7957cd7b))

## [1.182.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.182.3...@waitroom/common@1.182.4) (2024-01-31)


### Bug Fixes

* add user_id to custom_data so we can track through segment ([a13d48f](https://github.com/Waitroom/waitroom/commit/a13d48f0ea953e534afa636cbb0034e121208315))

## [1.182.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.182.2...@waitroom/common@1.182.3) (2024-01-31)


### Bug Fixes

* monetization issues ([8b91784](https://github.com/Waitroom/waitroom/commit/8b917840d95eabc5071fb798f0544902565e81d8))

## [1.182.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.182.1...@waitroom/common@1.182.2) (2024-01-29)


### Bug Fixes

* build ([ffa56bf](https://github.com/Waitroom/waitroom/commit/ffa56bf8291eb50712259f2cbf166b6d76f90303))

## [1.182.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.182.0...@waitroom/common@1.182.1) (2024-01-29)


### Bug Fixes

* removed punjab ([9f61111](https://github.com/Waitroom/waitroom/commit/9f61111a0bdc3f97b7de8f26825e27694bc07aa4))

## [1.182.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.181.0...@waitroom/common@1.182.0) (2024-01-29)


### Features

* project monetization ([#1841](https://github.com/Waitroom/rumi.ai/issues/1841)) ([edbaf07](https://github.com/Waitroom/waitroom/commit/edbaf0700bd694d2d195efd484d637dec1c97acb))

## [1.181.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.180.0...@waitroom/common@1.181.0) (2024-01-10)


### Features

* layout updates - WEB-1477, WEB-1481 ([bc3641f](https://github.com/Waitroom/waitroom/commit/bc3641faf5373b529a292459823e97adbd0d0282))

## [1.180.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.179.0...@waitroom/common@1.180.0) (2024-01-10)


### Features

* add italian translations ([#1758](https://github.com/Waitroom/rumi.ai/issues/1758)) ([23206bb](https://github.com/Waitroom/waitroom/commit/23206bb879ce51cdd2d95efb60fcd1b34afb8710))

## [1.179.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.178.0...@waitroom/common@1.179.0) (2024-01-02)


### Features

* aded loader to the integration page ([#1843](https://github.com/Waitroom/rumi.ai/issues/1843)) ([e695622](https://github.com/Waitroom/waitroom/commit/e695622b55ec9b020923dd784916aebed0ef8070))

## [1.178.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.177.1...@waitroom/common@1.178.0) (2023-12-22)


### Features

* web 1216 allow for more stream layouts that users can pick from ([#1749](https://github.com/Waitroom/rumi.ai/issues/1749)) ([55762bd](https://github.com/Waitroom/waitroom/commit/55762bdf2263a913602169dd6fbc35e224f0db52))


### Bug Fixes

* release ([606066c](https://github.com/Waitroom/waitroom/commit/606066ce01a7d2c09339d3e624cc452ff034bf4f))

## [1.177.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.177.0...@waitroom/common@1.177.1) (2023-12-19)

## [1.177.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.176.1...@waitroom/common@1.177.0) (2023-12-14)


### Features

* WEB-1363 group email with other messaging integrations ([#1828](https://github.com/Waitroom/rumi.ai/issues/1828)) ([61a08b9](https://github.com/Waitroom/waitroom/commit/61a08b9386a9fc8e7a8336b82db6a612be39ff46))
* WEB-1382 wrong date for shared item ([#1824](https://github.com/Waitroom/rumi.ai/issues/1824)) ([6dcfc64](https://github.com/Waitroom/waitroom/commit/6dcfc64749bee217e9467e5016101900d2c04991))

## [1.176.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.176.0...@waitroom/common@1.176.1) (2023-12-13)


### Bug Fixes

* integrations UI issues on dashboard ([#1820](https://github.com/Waitroom/rumi.ai/issues/1820)) ([4acdb26](https://github.com/Waitroom/waitroom/commit/4acdb2660aa202efbc2b60e5803bad17e7422a20))

## [1.176.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.175.0...@waitroom/common@1.176.0) (2023-12-08)


### Features

* added dashboard ui for integrations ([#1809](https://github.com/Waitroom/rumi.ai/issues/1809)) ([8bb3ea6](https://github.com/Waitroom/waitroom/commit/8bb3ea673df010e49fe07ad378457f6322827134))

## [1.175.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.174.0...@waitroom/common@1.175.0) (2023-12-07)


### Features

* integrations ux improve ([#1810](https://github.com/Waitroom/rumi.ai/issues/1810)) ([2ce8dc9](https://github.com/Waitroom/waitroom/commit/2ce8dc9b41c04c80cc37bb2c4d783930936851c0))

## [1.174.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.173.0...@waitroom/common@1.174.0) (2023-12-06)


### Features

* added integation info to feed items ([#1808](https://github.com/Waitroom/rumi.ai/issues/1808)) ([e86f65a](https://github.com/Waitroom/waitroom/commit/e86f65aa9323fcfe91ee52b403ab6160bdb6e819))

## [1.173.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.172.0...@waitroom/common@1.173.0) (2023-11-30)


### Features

* WEB-1315 use localstorage for storing recently used integration ([#1796](https://github.com/Waitroom/rumi.ai/issues/1796)) ([d883442](https://github.com/Waitroom/waitroom/commit/d883442f233f5d6cd73f93b5e55374438fca5a9d))

## [1.172.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.171.0...@waitroom/common@1.172.0) (2023-11-28)


### Features

* redeploy packages ([#1792](https://github.com/Waitroom/rumi.ai/issues/1792)) ([8d183ea](https://github.com/Waitroom/waitroom/commit/8d183eaabc29e48d14182ea010e44cd03022d815))

## [1.171.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.170.4...@waitroom/common@1.171.0) (2023-11-28)


### Features

* WEB-1237 ticketing form submit mutation hook ([#1777](https://github.com/Waitroom/rumi.ai/issues/1777)) ([2ec5695](https://github.com/Waitroom/waitroom/commit/2ec5695d40ed1ee2e444165454356f71581d6293))

## [1.170.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.170.3...@waitroom/common@1.170.4) (2023-11-27)


### Bug Fixes

* updated auth token refresh ([#1783](https://github.com/Waitroom/rumi.ai/issues/1783)) ([d412f6e](https://github.com/Waitroom/waitroom/commit/d412f6eac94c9cd98ace68c9be31fad0c84501ab))

## [1.170.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.170.2...@waitroom/common@1.170.3) (2023-11-23)


### Bug Fixes

* unit test cases for shouldBroadcast update ([#1786](https://github.com/Waitroom/rumi.ai/issues/1786)) ([6e1838e](https://github.com/Waitroom/waitroom/commit/6e1838e2b1c56e41b7e2a8dbc72fc158ddb3dccb))

## [1.170.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.170.1...@waitroom/common@1.170.2) (2023-11-21)


### Bug Fixes

* remove auto permission and fix preview audio leak ([694e101](https://github.com/Waitroom/waitroom/commit/694e10128b807a2d489ed87cd9ecd4302a321ec4))

## [1.170.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.170.0...@waitroom/common@1.170.1) (2023-11-21)

## [1.170.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.169.1...@waitroom/common@1.170.0) (2023-11-15)


### Features

* added nango sdk integration ([#1768](https://github.com/Waitroom/rumi.ai/issues/1768)) ([7573c25](https://github.com/Waitroom/waitroom/commit/7573c2534d2ade49265b094071974a92919081bc))

## [1.169.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.169.0...@waitroom/common@1.169.1) (2023-11-14)


### Bug Fixes

* minor fixes in context menu ([#1765](https://github.com/Waitroom/rumi.ai/issues/1765)) ([066df27](https://github.com/Waitroom/waitroom/commit/066df27b09e9f2dddba3c882a5e226d70a535815))

## [1.169.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.168.1...@waitroom/common@1.169.0) (2023-11-13)


### Features

* web 1232 update feed item context menu items ([#1762](https://github.com/Waitroom/rumi.ai/issues/1762)) ([6204d57](https://github.com/Waitroom/waitroom/commit/6204d57df3af6a236e57783bacca2429888d0e53))

## [1.168.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.168.0...@waitroom/common@1.168.1) (2023-11-07)


### Bug Fixes

* added stream warm up for next person ([#1746](https://github.com/Waitroom/rumi.ai/issues/1746)) ([da74915](https://github.com/Waitroom/waitroom/commit/da74915380c76bf3c8ddba893db2c8ef83f9a0bf))

## [1.168.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.167.0...@waitroom/common@1.168.0) (2023-10-23)


### Features

* add onsuccess toast for off the record ([f693290](https://github.com/Waitroom/waitroom/commit/f6932904318a3ff1a92b3219dc696a5b43103269))
* add onsuccess toast for off the record ([cef516f](https://github.com/Waitroom/waitroom/commit/cef516f4606687228fa7537c0d2438b87c0cb882))

## [1.167.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.166.0...@waitroom/common@1.167.0) (2023-10-16)


### Features

* improve streams play ([#1706](https://github.com/Waitroom/rumi.ai/issues/1706)) ([8446d92](https://github.com/Waitroom/waitroom/commit/8446d92b4d1c6712dffd88293b2d7cb53f724f59))

## [1.166.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.165.0...@waitroom/common@1.166.0) (2023-10-04)


### Features

* action item editing milestone 2 ([#1614](https://github.com/Waitroom/rumi.ai/issues/1614)) ([67cec4c](https://github.com/Waitroom/waitroom/commit/67cec4c73a26caf64262a139633ee554e59080bf))

## [1.165.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.164.0...@waitroom/common@1.165.0) (2023-10-04)


### Features

* trixta integration ([d0bd5f2](https://github.com/Waitroom/waitroom/commit/d0bd5f274e3d930719dd00206315d7c55afdd65e))

## [1.164.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.163.0...@waitroom/common@1.164.0) (2023-09-27)


### Features

* rumi rebrand ([#1672](https://github.com/Waitroom/rumi.ai/issues/1672)) ([f3940a6](https://github.com/Waitroom/waitroom/commit/f3940a6443bbbf1d6df80a6a3f782b720a19a041)), closes [#1677](https://github.com/Waitroom/rumi.ai/issues/1677) [#1678](https://github.com/Waitroom/rumi.ai/issues/1678) [#1688](https://github.com/Waitroom/rumi.ai/issues/1688) [#1690](https://github.com/Waitroom/rumi.ai/issues/1690)

## [1.163.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.162.2...@waitroom/common@1.163.0) (2023-09-27)


### Features

* redirect to session page if you have access ([#1693](https://github.com/Waitroom/rumi.ai/issues/1693)) ([59011b2](https://github.com/Waitroom/waitroom/commit/59011b2ffad658d81d0075e55c40d6d742bc14dd))

## [1.162.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.162.1...@waitroom/common@1.162.2) (2023-09-26)


### Bug Fixes

* default language selection should exclude unsupported languages ([4dd996a](https://github.com/Waitroom/waitroom/commit/4dd996a22f2c9df5f63d3d6c367fddbc6409b72d))

## [1.162.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.162.0...@waitroom/common@1.162.1) (2023-09-19)

## [1.162.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.161.2...@waitroom/common@1.162.0) (2023-09-18)


### Features

* virtual background ([#1671](https://github.com/Waitroom/rumi.ai/issues/1671)) ([457c3c6](https://github.com/Waitroom/waitroom/commit/457c3c6aa7458150000bb673e8c3564a63a51ce8))

## [1.161.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.161.1...@waitroom/common@1.161.2) (2023-09-08)

## [1.161.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.161.0...@waitroom/common@1.161.1) (2023-09-06)


### Bug Fixes

* release common and fix release concurrency ([a97fccb](https://github.com/Waitroom/waitroom/commit/a97fccb23782bfcaa6f6a9c24375cb0d80ab7a78))

## [1.161.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.160.1...@waitroom/common@1.161.0) (2023-09-06)


### Features

* remove update last viewed ([#1648](https://github.com/Waitroom/rumi.ai/issues/1648)) ([3a564cb](https://github.com/Waitroom/waitroom/commit/3a564cb39d399f455c69d4d557bf83aea50802b2))

## [1.160.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.160.0...@waitroom/common@1.160.1) (2023-09-01)

## [1.160.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.159.4...@waitroom/common@1.160.0) (2023-08-24)


### Features

* improve effect cleanup ([cd5e0ff](https://github.com/Waitroom/waitroom/commit/cd5e0ff84d50b29cb4131a2e1171e528accff2c3))

## [1.159.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.159.3...@waitroom/common@1.159.4) (2023-08-23)


### Bug Fixes

* request access cache key ([#1619](https://github.com/Waitroom/rumi.ai/issues/1619)) ([da9047f](https://github.com/Waitroom/waitroom/commit/da9047f8956f09e2e884f7da5431126dee3f3d4f))

## [1.159.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.159.2...@waitroom/common@1.159.3) (2023-08-23)


### Bug Fixes

* lint issue ([92c5028](https://github.com/Waitroom/waitroom/commit/92c5028121e56f99a09fa7061b5a4c3b14101f22))

## [1.159.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.159.1...@waitroom/common@1.159.2) (2023-08-21)

## [1.159.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.159.0...@waitroom/common@1.159.1) (2023-08-16)


### Bug Fixes

* storybook deploy ([5648c62](https://github.com/Waitroom/waitroom/commit/5648c628b9624a5cbc83678b7b80d71fa744f480))

## [1.159.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.158.4...@waitroom/common@1.159.0) (2023-08-16)


### Features

* post meeting summary ([#1606](https://github.com/Waitroom/rumi.ai/issues/1606)) ([d864526](https://github.com/Waitroom/waitroom/commit/d8645260e02c8ace6773e4e86277fb01b552a979))

## [1.158.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.158.3...@waitroom/common@1.158.4) (2023-08-16)


### Bug Fixes

* summa ai copy and translation ([3ea79be](https://github.com/Waitroom/waitroom/commit/3ea79bed02d36b8ec2f700122db8f6c39753d487))

## [1.158.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.158.2...@waitroom/common@1.158.3) (2023-08-15)


### Bug Fixes

* trixta session channel cleanup ([46c07ba](https://github.com/Waitroom/waitroom/commit/46c07ba540b1e57d073d286c6332a6cba035641e))

## [1.158.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.158.1...@waitroom/common@1.158.2) (2023-08-14)

## [1.158.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.158.0...@waitroom/common@1.158.1) (2023-08-10)


### Bug Fixes

* auth response transaltions ([0c69a03](https://github.com/Waitroom/waitroom/commit/0c69a03eb52a6e4370c8b06d75d3af5e57a9c8df))

## [1.158.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.157.8...@waitroom/common@1.158.0) (2023-08-09)


### Features

* show permission fix guide ([#1570](https://github.com/Waitroom/rumi.ai/issues/1570)) ([3297821](https://github.com/Waitroom/waitroom/commit/329782196e7720702ded96f45765e529cb0377da))

## [1.157.8](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.157.7...@waitroom/common@1.157.8) (2023-08-08)


### Bug Fixes

* dashboard cache issue ([b7a5962](https://github.com/Waitroom/waitroom/commit/b7a59627ec0726f1db1afe673d63411bbfa4ba95))

## [1.157.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.157.6...@waitroom/common@1.157.7) (2023-08-08)


### Bug Fixes

* show join modal on consequent requests ([#1582](https://github.com/Waitroom/rumi.ai/issues/1582)) ([c4d21a2](https://github.com/Waitroom/waitroom/commit/c4d21a2b18a4bc81f438d6252454b31b147a4fa8))

## [1.157.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.157.5...@waitroom/common@1.157.6) (2023-08-08)


### Bug Fixes

* missing recording message ([bcf2082](https://github.com/Waitroom/waitroom/commit/bcf2082a127946bc0e5926a83f78ca15212feba0))

## [1.157.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.157.4...@waitroom/common@1.157.5) (2023-08-08)

## [1.157.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.157.3...@waitroom/common@1.157.4) (2023-08-08)


### Bug Fixes

* corrected meetingType value for migrated public sessions ([#1580](https://github.com/Waitroom/rumi.ai/issues/1580)) ([fdaf962](https://github.com/Waitroom/waitroom/commit/fdaf962b0731e36607257a41b51bc15947889826))

## [1.157.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.157.2...@waitroom/common@1.157.3) (2023-08-02)

## [1.157.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.157.1...@waitroom/common@1.157.2) (2023-07-31)

## [1.157.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.157.0...@waitroom/common@1.157.1) (2023-07-31)

## [1.157.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.156.0...@waitroom/common@1.157.0) (2023-07-31)


### Features

* updated release setup ([f452e8a](https://github.com/Waitroom/waitroom/commit/f452e8aa98243fbcc24799738c4d4eb1eb2f4fa8))


### Bug Fixes

* build ([d692c0a](https://github.com/Waitroom/waitroom/commit/d692c0a3f93fd15960e847d33bcb6068542f53dd))
* semantic release ([db91e8f](https://github.com/Waitroom/waitroom/commit/db91e8f7e26f3206b80c54b68c0ec4f046d05494))

# @waitroom/common [1.156.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.155.0...@waitroom/common@1.156.0) (2023-07-28)


### Features

* WEB-1007 UI bug fix only organizer should be able to end for everyone ([#1554](https://github.com/Waitroom/rumi.ai/issues/1554)) ([dbb8966](https://github.com/Waitroom/waitroom/commit/dbb8966014a3fb15f1e607222afb9df960fad437))

# @waitroom/common [1.155.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.154.3...@waitroom/common@1.155.0) (2023-07-27)


### Features

* facilitate party mode type of meetings ([#1524](https://github.com/Waitroom/rumi.ai/issues/1524)) ([5ab4168](https://github.com/Waitroom/waitroom/commit/5ab4168e1212375418c59455deedfbd12731e16a)), closes [#1525](https://github.com/Waitroom/rumi.ai/issues/1525) [#1529](https://github.com/Waitroom/rumi.ai/issues/1529) [#1535](https://github.com/Waitroom/rumi.ai/issues/1535) [#1534](https://github.com/Waitroom/rumi.ai/issues/1534) [#1536](https://github.com/Waitroom/rumi.ai/issues/1536) [#1539](https://github.com/Waitroom/rumi.ai/issues/1539) [#1538](https://github.com/Waitroom/rumi.ai/issues/1538) [#1543](https://github.com/Waitroom/rumi.ai/issues/1543) [#1546](https://github.com/Waitroom/rumi.ai/issues/1546) [#1550](https://github.com/Waitroom/rumi.ai/issues/1550) [#1551](https://github.com/Waitroom/rumi.ai/issues/1551)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.22
* **@waitroom/common-api:** upgraded to 1.15.3
* **@waitroom/config:** upgraded to 1.23.0
* **@waitroom/http-client:** upgraded to 1.14.20
* **@waitroom/models:** upgraded to 1.100.0
* **@waitroom/tests:** upgraded to 1.21.2
* **@waitroom/trixta-utils:** upgraded to 1.19.20
* **@waitroom/utils:** upgraded to 1.38.20

## @waitroom/common [1.154.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.154.2...@waitroom/common@1.154.3) (2023-07-27)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.21

## @waitroom/common [1.154.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.154.1...@waitroom/common@1.154.2) (2023-07-24)


### Bug Fixes

* request access dismissal ([1605bdc](https://github.com/Waitroom/waitroom/commit/1605bdc8677ccec31def5f8a0785e9cc1e6757eb))

## @waitroom/common [1.154.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.154.0...@waitroom/common@1.154.1) (2023-07-24)


### Bug Fixes

* downgrade limit on getAiFeed ([#1542](https://github.com/Waitroom/rumi.ai/issues/1542)) ([347b76e](https://github.com/Waitroom/waitroom/commit/347b76e682d153455b6821f60c7bc1b0d95f8144))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.20
* **@waitroom/common-api:** upgraded to 1.15.2
* **@waitroom/http-client:** upgraded to 1.14.19
* **@waitroom/models:** upgraded to 1.99.1
* **@waitroom/tests:** upgraded to 1.21.1
* **@waitroom/trixta-utils:** upgraded to 1.19.19
* **@waitroom/utils:** upgraded to 1.38.19

# @waitroom/common [1.154.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.153.0...@waitroom/common@1.154.0) (2023-07-21)


### Features

* more common changes for party-mode ([#1541](https://github.com/Waitroom/rumi.ai/issues/1541)) ([59a0c2f](https://github.com/Waitroom/waitroom/commit/59a0c2f3ab1c41f7ea2d65942373056864712026))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.19
* **@waitroom/common-api:** upgraded to 1.15.1
* **@waitroom/http-client:** upgraded to 1.14.18
* **@waitroom/models:** upgraded to 1.99.0
* **@waitroom/tests:** upgraded to 1.21.0
* **@waitroom/trixta-utils:** upgraded to 1.19.18
* **@waitroom/utils:** upgraded to 1.38.18

# @waitroom/common [1.153.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.152.3...@waitroom/common@1.153.0) (2023-07-21)


### Features

* picture in picture ([#1495](https://github.com/Waitroom/rumi.ai/issues/1495)) ([1697ee4](https://github.com/Waitroom/waitroom/commit/1697ee408d2ced284d6fa1c879b100818e499963))

## @waitroom/common [1.152.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.152.2...@waitroom/common@1.152.3) (2023-07-21)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.18
* **@waitroom/common-api:** upgraded to 1.15.0

## @waitroom/common [1.152.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.152.1...@waitroom/common@1.152.2) (2023-07-20)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.17
* **@waitroom/common-api:** upgraded to 1.14.16
* **@waitroom/http-client:** upgraded to 1.14.17
* **@waitroom/models:** upgraded to 1.98.1
* **@waitroom/tests:** upgraded to 1.20.8
* **@waitroom/trixta-utils:** upgraded to 1.19.17
* **@waitroom/utils:** upgraded to 1.38.17

## @waitroom/common [1.152.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.152.0...@waitroom/common@1.152.1) (2023-07-18)


### Bug Fixes

* get request data type ([63a2e96](https://github.com/Waitroom/waitroom/commit/63a2e9600d3d4319df1204a77c78fb31b1a8ccd6))

# @waitroom/common [1.152.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.151.0...@waitroom/common@1.152.0) (2023-07-18)


### Features

* get query request data update ([4b15f24](https://github.com/Waitroom/waitroom/commit/4b15f24854345a60049b191f0eaade479b949762))

# @waitroom/common [1.151.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.150.3...@waitroom/common@1.151.0) (2023-07-17)


### Features

* party-mode common changes ([#1531](https://github.com/Waitroom/rumi.ai/issues/1531)) ([9a7e4aa](https://github.com/Waitroom/waitroom/commit/9a7e4aa5459217fc0aaa8f11929cd5b7365fcd95))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.16
* **@waitroom/common-api:** upgraded to 1.14.15
* **@waitroom/http-client:** upgraded to 1.14.16
* **@waitroom/models:** upgraded to 1.98.0
* **@waitroom/tests:** upgraded to 1.20.7
* **@waitroom/trixta-utils:** upgraded to 1.19.16
* **@waitroom/utils:** upgraded to 1.38.16

## @waitroom/common [1.150.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.150.2...@waitroom/common@1.150.3) (2023-07-17)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.15
* **@waitroom/common-api:** upgraded to 1.14.14
* **@waitroom/http-client:** upgraded to 1.14.15
* **@waitroom/models:** upgraded to 1.97.3
* **@waitroom/tests:** upgraded to 1.20.6
* **@waitroom/trixta-utils:** upgraded to 1.19.15
* **@waitroom/utils:** upgraded to 1.38.15

## @waitroom/common [1.150.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.150.1...@waitroom/common@1.150.2) (2023-07-14)


### Bug Fixes

* issues with dependencies ([33aad6b](https://github.com/Waitroom/waitroom/commit/33aad6b68525d755df9715018a2b9f25e168b897))

## @waitroom/common [1.150.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.150.0...@waitroom/common@1.150.1) (2023-07-14)


### Bug Fixes

* lower the bitrates ([5e4f07a](https://github.com/Waitroom/waitroom/commit/5e4f07a4eb57182041dbdc3eceac154d0285859c))

# @waitroom/common [1.150.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.149.10...@waitroom/common@1.150.0) (2023-07-14)


### Features

* increase framerate to 30fps ([0fd3305](https://github.com/Waitroom/waitroom/commit/0fd3305f04901cecf66397dffc73cfd903c6cfc3))

## @waitroom/common [1.149.10](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.149.9...@waitroom/common@1.149.10) (2023-07-12)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.14
* **@waitroom/common-api:** upgraded to 1.14.13
* **@waitroom/http-client:** upgraded to 1.14.14
* **@waitroom/models:** upgraded to 1.97.2
* **@waitroom/tests:** upgraded to 1.20.5
* **@waitroom/trixta-utils:** upgraded to 1.19.14
* **@waitroom/utils:** upgraded to 1.38.14

## @waitroom/common [1.149.9](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.149.8...@waitroom/common@1.149.9) (2023-07-11)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.13
* **@waitroom/common-api:** upgraded to 1.14.12
* **@waitroom/http-client:** upgraded to 1.14.13
* **@waitroom/models:** upgraded to 1.97.1
* **@waitroom/tests:** upgraded to 1.20.4
* **@waitroom/trixta-utils:** upgraded to 1.19.13
* **@waitroom/utils:** upgraded to 1.38.13

## @waitroom/common [1.149.8](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.149.7...@waitroom/common@1.149.8) (2023-07-11)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.12
* **@waitroom/common-api:** upgraded to 1.14.11
* **@waitroom/http-client:** upgraded to 1.14.12
* **@waitroom/trixta-utils:** upgraded to 1.19.12
* **@waitroom/utils:** upgraded to 1.38.12

## @waitroom/common [1.149.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.149.6...@waitroom/common@1.149.7) (2023-07-10)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.11
* **@waitroom/common-api:** upgraded to 1.14.10
* **@waitroom/http-client:** upgraded to 1.14.11
* **@waitroom/models:** upgraded to 1.97.0
* **@waitroom/tests:** upgraded to 1.20.3
* **@waitroom/trixta-utils:** upgraded to 1.19.11
* **@waitroom/utils:** upgraded to 1.38.11

## @waitroom/common [1.149.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.149.5...@waitroom/common@1.149.6) (2023-07-09)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.10
* **@waitroom/common-api:** upgraded to 1.14.9

## @waitroom/common [1.149.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.149.4...@waitroom/common@1.149.5) (2023-07-08)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.9

## @waitroom/common [1.149.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.149.3...@waitroom/common@1.149.4) (2023-07-08)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.8
* **@waitroom/common-api:** upgraded to 1.14.8
* **@waitroom/http-client:** upgraded to 1.14.10
* **@waitroom/models:** upgraded to 1.96.1
* **@waitroom/tests:** upgraded to 1.20.2
* **@waitroom/trixta-utils:** upgraded to 1.19.10
* **@waitroom/utils:** upgraded to 1.38.10

## @waitroom/common [1.149.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.149.2...@waitroom/common@1.149.3) (2023-07-08)

## @waitroom/common [1.149.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.149.1...@waitroom/common@1.149.2) (2023-07-08)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.7
* **@waitroom/common-api:** upgraded to 1.14.7
* **@waitroom/http-client:** upgraded to 1.14.9
* **@waitroom/models:** upgraded to 1.96.0
* **@waitroom/tests:** upgraded to 1.20.1
* **@waitroom/trixta-utils:** upgraded to 1.19.9
* **@waitroom/utils:** upgraded to 1.38.9

## @waitroom/common [1.149.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.149.0...@waitroom/common@1.149.1) (2023-07-07)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.6
* **@waitroom/common-api:** upgraded to 1.14.6

# @waitroom/common [1.149.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.148.1...@waitroom/common@1.149.0) (2023-07-07)


### Features

* update organizer details ([#1505](https://github.com/Waitroom/rumi.ai/issues/1505)) ([08cda98](https://github.com/Waitroom/waitroom/commit/08cda9832cd3a9f8a2346d95b83b947a7b58c9a9))

## @waitroom/common [1.148.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.148.0...@waitroom/common@1.148.1) (2023-07-07)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.5
* **@waitroom/common-api:** upgraded to 1.14.5
* **@waitroom/http-client:** upgraded to 1.14.8
* **@waitroom/models:** upgraded to 1.95.0
* **@waitroom/tests:** upgraded to 1.20.0
* **@waitroom/trixta-utils:** upgraded to 1.19.8
* **@waitroom/utils:** upgraded to 1.38.8

# @waitroom/common [1.148.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.147.11...@waitroom/common@1.148.0) (2023-07-06)


### Features

* allow reording page without recurrenceId ([42ef941](https://github.com/Waitroom/waitroom/commit/42ef94186c9e7f8e94e9d9795a5d03c2bc71b313))

## @waitroom/common [1.147.11](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.147.10...@waitroom/common@1.147.11) (2023-07-05)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.4
* **@waitroom/common-api:** upgraded to 1.14.4
* **@waitroom/http-client:** upgraded to 1.14.7
* **@waitroom/models:** upgraded to 1.94.3
* **@waitroom/tests:** upgraded to 1.19.6
* **@waitroom/trixta-utils:** upgraded to 1.19.7
* **@waitroom/utils:** upgraded to 1.38.7

## @waitroom/common [1.147.10](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.147.9...@waitroom/common@1.147.10) (2023-06-28)


### Bug Fixes

* request access removal ([113c33a](https://github.com/Waitroom/waitroom/commit/113c33aeae3ce240c1e80ae5d8d8460d0051680f))

## @waitroom/common [1.147.9](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.147.8...@waitroom/common@1.147.9) (2023-06-26)





### Dependencies

* **@waitroom/config:** upgraded to 1.22.0

## @waitroom/common [1.147.8](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.147.7...@waitroom/common@1.147.8) (2023-06-22)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.3
* **@waitroom/common-api:** upgraded to 1.14.3
* **@waitroom/config:** upgraded to 1.21.4
* **@waitroom/http-client:** upgraded to 1.14.6
* **@waitroom/models:** upgraded to 1.94.2
* **@waitroom/tests:** upgraded to 1.19.5
* **@waitroom/trixta-utils:** upgraded to 1.19.6
* **@waitroom/utils:** upgraded to 1.38.6

## @waitroom/common [1.147.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.147.6...@waitroom/common@1.147.7) (2023-06-19)


### Bug Fixes

* sentry errors ([#1498](https://github.com/Waitroom/rumi.ai/issues/1498)) ([5f9f5d6](https://github.com/Waitroom/waitroom/commit/5f9f5d60cce605e732b04bf17db0d808aeaba908))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.2
* **@waitroom/common-api:** upgraded to 1.14.2
* **@waitroom/http-client:** upgraded to 1.14.5
* **@waitroom/trixta-utils:** upgraded to 1.19.5
* **@waitroom/utils:** upgraded to 1.38.5

## @waitroom/common [1.147.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.147.5...@waitroom/common@1.147.6) (2023-06-16)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.1
* **@waitroom/config:** upgraded to 1.21.3

## @waitroom/common [1.147.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.147.4...@waitroom/common@1.147.5) (2023-06-15)





### Dependencies

* **@waitroom/config:** upgraded to 1.21.2

## @waitroom/common [1.147.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.147.3...@waitroom/common@1.147.4) (2023-06-14)

## @waitroom/common [1.147.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.147.2...@waitroom/common@1.147.3) (2023-06-14)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.40.0

## @waitroom/common [1.147.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.147.1...@waitroom/common@1.147.2) (2023-06-13)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.39.1
* **@waitroom/common-api:** upgraded to 1.14.1
* **@waitroom/config:** upgraded to 1.21.1
* **@waitroom/http-client:** upgraded to 1.14.4
* **@waitroom/models:** upgraded to 1.94.1
* **@waitroom/tests:** upgraded to 1.19.4
* **@waitroom/trixta-utils:** upgraded to 1.19.4
* **@waitroom/utils:** upgraded to 1.38.4

## @waitroom/common [1.147.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.147.0...@waitroom/common@1.147.1) (2023-06-13)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.39.0

# @waitroom/common [1.147.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.146.1...@waitroom/common@1.147.0) (2023-06-08)


### Features

* updated api gateway headers ([#1484](https://github.com/Waitroom/rumi.ai/issues/1484)) ([0656fd1](https://github.com/Waitroom/waitroom/commit/0656fd140762b76c6ae6cfccbc4beabfe729a295))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.38.3
* **@waitroom/common-api:** upgraded to 1.14.0
* **@waitroom/http-client:** upgraded to 1.14.3
* **@waitroom/models:** upgraded to 1.94.0
* **@waitroom/tests:** upgraded to 1.19.3
* **@waitroom/trixta-utils:** upgraded to 1.19.3
* **@waitroom/utils:** upgraded to 1.38.3

## @waitroom/common [1.146.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.146.0...@waitroom/common@1.146.1) (2023-06-07)


### Bug Fixes

* WEB-856 show the allow bots popup only for session creator ([#1481](https://github.com/Waitroom/rumi.ai/issues/1481)) ([0fe5921](https://github.com/Waitroom/waitroom/commit/0fe59214d0c318bf7baa4db09c85b96aa3c5955b))

# @waitroom/common [1.146.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.145.0...@waitroom/common@1.146.0) (2023-06-05)


### Features

* show bots reminder message when vbots leave ([#1474](https://github.com/Waitroom/rumi.ai/issues/1474)) ([4ddcaa2](https://github.com/Waitroom/waitroom/commit/4ddcaa219964f1dea521b3052c82d50e25b5d6ef))

# @waitroom/common [1.145.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.144.0...@waitroom/common@1.145.0) (2023-06-05)


### Features

* show alert if bots were already added ([#1464](https://github.com/Waitroom/rumi.ai/issues/1464)) ([fc331fc](https://github.com/Waitroom/waitroom/commit/fc331fc6dba9edf27ab7b47c747198bfa1b391ee)), closes [#1461](https://github.com/Waitroom/rumi.ai/issues/1461)

# @waitroom/common [1.144.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.143.2...@waitroom/common@1.144.0) (2023-06-05)


### Features

* WEB-818 meeting live updates ([#1473](https://github.com/Waitroom/rumi.ai/issues/1473)) ([dee0b84](https://github.com/Waitroom/waitroom/commit/dee0b847e32cd8f295433b9dc0d500401e156fc1)), closes [#1461](https://github.com/Waitroom/rumi.ai/issues/1461)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.38.2
* **@waitroom/common-api:** upgraded to 1.13.2
* **@waitroom/http-client:** upgraded to 1.14.2
* **@waitroom/models:** upgraded to 1.93.0
* **@waitroom/tests:** upgraded to 1.19.2
* **@waitroom/trixta-utils:** upgraded to 1.19.2
* **@waitroom/utils:** upgraded to 1.38.2

## @waitroom/common [1.143.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.143.1...@waitroom/common@1.143.2) (2023-05-31)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.38.1
* **@waitroom/common-api:** upgraded to 1.13.1
* **@waitroom/http-client:** upgraded to 1.14.1
* **@waitroom/models:** upgraded to 1.92.0
* **@waitroom/tests:** upgraded to 1.19.1
* **@waitroom/trixta-utils:** upgraded to 1.19.1
* **@waitroom/utils:** upgraded to 1.38.1

## @waitroom/common [1.143.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.143.0...@waitroom/common@1.143.1) (2023-05-30)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.38.0

# @waitroom/common [1.143.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.142.1...@waitroom/common@1.143.0) (2023-05-30)


### Features

* shortcut instructions ([e8424d7](https://github.com/Waitroom/waitroom/commit/e8424d786c97aad53afb609437c204fdf60138bb))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.37.0
* **@waitroom/common-api:** upgraded to 1.13.0
* **@waitroom/http-client:** upgraded to 1.14.0
* **@waitroom/models:** upgraded to 1.91.0
* **@waitroom/tests:** upgraded to 1.19.0
* **@waitroom/trixta-utils:** upgraded to 1.19.0
* **@waitroom/utils:** upgraded to 1.38.0

# @waitroom/common [1.142.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.141.1...@waitroom/common@1.142.0) (2023-05-29)


### Bug Fixes

* package versions ([23fa1c0](https://github.com/Waitroom/waitroom/commit/23fa1c02825b5a8c5ef466cc5dc6a884192ccb5e))


### Features

* web 755 implement summa ai tabs ([#1418](https://github.com/Waitroom/rumi.ai/issues/1418)) ([452aa38](https://github.com/Waitroom/waitroom/commit/452aa38fe035f1829f3d52a15536144003fcb41d))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.36.6
* **@waitroom/common-api:** upgraded to 1.12.27
* **@waitroom/config:** upgraded to 1.21.0
* **@waitroom/http-client:** upgraded to 1.13.16
* **@waitroom/models:** upgraded to 1.90.0
* **@waitroom/tests:** upgraded to 1.18.16
* **@waitroom/trixta-utils:** upgraded to 1.18.5
* **@waitroom/utils:** upgraded to 1.37.0

## @waitroom/common [1.141.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.141.0...@waitroom/common@1.141.1) (2023-05-26)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.36.5
* **@waitroom/common-api:** upgraded to 1.12.26
* **@waitroom/http-client:** upgraded to 1.13.15
* **@waitroom/models:** upgraded to 1.89.0
* **@waitroom/tests:** upgraded to 1.18.15
* **@waitroom/trixta-utils:** upgraded to 1.18.4
* **@waitroom/utils:** upgraded to 1.36.6

# @waitroom/common [1.141.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.140.4...@waitroom/common@1.141.0) (2023-05-25)


### Features

* project adding a co host-producer ([#1394](https://github.com/Waitroom/rumi.ai/issues/1394)) ([d9388f3](https://github.com/Waitroom/waitroom/commit/d9388f3584c9ed4aef912212a87d7b8c0c4396c8)), closes [#1396](https://github.com/Waitroom/rumi.ai/issues/1396) [#1398](https://github.com/Waitroom/rumi.ai/issues/1398) [#1403](https://github.com/Waitroom/rumi.ai/issues/1403) [#1399](https://github.com/Waitroom/rumi.ai/issues/1399) [#1406](https://github.com/Waitroom/rumi.ai/issues/1406) [#1420](https://github.com/Waitroom/rumi.ai/issues/1420) [#1435](https://github.com/Waitroom/rumi.ai/issues/1435) [#1436](https://github.com/Waitroom/rumi.ai/issues/1436) [#1444](https://github.com/Waitroom/rumi.ai/issues/1444) [#1445](https://github.com/Waitroom/rumi.ai/issues/1445)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.36.4
* **@waitroom/common-api:** upgraded to 1.12.25
* **@waitroom/http-client:** upgraded to 1.13.14
* **@waitroom/models:** upgraded to 1.88.0
* **@waitroom/tests:** upgraded to 1.18.14
* **@waitroom/trixta-utils:** upgraded to 1.18.3
* **@waitroom/utils:** upgraded to 1.36.5

## @waitroom/common [1.140.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.140.3...@waitroom/common@1.140.4) (2023-05-24)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.36.3

## @waitroom/common [1.140.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.140.2...@waitroom/common@1.140.3) (2023-05-23)


### Bug Fixes

* hide end button for guests who are not live ([#1441](https://github.com/Waitroom/rumi.ai/issues/1441)) ([6b90110](https://github.com/Waitroom/waitroom/commit/6b9011001eb9b713e6b639daf8cc4a6185f30b76))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.36.2
* **@waitroom/common-api:** upgraded to 1.12.24

## @waitroom/common [1.140.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.140.1...@waitroom/common@1.140.2) (2023-05-18)





### Dependencies

* **@waitroom/config:** upgraded to 1.20.1

## @waitroom/common [1.140.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.140.0...@waitroom/common@1.140.1) (2023-05-18)





### Dependencies

* **@waitroom/config:** upgraded to 1.20.0

# @waitroom/common [1.140.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.139.9...@waitroom/common@1.140.0) (2023-05-18)


### Features

* added actions and reaction for host downgrade ([1e4ca82](https://github.com/Waitroom/waitroom/commit/1e4ca82ec0092c35cc11c2de054d59c975a49d34))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.36.1
* **@waitroom/common-api:** upgraded to 1.12.23
* **@waitroom/http-client:** upgraded to 1.13.13
* **@waitroom/models:** upgraded to 1.87.0
* **@waitroom/tests:** upgraded to 1.18.13
* **@waitroom/trixta-utils:** upgraded to 1.18.2
* **@waitroom/utils:** upgraded to 1.36.4

## @waitroom/common [1.139.9](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.139.8...@waitroom/common@1.139.9) (2023-05-15)

## @waitroom/common [1.139.8](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.139.7...@waitroom/common@1.139.8) (2023-05-12)

## @waitroom/common [1.139.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.139.6...@waitroom/common@1.139.7) (2023-05-12)


### Bug Fixes

* bug with setting the right preset for less than 2 active streams ([ae11607](https://github.com/Waitroom/waitroom/commit/ae11607125c5e358c923bac29cfda96986821056))

## @waitroom/common [1.139.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.139.5...@waitroom/common@1.139.6) (2023-05-10)

## @waitroom/common [1.139.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.139.4...@waitroom/common@1.139.5) (2023-05-10)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.36.0

## @waitroom/common [1.139.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.139.3...@waitroom/common@1.139.4) (2023-05-10)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.20
* **@waitroom/common-api:** upgraded to 1.12.22
* **@waitroom/http-client:** upgraded to 1.13.12
* **@waitroom/models:** upgraded to 1.86.1
* **@waitroom/tests:** upgraded to 1.18.12
* **@waitroom/trixta-utils:** upgraded to 1.18.1
* **@waitroom/utils:** upgraded to 1.36.3

## @waitroom/common [1.139.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.139.2...@waitroom/common@1.139.3) (2023-05-10)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.19
* **@waitroom/common-api:** upgraded to 1.12.21
* **@waitroom/http-client:** upgraded to 1.13.11
* **@waitroom/models:** upgraded to 1.86.0
* **@waitroom/tests:** upgraded to 1.18.11
* **@waitroom/trixta-utils:** upgraded to 1.18.0
* **@waitroom/utils:** upgraded to 1.36.2

## @waitroom/common [1.139.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.139.1...@waitroom/common@1.139.2) (2023-05-09)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.18
* **@waitroom/common-api:** upgraded to 1.12.20
* **@waitroom/http-client:** upgraded to 1.13.10
* **@waitroom/models:** upgraded to 1.85.0
* **@waitroom/tests:** upgraded to 1.18.10
* **@waitroom/trixta-utils:** upgraded to 1.17.21
* **@waitroom/utils:** upgraded to 1.36.1

## @waitroom/common [1.139.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.139.0...@waitroom/common@1.139.1) (2023-05-04)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.17
* **@waitroom/common-api:** upgraded to 1.12.19
* **@waitroom/http-client:** upgraded to 1.13.9
* **@waitroom/trixta-utils:** upgraded to 1.17.20
* **@waitroom/utils:** upgraded to 1.36.0

# @waitroom/common [1.139.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.138.3...@waitroom/common@1.139.0) (2023-05-02)


### Features

* disabled catch up, fixed ui issues ([1094ae2](https://github.com/Waitroom/waitroom/commit/1094ae2c84b9c6c15fe41677d76a24894d3bcfee))

## @waitroom/common [1.138.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.138.2...@waitroom/common@1.138.3) (2023-04-29)


### Bug Fixes

* host role approval ([af35cc5](https://github.com/Waitroom/waitroom/commit/af35cc5006faacd7b4161104a54beac2523bcfcf))

## @waitroom/common [1.138.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.138.1...@waitroom/common@1.138.2) (2023-04-25)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.16

## @waitroom/common [1.138.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.138.0...@waitroom/common@1.138.1) (2023-04-25)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.15
* **@waitroom/common-api:** upgraded to 1.12.18
* **@waitroom/http-client:** upgraded to 1.13.8
* **@waitroom/models:** upgraded to 1.84.1
* **@waitroom/tests:** upgraded to 1.18.9
* **@waitroom/trixta-utils:** upgraded to 1.17.19
* **@waitroom/utils:** upgraded to 1.35.12

# @waitroom/common [1.138.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.137.1...@waitroom/common@1.138.0) (2023-04-22)


### Features

* transcription summary ([414c36f](https://github.com/Waitroom/waitroom/commit/414c36f9666e126bb6a3fa6520cec18650603c4d))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.14
* **@waitroom/common-api:** upgraded to 1.12.17
* **@waitroom/http-client:** upgraded to 1.13.7
* **@waitroom/models:** upgraded to 1.84.0
* **@waitroom/tests:** upgraded to 1.18.8
* **@waitroom/trixta-utils:** upgraded to 1.17.18
* **@waitroom/utils:** upgraded to 1.35.11

## @waitroom/common [1.137.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.137.0...@waitroom/common@1.137.1) (2023-04-21)


### Bug Fixes

* return whole response from get summary ([#1397](https://github.com/Waitroom/rumi.ai/issues/1397)) ([21480db](https://github.com/Waitroom/waitroom/commit/21480db19ad6f04f95c21bf08999f2373ad1c251))

# @waitroom/common [1.137.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.136.1...@waitroom/common@1.137.0) (2023-04-21)


### Features

* get live transcription summary hook ([#1395](https://github.com/Waitroom/rumi.ai/issues/1395)) ([d999851](https://github.com/Waitroom/waitroom/commit/d9998518235d6acf35a9a0132872d0643d267a5e))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.13
* **@waitroom/common-api:** upgraded to 1.12.16
* **@waitroom/http-client:** upgraded to 1.13.6
* **@waitroom/models:** upgraded to 1.83.0
* **@waitroom/tests:** upgraded to 1.18.7
* **@waitroom/trixta-utils:** upgraded to 1.17.17
* **@waitroom/utils:** upgraded to 1.35.10

## @waitroom/common [1.136.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.136.0...@waitroom/common@1.136.1) (2023-04-20)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.12
* **@waitroom/common-api:** upgraded to 1.12.15
* **@waitroom/http-client:** upgraded to 1.13.5
* **@waitroom/trixta-utils:** upgraded to 1.17.16
* **@waitroom/utils:** upgraded to 1.35.9

# @waitroom/common [1.136.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.135.0...@waitroom/common@1.136.0) (2023-04-18)


### Features

* agora rtt ([#1393](https://github.com/Waitroom/rumi.ai/issues/1393)) ([1ddd65b](https://github.com/Waitroom/waitroom/commit/1ddd65b4c1258073870a5ae5168bb1a30b82ffa7))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.11
* **@waitroom/common-api:** upgraded to 1.12.14
* **@waitroom/http-client:** upgraded to 1.13.4
* **@waitroom/models:** upgraded to 1.82.0
* **@waitroom/tests:** upgraded to 1.18.6
* **@waitroom/trixta-utils:** upgraded to 1.17.15
* **@waitroom/utils:** upgraded to 1.35.8

# @waitroom/common [1.135.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.134.0...@waitroom/common@1.135.0) (2023-04-14)


### Features

* export useSendTranscription hook ([#1389](https://github.com/Waitroom/rumi.ai/issues/1389)) ([325c6e5](https://github.com/Waitroom/waitroom/commit/325c6e570fe30e3edce9397d46bba994407d088c))

# @waitroom/common [1.134.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.133.1...@waitroom/common@1.134.0) (2023-04-14)


### Features

* useSendTranscription hook ([#1388](https://github.com/Waitroom/rumi.ai/issues/1388)) ([02a9201](https://github.com/Waitroom/waitroom/commit/02a92018a83ec16e6d0fa1b159aa61bf89547531))


### Reverts

* Revert "refactor: trixta sagas (#1385)" ([077783e](https://github.com/Waitroom/waitroom/commit/077783e859fed0e2cf9cdc064979c7335afb0881)), closes [#1385](https://github.com/Waitroom/rumi.ai/issues/1385)
* Revert "chore(release): @waitroom/common@v1.133.1 [skip ci]" ([2c03184](https://github.com/Waitroom/waitroom/commit/2c031841f312c2510130e60127735f4c574e652c))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.10
* **@waitroom/common-api:** upgraded to 1.12.13
* **@waitroom/http-client:** upgraded to 1.13.3
* **@waitroom/models:** upgraded to 1.81.0
* **@waitroom/tests:** upgraded to 1.18.5
* **@waitroom/trixta-utils:** upgraded to 1.17.14
* **@waitroom/utils:** upgraded to 1.35.7

# @waitroom/common [1.133.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.132.4...@waitroom/common@1.133.0) (2023-04-13)


### Features

* add trixta action payload model for live transcript  ([#1386](https://github.com/Waitroom/rumi.ai/issues/1386)) ([0e1feb0](https://github.com/Waitroom/waitroom/commit/0e1feb09f38b08f5634f2b412584877aa8634d7a))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.8
* **@waitroom/common-api:** upgraded to 1.12.11
* **@waitroom/http-client:** upgraded to 1.13.2
* **@waitroom/models:** upgraded to 1.80.0
* **@waitroom/tests:** upgraded to 1.18.4
* **@waitroom/trixta-utils:** upgraded to 1.17.12
* **@waitroom/utils:** upgraded to 1.35.6

## @waitroom/common [1.132.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.132.3...@waitroom/common@1.132.4) (2023-04-12)


### Bug Fixes

* auth service ([be03150](https://github.com/Waitroom/waitroom/commit/be03150d7aa37a0e58f19bc732c3bf43007fdd7f))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.7
* **@waitroom/common-api:** upgraded to 1.12.10

## @waitroom/common [1.132.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.132.2...@waitroom/common@1.132.3) (2023-04-12)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.6
* **@waitroom/common-api:** upgraded to 1.12.9
* **@waitroom/http-client:** upgraded to 1.13.1
* **@waitroom/trixta-utils:** upgraded to 1.17.11
* **@waitroom/utils:** upgraded to 1.35.5

## @waitroom/common [1.132.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.132.1...@waitroom/common@1.132.2) (2023-04-11)


### Bug Fixes

* fix mapping ([3a46428](https://github.com/Waitroom/waitroom/commit/3a464283c4f5536f664856fed16d36343a0f9177))

## @waitroom/common [1.132.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.132.0...@waitroom/common@1.132.1) (2023-04-11)


### Bug Fixes

* current user mapping ([a659e64](https://github.com/Waitroom/waitroom/commit/a659e6448f53a533bd7f797f0b7aea2ca1f92eec))

# @waitroom/common [1.132.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.131.4...@waitroom/common@1.132.0) (2023-04-10)


### Features

* WEB-683 unable to retry otp token ([#1381](https://github.com/Waitroom/rumi.ai/issues/1381)) ([08d980a](https://github.com/Waitroom/waitroom/commit/08d980a01cc527a6953237c2853729063f7ef13f))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.5
* **@waitroom/common-api:** upgraded to 1.12.8
* **@waitroom/http-client:** upgraded to 1.13.0
* **@waitroom/trixta-utils:** upgraded to 1.17.10

## @waitroom/common [1.131.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.131.3...@waitroom/common@1.131.4) (2023-04-06)


### Bug Fixes

* fixed current user, fixed session UI ([e706a7b](https://github.com/Waitroom/waitroom/commit/e706a7bbf5b8da861fe8628ff6ecfd2d1cd9026b))

## @waitroom/common [1.131.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.131.2...@waitroom/common@1.131.3) (2023-04-06)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.4
* **@waitroom/common-api:** upgraded to 1.12.7

## @waitroom/common [1.131.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.131.1...@waitroom/common@1.131.2) (2023-04-05)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.3
* **@waitroom/common-api:** upgraded to 1.12.6
* **@waitroom/config:** upgraded to 1.19.1
* **@waitroom/http-client:** upgraded to 1.12.9
* **@waitroom/models:** upgraded to 1.79.3
* **@waitroom/tests:** upgraded to 1.18.3
* **@waitroom/trixta-utils:** upgraded to 1.17.9
* **@waitroom/utils:** upgraded to 1.35.4

## @waitroom/common [1.131.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.131.0...@waitroom/common@1.131.1) (2023-04-04)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.2

# @waitroom/common [1.131.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.130.0...@waitroom/common@1.131.0) (2023-03-29)


### Features

* rest presence list on session end ([5e602ab](https://github.com/Waitroom/waitroom/commit/5e602abf98aa50070646314fe645a868854c01fe))

# @waitroom/common [1.130.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.129.0...@waitroom/common@1.130.0) (2023-03-29)


### Features

* moved cache management to common ([d0fc11f](https://github.com/Waitroom/waitroom/commit/d0fc11f78bb45af35344b729041df46fe2d08ed1))

# @waitroom/common [1.129.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.128.8...@waitroom/common@1.129.0) (2023-03-28)


### Features

* adds backward compatability for email links ([7ebf82a](https://github.com/Waitroom/waitroom/commit/7ebf82a44ddcf8bf47655b7f229a17997d946fc9))

## @waitroom/common [1.128.8](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.128.7...@waitroom/common@1.128.8) (2023-03-28)


### Bug Fixes

* test coverage threshold ([0970497](https://github.com/Waitroom/waitroom/commit/0970497326538be736ee8ffcd77498b87859cc57))

## @waitroom/common [1.128.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.128.6...@waitroom/common@1.128.7) (2023-03-27)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.1
* **@waitroom/common-api:** upgraded to 1.12.5
* **@waitroom/http-client:** upgraded to 1.12.8
* **@waitroom/models:** upgraded to 1.79.2
* **@waitroom/tests:** upgraded to 1.18.2
* **@waitroom/trixta-utils:** upgraded to 1.17.8
* **@waitroom/utils:** upgraded to 1.35.3

## @waitroom/common [1.128.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.128.5...@waitroom/common@1.128.6) (2023-03-24)


### Bug Fixes

* host role ([82882ab](https://github.com/Waitroom/waitroom/commit/82882ab4bbe4513142e57aadf38c4ba834095d8c))

## @waitroom/common [1.128.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.128.4...@waitroom/common@1.128.5) (2023-03-24)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.35.0

## @waitroom/common [1.128.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.128.3...@waitroom/common@1.128.4) (2023-03-24)


### Bug Fixes

* auth access ([f75bd48](https://github.com/Waitroom/waitroom/commit/f75bd484cd4b7483a08b6a30e1abb7407fc32dea))

## @waitroom/common [1.128.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.128.2...@waitroom/common@1.128.3) (2023-03-24)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.34.14

## @waitroom/common [1.128.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.128.1...@waitroom/common@1.128.2) (2023-03-24)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.34.13
* **@waitroom/common-api:** upgraded to 1.12.4
* **@waitroom/http-client:** upgraded to 1.12.7
* **@waitroom/models:** upgraded to 1.79.1
* **@waitroom/tests:** upgraded to 1.18.1
* **@waitroom/trixta-utils:** upgraded to 1.17.7
* **@waitroom/utils:** upgraded to 1.35.2

## @waitroom/common [1.128.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.128.0...@waitroom/common@1.128.1) (2023-03-23)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.34.12
* **@waitroom/common-api:** upgraded to 1.12.3

# @waitroom/common [1.128.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.127.10...@waitroom/common@1.128.0) (2023-03-23)


### Bug Fixes

* modal issues, auth refactor ([a020cd1](https://github.com/Waitroom/waitroom/commit/a020cd1edfaad19c82faceb1fbdd069c2382b299))
* tests ([b21582e](https://github.com/Waitroom/waitroom/commit/b21582e20ee774ea2737897609e1d07f49eff113))


### Features

* updated UI according to recordings state ([#1342](https://github.com/Waitroom/rumi.ai/issues/1342)) ([c56c27c](https://github.com/Waitroom/waitroom/commit/c56c27c9b7f3136e289aeca35e9552e13351a398))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.34.11
* **@waitroom/common-api:** upgraded to 1.12.2
* **@waitroom/http-client:** upgraded to 1.12.6
* **@waitroom/models:** upgraded to 1.79.0
* **@waitroom/tests:** upgraded to 1.18.0
* **@waitroom/trixta-utils:** upgraded to 1.17.6
* **@waitroom/utils:** upgraded to 1.35.1

## @waitroom/common [1.127.10](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.127.9...@waitroom/common@1.127.10) (2023-03-22)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.34.10
* **@waitroom/common-api:** upgraded to 1.12.1

## @waitroom/common [1.127.9](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.127.8...@waitroom/common@1.127.9) (2023-03-22)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.34.9
* **@waitroom/common-api:** upgraded to 1.12.0

## @waitroom/common [1.127.8](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.127.7...@waitroom/common@1.127.8) (2023-03-21)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.34.8
* **@waitroom/common-api:** upgraded to 1.11.4
* **@waitroom/http-client:** upgraded to 1.12.5
* **@waitroom/trixta-utils:** upgraded to 1.17.5
* **@waitroom/utils:** upgraded to 1.35.0

## @waitroom/common [1.127.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.127.6...@waitroom/common@1.127.7) (2023-03-21)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.34.7
* **@waitroom/common-api:** upgraded to 1.11.3
* **@waitroom/http-client:** upgraded to 1.12.4
* **@waitroom/models:** upgraded to 1.78.0
* **@waitroom/tests:** upgraded to 1.17.4
* **@waitroom/trixta-utils:** upgraded to 1.17.4
* **@waitroom/utils:** upgraded to 1.34.4

## @waitroom/common [1.127.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.127.5...@waitroom/common@1.127.6) (2023-03-21)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.34.6
* **@waitroom/common-api:** upgraded to 1.11.2

## @waitroom/common [1.127.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.127.4...@waitroom/common@1.127.5) (2023-03-21)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.34.5
* **@waitroom/common-api:** upgraded to 1.11.1
* **@waitroom/http-client:** upgraded to 1.12.3
* **@waitroom/models:** upgraded to 1.77.0
* **@waitroom/tests:** upgraded to 1.17.3
* **@waitroom/trixta-utils:** upgraded to 1.17.3
* **@waitroom/utils:** upgraded to 1.34.3

## @waitroom/common [1.127.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.127.3...@waitroom/common@1.127.4) (2023-03-20)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.34.4
* **@waitroom/common-api:** upgraded to 1.11.0

## @waitroom/common [1.127.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.127.2...@waitroom/common@1.127.3) (2023-03-20)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.34.3
* **@waitroom/common-api:** upgraded to 1.10.3

## @waitroom/common [1.127.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.127.1...@waitroom/common@1.127.2) (2023-03-20)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.34.2
* **@waitroom/common-api:** upgraded to 1.10.2
* **@waitroom/http-client:** upgraded to 1.12.2
* **@waitroom/models:** upgraded to 1.76.1
* **@waitroom/tests:** upgraded to 1.17.2
* **@waitroom/trixta-utils:** upgraded to 1.17.2
* **@waitroom/utils:** upgraded to 1.34.2

## @waitroom/common [1.127.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.127.0...@waitroom/common@1.127.1) (2023-03-17)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.34.1
* **@waitroom/common-api:** upgraded to 1.10.1
* **@waitroom/http-client:** upgraded to 1.12.1
* **@waitroom/models:** upgraded to 1.76.0
* **@waitroom/tests:** upgraded to 1.17.1
* **@waitroom/trixta-utils:** upgraded to 1.17.1
* **@waitroom/utils:** upgraded to 1.34.1

# @waitroom/common [1.127.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.126.4...@waitroom/common@1.127.0) (2023-03-17)


### Features

* instant meetings ([#1279](https://github.com/Waitroom/rumi.ai/issues/1279)) ([e5f08eb](https://github.com/Waitroom/waitroom/commit/e5f08eb9a1bd6db6dddd4ec7a6a40f8ccf1dcbf1)), closes [#1284](https://github.com/Waitroom/rumi.ai/issues/1284) [#1289](https://github.com/Waitroom/rumi.ai/issues/1289) [#1287](https://github.com/Waitroom/rumi.ai/issues/1287) [#1295](https://github.com/Waitroom/rumi.ai/issues/1295) [#1296](https://github.com/Waitroom/rumi.ai/issues/1296) [#1298](https://github.com/Waitroom/rumi.ai/issues/1298) [#1300](https://github.com/Waitroom/rumi.ai/issues/1300) [#1303](https://github.com/Waitroom/rumi.ai/issues/1303) [#1302](https://github.com/Waitroom/rumi.ai/issues/1302) [#1304](https://github.com/Waitroom/rumi.ai/issues/1304) [#1305](https://github.com/Waitroom/rumi.ai/issues/1305) [#1306](https://github.com/Waitroom/rumi.ai/issues/1306) [#1310](https://github.com/Waitroom/rumi.ai/issues/1310) [#1309](https://github.com/Waitroom/rumi.ai/issues/1309) [#1315](https://github.com/Waitroom/rumi.ai/issues/1315) [#1317](https://github.com/Waitroom/rumi.ai/issues/1317) [#1320](https://github.com/Waitroom/rumi.ai/issues/1320) [#1319](https://github.com/Waitroom/rumi.ai/issues/1319) [#1321](https://github.com/Waitroom/rumi.ai/issues/1321) [#1318](https://github.com/Waitroom/rumi.ai/issues/1318)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.34.0
* **@waitroom/common-api:** upgraded to 1.10.0
* **@waitroom/config:** upgraded to 1.19.0
* **@waitroom/http-client:** upgraded to 1.12.0
* **@waitroom/models:** upgraded to 1.75.0
* **@waitroom/tests:** upgraded to 1.17.0
* **@waitroom/trixta-utils:** upgraded to 1.17.0
* **@waitroom/utils:** upgraded to 1.34.0

## @waitroom/common [1.126.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.126.3...@waitroom/common@1.126.4) (2023-03-17)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.33.4
* **@waitroom/common-api:** upgraded to 1.9.0

## @waitroom/common [1.126.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.126.2...@waitroom/common@1.126.3) (2023-03-15)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.33.3
* **@waitroom/common-api:** upgraded to 1.8.2
* **@waitroom/config:** upgraded to 1.18.0
* **@waitroom/http-client:** upgraded to 1.11.3
* **@waitroom/models:** upgraded to 1.74.0
* **@waitroom/tests:** upgraded to 1.16.3
* **@waitroom/trixta-utils:** upgraded to 1.16.3
* **@waitroom/utils:** upgraded to 1.33.3

## @waitroom/common [1.126.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.126.1...@waitroom/common@1.126.2) (2023-03-15)





### Dependencies

* **@waitroom/config:** upgraded to 1.17.1

## @waitroom/common [1.126.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.126.0...@waitroom/common@1.126.1) (2023-03-15)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.33.2
* **@waitroom/common-api:** upgraded to 1.8.1
* **@waitroom/http-client:** upgraded to 1.11.2
* **@waitroom/models:** upgraded to 1.73.0
* **@waitroom/tests:** upgraded to 1.16.2
* **@waitroom/trixta-utils:** upgraded to 1.16.2
* **@waitroom/utils:** upgraded to 1.33.2

# @waitroom/common [1.126.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.125.2...@waitroom/common@1.126.0) (2023-03-12)


### Features

* slack id ([67e5133](https://github.com/Waitroom/waitroom/commit/67e5133f8b7c0a5189224bf3e152eaf3fac9219f))





### Dependencies

* **@waitroom/config:** upgraded to 1.17.0

## @waitroom/common [1.125.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.125.1...@waitroom/common@1.125.2) (2023-03-09)

## @waitroom/common [1.125.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.125.0...@waitroom/common@1.125.1) (2023-03-06)





### Dependencies

* **@waitroom/config:** upgraded to 1.16.1

# @waitroom/common [1.125.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.124.2...@waitroom/common@1.125.0) (2023-03-06)


### Features

* removed home link ([#1290](https://github.com/Waitroom/rumi.ai/issues/1290)) ([6a0df0b](https://github.com/Waitroom/waitroom/commit/6a0df0b9cb20e3732aab510a8edba00b9cfdeeb6))

## @waitroom/common [1.124.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.124.1...@waitroom/common@1.124.2) (2023-02-28)


### Bug Fixes

* clean reactions when unmounting ([#1278](https://github.com/Waitroom/rumi.ai/issues/1278)) ([2a1650e](https://github.com/Waitroom/waitroom/commit/2a1650e16d0f16f493cc4439e8f761f00935b7b6))

## @waitroom/common [1.124.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.124.0...@waitroom/common@1.124.1) (2023-02-28)


### Bug Fixes

* adds 30000 duration to chime-in request notification ([#1277](https://github.com/Waitroom/rumi.ai/issues/1277)) ([c177994](https://github.com/Waitroom/waitroom/commit/c17799403389f2c166dd6af66eb577c55cdcb8f2))

# @waitroom/common [1.124.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.123.0...@waitroom/common@1.124.0) (2023-02-28)


### Features

* has chimed in users selector ([#1273](https://github.com/Waitroom/rumi.ai/issues/1273)) ([c064a94](https://github.com/Waitroom/waitroom/commit/c064a9487d62a57c4e84ce80f097a4769b31f0d8))

# @waitroom/common [1.123.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.122.0...@waitroom/common@1.123.0) (2023-02-23)


### Features

* chime-in polishes ([ed7d54a](https://github.com/Waitroom/waitroom/commit/ed7d54abfa11c3e5c63214ed626ef4c555879644)), closes [#2](https://github.com/Waitroom/rumi.ai/issues/2)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.33.1
* **@waitroom/common-api:** upgraded to 1.8.0
* **@waitroom/http-client:** upgraded to 1.11.1
* **@waitroom/models:** upgraded to 1.72.0
* **@waitroom/tests:** upgraded to 1.16.1
* **@waitroom/trixta-utils:** upgraded to 1.16.1
* **@waitroom/utils:** upgraded to 1.33.1

# @waitroom/common [1.122.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.121.5...@waitroom/common@1.122.0) (2023-02-23)


### Features

* one on one conversations ([#1232](https://github.com/Waitroom/rumi.ai/issues/1232)) ([feb0d53](https://github.com/Waitroom/waitroom/commit/feb0d535d4520bb355c8e5dcfaf4a53c86f25aa4)), closes [#1125](https://github.com/Waitroom/rumi.ai/issues/1125) [#1128](https://github.com/Waitroom/rumi.ai/issues/1128) [#1182](https://github.com/Waitroom/rumi.ai/issues/1182) [#1183](https://github.com/Waitroom/rumi.ai/issues/1183) [#1184](https://github.com/Waitroom/rumi.ai/issues/1184) [#1185](https://github.com/Waitroom/rumi.ai/issues/1185) [#1187](https://github.com/Waitroom/rumi.ai/issues/1187) [#1205](https://github.com/Waitroom/rumi.ai/issues/1205) [#1208](https://github.com/Waitroom/rumi.ai/issues/1208) [#1209](https://github.com/Waitroom/rumi.ai/issues/1209) [#1215](https://github.com/Waitroom/rumi.ai/issues/1215) [#1227](https://github.com/Waitroom/rumi.ai/issues/1227) [#1229](https://github.com/Waitroom/rumi.ai/issues/1229) [#1228](https://github.com/Waitroom/rumi.ai/issues/1228)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.33.0
* **@waitroom/common-api:** upgraded to 1.7.0
* **@waitroom/config:** upgraded to 1.16.0
* **@waitroom/http-client:** upgraded to 1.11.0
* **@waitroom/models:** upgraded to 1.71.0
* **@waitroom/tests:** upgraded to 1.16.0
* **@waitroom/trixta-utils:** upgraded to 1.16.0
* **@waitroom/utils:** upgraded to 1.33.0

## @waitroom/common [1.121.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.121.4...@waitroom/common@1.121.5) (2023-02-23)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.23
* **@waitroom/common-api:** upgraded to 1.6.2
* **@waitroom/http-client:** upgraded to 1.10.18
* **@waitroom/models:** upgraded to 1.70.0
* **@waitroom/tests:** upgraded to 1.15.17
* **@waitroom/trixta-utils:** upgraded to 1.15.17
* **@waitroom/utils:** upgraded to 1.32.9

## @waitroom/common [1.121.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.121.3...@waitroom/common@1.121.4) (2023-02-22)


### Bug Fixes

* chime-in and notifications hooks bugfixes ([fa691e5](https://github.com/Waitroom/waitroom/commit/fa691e50aa5cde864a1ec3e88f8c51512f0f1356))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.22
* **@waitroom/common-api:** upgraded to 1.6.1

## @waitroom/common [1.121.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.121.2...@waitroom/common@1.121.3) (2023-02-22)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.21
* **@waitroom/common-api:** upgraded to 1.6.0

## @waitroom/common [1.121.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.121.1...@waitroom/common@1.121.2) (2023-02-21)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.20
* **@waitroom/common-api:** upgraded to 1.5.0

## @waitroom/common [1.121.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.121.0...@waitroom/common@1.121.1) (2023-02-21)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.19
* **@waitroom/common-api:** upgraded to 1.4.1
* **@waitroom/http-client:** upgraded to 1.10.17
* **@waitroom/trixta-utils:** upgraded to 1.15.16
* **@waitroom/utils:** upgraded to 1.32.8

# @waitroom/common [1.121.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.120.2...@waitroom/common@1.121.0) (2023-02-20)


### Features

* social login external service ([d7bf3b6](https://github.com/Waitroom/waitroom/commit/d7bf3b6ecfc128630f95f732a51ab36df8a80abb))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.18
* **@waitroom/common-api:** upgraded to 1.4.0

## @waitroom/common [1.120.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.120.1...@waitroom/common@1.120.2) (2023-02-20)


### Bug Fixes

* toast messages when accepting request ([#1258](https://github.com/Waitroom/rumi.ai/issues/1258)) ([298a713](https://github.com/Waitroom/waitroom/commit/298a713ed9ff85963ddd508a3c2f2a8f15f87bc4))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.17
* **@waitroom/common-api:** upgraded to 1.3.2
* **@waitroom/http-client:** upgraded to 1.10.16
* **@waitroom/models:** upgraded to 1.69.1
* **@waitroom/tests:** upgraded to 1.15.16
* **@waitroom/trixta-utils:** upgraded to 1.15.15
* **@waitroom/utils:** upgraded to 1.32.7

## @waitroom/common [1.120.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.120.0...@waitroom/common@1.120.1) (2023-02-17)


### Bug Fixes

* lint issues ([57aa47e](https://github.com/Waitroom/waitroom/commit/57aa47e2cfe9a555c97be7bd323d2157aab3eb49))

# @waitroom/common [1.120.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.119.0...@waitroom/common@1.120.0) (2023-02-17)


### Features

* chime-in ([fc7c956](https://github.com/Waitroom/waitroom/commit/fc7c956a24b78a9eb96fdf60c841bdf1b2f0cd9c)), closes [#1241](https://github.com/Waitroom/rumi.ai/issues/1241)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.16
* **@waitroom/common-api:** upgraded to 1.3.1
* **@waitroom/http-client:** upgraded to 1.10.15
* **@waitroom/models:** upgraded to 1.69.0
* **@waitroom/tests:** upgraded to 1.15.15
* **@waitroom/trixta-utils:** upgraded to 1.15.14
* **@waitroom/utils:** upgraded to 1.32.6

# @waitroom/common [1.119.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.118.2...@waitroom/common@1.119.0) (2023-02-16)


### Features

* client auth ([#1251](https://github.com/Waitroom/rumi.ai/issues/1251)) ([2bd2f4e](https://github.com/Waitroom/waitroom/commit/2bd2f4ebb85f523b759485b034157827571d662b))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.15
* **@waitroom/common-api:** upgraded to 1.3.0

## @waitroom/common [1.118.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.118.1...@waitroom/common@1.118.2) (2023-02-15)





### Dependencies

* **@waitroom/config:** upgraded to 1.15.3

## @waitroom/common [1.118.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.118.0...@waitroom/common@1.118.1) (2023-02-15)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.14
* **@waitroom/common-api:** upgraded to 1.2.7
* **@waitroom/http-client:** upgraded to 1.10.14
* **@waitroom/models:** upgraded to 1.68.4
* **@waitroom/tests:** upgraded to 1.15.14
* **@waitroom/trixta-utils:** upgraded to 1.15.13
* **@waitroom/utils:** upgraded to 1.32.5

# @waitroom/common [1.118.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.117.3...@waitroom/common@1.118.0) (2023-02-15)


### Features

* adds chime-in selectors ([#1249](https://github.com/Waitroom/rumi.ai/issues/1249)) ([94c43a8](https://github.com/Waitroom/waitroom/commit/94c43a84a67c28f87abe610ffa4177fb68e1c604))

## @waitroom/common [1.117.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.117.2...@waitroom/common@1.117.3) (2023-02-11)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.13
* **@waitroom/common-api:** upgraded to 1.2.6
* **@waitroom/http-client:** upgraded to 1.10.13
* **@waitroom/models:** upgraded to 1.68.3
* **@waitroom/tests:** upgraded to 1.15.13
* **@waitroom/trixta-utils:** upgraded to 1.15.12
* **@waitroom/utils:** upgraded to 1.32.4

## @waitroom/common [1.117.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.117.1...@waitroom/common@1.117.2) (2023-02-10)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.12
* **@waitroom/common-api:** upgraded to 1.2.5
* **@waitroom/http-client:** upgraded to 1.10.12
* **@waitroom/models:** upgraded to 1.68.2
* **@waitroom/tests:** upgraded to 1.15.12
* **@waitroom/trixta-utils:** upgraded to 1.15.11
* **@waitroom/utils:** upgraded to 1.32.3

## @waitroom/common [1.117.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.117.0...@waitroom/common@1.117.1) (2023-02-09)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.11
* **@waitroom/common-api:** upgraded to 1.2.4
* **@waitroom/http-client:** upgraded to 1.10.11
* **@waitroom/models:** upgraded to 1.68.1
* **@waitroom/tests:** upgraded to 1.15.11
* **@waitroom/trixta-utils:** upgraded to 1.15.10
* **@waitroom/utils:** upgraded to 1.32.2

# @waitroom/common [1.117.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.116.0...@waitroom/common@1.117.0) (2023-02-09)


### Features

* added footnote to vertical section ([f2b9a4c](https://github.com/Waitroom/waitroom/commit/f2b9a4cfde1edece459fd23317e3bf06e2efaa21))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.10
* **@waitroom/common-api:** upgraded to 1.2.3
* **@waitroom/http-client:** upgraded to 1.10.10
* **@waitroom/models:** upgraded to 1.68.0
* **@waitroom/tests:** upgraded to 1.15.10
* **@waitroom/trixta-utils:** upgraded to 1.15.9
* **@waitroom/utils:** upgraded to 1.32.1

# @waitroom/common [1.116.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.115.1...@waitroom/common@1.116.0) (2023-02-08)


### Features

* add users array to aggregate emoji reactions ([ef705d1](https://github.com/Waitroom/waitroom/commit/ef705d1d7aad89be6532053e504da95112870653))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.9
* **@waitroom/common-api:** upgraded to 1.2.2
* **@waitroom/http-client:** upgraded to 1.10.9
* **@waitroom/models:** upgraded to 1.67.0
* **@waitroom/tests:** upgraded to 1.15.9
* **@waitroom/trixta-utils:** upgraded to 1.15.8
* **@waitroom/utils:** upgraded to 1.32.0

## @waitroom/common [1.115.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.115.0...@waitroom/common@1.115.1) (2023-02-08)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.8
* **@waitroom/common-api:** upgraded to 1.2.1
* **@waitroom/http-client:** upgraded to 1.10.8
* **@waitroom/models:** upgraded to 1.66.0
* **@waitroom/tests:** upgraded to 1.15.8
* **@waitroom/trixta-utils:** upgraded to 1.15.7
* **@waitroom/utils:** upgraded to 1.31.8

# @waitroom/common [1.115.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.114.0...@waitroom/common@1.115.0) (2023-02-08)


### Features

* add types for recording apis ([#1238](https://github.com/Waitroom/rumi.ai/issues/1238)) ([926c7c3](https://github.com/Waitroom/waitroom/commit/926c7c3479c555c3ebe88f796553395fe37b1b94))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.7
* **@waitroom/common-api:** upgraded to 1.2.0

# @waitroom/common [1.114.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.113.0...@waitroom/common@1.114.0) (2023-02-08)


### Features

* adds useChimeInNotifications hook to common ([#1235](https://github.com/Waitroom/rumi.ai/issues/1235)) ([ad3a0c9](https://github.com/Waitroom/waitroom/commit/ad3a0c9b3cabad68b4e7d6dbce6413f1f191fdb4))

# @waitroom/common [1.113.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.112.0...@waitroom/common@1.113.0) (2023-02-07)


### Features

* Onboarding and dashboard simplification ([#1231](https://github.com/Waitroom/rumi.ai/issues/1231)) ([46eaa6d](https://github.com/Waitroom/waitroom/commit/46eaa6d4d1510adba60e9bc6467203043351d522))

# @waitroom/common [1.112.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.111.8...@waitroom/common@1.112.0) (2023-02-06)


### Features

* adds models & common package chime-in changes ([#1234](https://github.com/Waitroom/rumi.ai/issues/1234)) ([7e6ac71](https://github.com/Waitroom/waitroom/commit/7e6ac711ddb80d3302fa7698c36bdbfb9a3e27af))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.6
* **@waitroom/common-api:** upgraded to 1.1.8
* **@waitroom/http-client:** upgraded to 1.10.7
* **@waitroom/models:** upgraded to 1.65.0
* **@waitroom/tests:** upgraded to 1.15.7
* **@waitroom/trixta-utils:** upgraded to 1.15.6
* **@waitroom/utils:** upgraded to 1.31.7

## @waitroom/common [1.111.8](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.111.7...@waitroom/common@1.111.8) (2023-01-27)


### Bug Fixes

* auth login issue on session pages, cms ui bugs ([214dcf4](https://github.com/Waitroom/waitroom/commit/214dcf4442e613d1a803397735771bba3c441c40))

## @waitroom/common [1.111.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.111.6...@waitroom/common@1.111.7) (2023-01-26)


### Bug Fixes

* cms button ([173930b](https://github.com/Waitroom/waitroom/commit/173930b285321e1376c5139704e2aaf6c7d04794))

## @waitroom/common [1.111.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.111.5...@waitroom/common@1.111.6) (2023-01-25)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.5
* **@waitroom/common-api:** upgraded to 1.1.7
* **@waitroom/http-client:** upgraded to 1.10.6
* **@waitroom/models:** upgraded to 1.64.1
* **@waitroom/tests:** upgraded to 1.15.6
* **@waitroom/trixta-utils:** upgraded to 1.15.5
* **@waitroom/utils:** upgraded to 1.31.6

## @waitroom/common [1.111.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.111.4...@waitroom/common@1.111.5) (2023-01-25)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.4
* **@waitroom/common-api:** upgraded to 1.1.6
* **@waitroom/http-client:** upgraded to 1.10.5
* **@waitroom/models:** upgraded to 1.64.0
* **@waitroom/tests:** upgraded to 1.15.5
* **@waitroom/trixta-utils:** upgraded to 1.15.4
* **@waitroom/utils:** upgraded to 1.31.5

## @waitroom/common [1.111.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.111.3...@waitroom/common@1.111.4) (2023-01-24)


### Bug Fixes

* packages, WEB-349 ([1edad47](https://github.com/Waitroom/waitroom/commit/1edad471139b8afbb12721d480e0d5a2acf343e1))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.3
* **@waitroom/common-api:** upgraded to 1.1.5
* **@waitroom/config:** upgraded to 1.15.2
* **@waitroom/http-client:** upgraded to 1.10.4
* **@waitroom/models:** upgraded to 1.63.3
* **@waitroom/tests:** upgraded to 1.15.4
* **@waitroom/trixta-utils:** upgraded to 1.15.3
* **@waitroom/utils:** upgraded to 1.31.4

## @waitroom/common [1.111.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.111.2...@waitroom/common@1.111.3) (2023-01-24)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.2
* **@waitroom/common-api:** upgraded to 1.1.4
* **@waitroom/config:** upgraded to 1.15.1
* **@waitroom/http-client:** upgraded to 1.10.3
* **@waitroom/models:** upgraded to 1.63.2
* **@waitroom/tests:** upgraded to 1.15.3
* **@waitroom/trixta-utils:** upgraded to 1.15.2
* **@waitroom/utils:** upgraded to 1.31.3

## @waitroom/common [1.111.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.111.1...@waitroom/common@1.111.2) (2023-01-20)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.1
* **@waitroom/common-api:** upgraded to 1.1.3
* **@waitroom/http-client:** upgraded to 1.10.2
* **@waitroom/models:** upgraded to 1.63.1
* **@waitroom/tests:** upgraded to 1.15.2
* **@waitroom/trixta-utils:** upgraded to 1.15.1
* **@waitroom/utils:** upgraded to 1.31.2

## @waitroom/common [1.111.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.111.0...@waitroom/common@1.111.1) (2023-01-20)

# @waitroom/common [1.111.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.110.0...@waitroom/common@1.111.0) (2023-01-20)


### Features

* add timestamp to individual emoji reactions ([82a3678](https://github.com/Waitroom/waitroom/commit/82a3678ccfdebf004725f5d1a65e9ea038db79aa))

# @waitroom/common [1.110.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.109.2...@waitroom/common@1.110.0) (2023-01-19)


### Features

* update and replace deprecated google package ([d3b2d5d](https://github.com/Waitroom/waitroom/commit/d3b2d5d193a6ca04f733cbfe2275acd59f3480f6))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.32.0
* **@waitroom/common-api:** upgraded to 1.1.2
* **@waitroom/trixta-utils:** upgraded to 1.15.0

## @waitroom/common [1.109.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.109.1...@waitroom/common@1.109.2) (2023-01-17)


### Bug Fixes

* added missing auth state in redux store ([#1206](https://github.com/Waitroom/rumi.ai/issues/1206)) ([c562fca](https://github.com/Waitroom/waitroom/commit/c562fca4e873c8a0f1ce794567c1b4decd06eba2))

## @waitroom/common [1.109.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.109.0...@waitroom/common@1.109.1) (2023-01-16)


### Bug Fixes

* individual reaction animation issue ([7712677](https://github.com/Waitroom/waitroom/commit/7712677f75af19df9e95b651529cfeb96716d80f))

# @waitroom/common [1.109.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.108.0...@waitroom/common@1.109.0) (2023-01-15)


### Features

* Project/website 3.0 ([#1203](https://github.com/Waitroom/rumi.ai/issues/1203)) ([c4cdc27](https://github.com/Waitroom/waitroom/commit/c4cdc273337b2e2b47531d03b6c790d983f6347d)), closes [#1194](https://github.com/Waitroom/rumi.ai/issues/1194) [#1196](https://github.com/Waitroom/rumi.ai/issues/1196)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.31.1
* **@waitroom/common-api:** upgraded to 1.1.1
* **@waitroom/config:** upgraded to 1.15.0
* **@waitroom/http-client:** upgraded to 1.10.1
* **@waitroom/models:** upgraded to 1.63.0
* **@waitroom/tests:** upgraded to 1.15.1
* **@waitroom/trixta-utils:** upgraded to 1.14.1
* **@waitroom/utils:** upgraded to 1.31.1

# @waitroom/common [1.108.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.107.4...@waitroom/common@1.108.0) (2023-01-13)


### Features

* CRE-1235 update auth service ([#1114](https://github.com/Waitroom/rumi.ai/issues/1114)) ([392073b](https://github.com/Waitroom/waitroom/commit/392073b50c4458441ec1b98112ef0de8778d6554)), closes [#1125](https://github.com/Waitroom/rumi.ai/issues/1125) [#1128](https://github.com/Waitroom/rumi.ai/issues/1128) [#1182](https://github.com/Waitroom/rumi.ai/issues/1182) [#1183](https://github.com/Waitroom/rumi.ai/issues/1183) [#1184](https://github.com/Waitroom/rumi.ai/issues/1184) [#1185](https://github.com/Waitroom/rumi.ai/issues/1185) [#1187](https://github.com/Waitroom/rumi.ai/issues/1187) [#1189](https://github.com/Waitroom/rumi.ai/issues/1189)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.31.0
* **@waitroom/common-api:** upgraded to 1.1.0
* **@waitroom/config:** upgraded to 1.14.0
* **@waitroom/http-client:** upgraded to 1.10.0
* **@waitroom/models:** upgraded to 1.62.0
* **@waitroom/tests:** upgraded to 1.15.0
* **@waitroom/trixta-utils:** upgraded to 1.14.0
* **@waitroom/utils:** upgraded to 1.31.0

## @waitroom/common [1.107.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.107.3...@waitroom/common@1.107.4) (2023-01-07)

## @waitroom/common [1.107.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.107.2...@waitroom/common@1.107.3) (2023-01-05)


### Bug Fixes

* individual emoji reactions animation ([2544c15](https://github.com/Waitroom/waitroom/commit/2544c156573845c79fb63115fadc1753fac3a32a))

## @waitroom/common [1.107.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.107.1...@waitroom/common@1.107.2) (2022-12-22)


### Bug Fixes

* open emoji reactions popover on hover and no tooltip for authenticated users ([686f20b](https://github.com/Waitroom/waitroom/commit/686f20b612e81a97204c3caa273e2dde3fdfb733))

## @waitroom/common [1.107.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.107.0...@waitroom/common@1.107.1) (2022-12-20)


### Bug Fixes

* environment variables and test with log ([8237e67](https://github.com/Waitroom/waitroom/commit/8237e670cb5d252cc0edf224e150a09e9d0a2b4d))

# @waitroom/common [1.107.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.106.10...@waitroom/common@1.107.0) (2022-12-16)


### Features

* added menu links to visit home page ([#1152](https://github.com/Waitroom/rumi.ai/issues/1152)) ([60a6bd8](https://github.com/Waitroom/waitroom/commit/60a6bd8e54a4c9a6b780adee13887627cd493bc8))

## @waitroom/common [1.106.10](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.106.9...@waitroom/common@1.106.10) (2022-12-15)

## @waitroom/common [1.106.9](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.106.8...@waitroom/common@1.106.9) (2022-12-15)


### Bug Fixes

* redirect request access session in your list ([539c986](https://github.com/Waitroom/waitroom/commit/539c98672cee31c61f36d27f5ed2223229047437))
* sentry issues ([5ace518](https://github.com/Waitroom/waitroom/commit/5ace51819576ef79f7597e6f966e07f8fe8256ca))

## @waitroom/common [1.106.8](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.106.7...@waitroom/common@1.106.8) (2022-12-14)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.30.3
* **@waitroom/config:** upgraded to 1.13.5
* **@waitroom/http-client:** upgraded to 1.9.1
* **@waitroom/models:** upgraded to 1.61.1
* **@waitroom/tests:** upgraded to 1.14.1
* **@waitroom/trixta-utils:** upgraded to 1.13.1
* **@waitroom/utils:** upgraded to 1.30.1

## @waitroom/common [1.106.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.106.6...@waitroom/common@1.106.7) (2022-12-14)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.30.2
* **@waitroom/http-client:** upgraded to 1.9.0
* **@waitroom/models:** upgraded to 1.61.0
* **@waitroom/tests:** upgraded to 1.14.0
* **@waitroom/utils:** upgraded to 1.30.0
* **@waitroom/trixta-utils:** upgraded to 1.13.0

## @waitroom/common [1.106.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.106.5...@waitroom/common@1.106.6) (2022-12-09)


### Bug Fixes

* session form initial data ([#1153](https://github.com/Waitroom/rumi.ai/issues/1153)) ([b36cb38](https://github.com/Waitroom/waitroom/commit/b36cb38191f98a723d8a45944afe125d461c73e3))

## @waitroom/common [1.106.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.106.4...@waitroom/common@1.106.5) (2022-12-09)

## @waitroom/common [1.106.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.106.3...@waitroom/common@1.106.4) (2022-12-08)


### Bug Fixes

* viewer list memo, refactor useCallbacks hook ([b300d9a](https://github.com/Waitroom/waitroom/commit/b300d9ab4c6b01309060d66f4fb5ddc4fdc08276))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.30.1
* **@waitroom/config:** upgraded to 1.13.4
* **@waitroom/http-client:** upgraded to 1.8.31
* **@waitroom/models:** upgraded to 1.60.2
* **@waitroom/tests:** upgraded to 1.13.13
* **@waitroom/utils:** upgraded to 1.29.22
* **@waitroom/trixta-utils:** upgraded to 1.12.1

## @waitroom/common [1.106.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.106.2...@waitroom/common@1.106.3) (2022-12-08)


### Bug Fixes

* start for recurring session ([8e89085](https://github.com/Waitroom/waitroom/commit/8e890850d4f5bf1149ba173a807b5c89ea559321))

## @waitroom/common [1.106.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.106.1...@waitroom/common@1.106.2) (2022-12-08)


### Bug Fixes

* load screen share streams ([#1149](https://github.com/Waitroom/rumi.ai/issues/1149)) ([0b12cb2](https://github.com/Waitroom/waitroom/commit/0b12cb24b69df6f050076ce90444c8f6d75ddee2))

## @waitroom/common [1.106.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.106.0...@waitroom/common@1.106.1) (2022-12-06)


### Bug Fixes

* queue ui issue ([32764c3](https://github.com/Waitroom/waitroom/commit/32764c3d60ee88e5d4d4f8b99f468663d0275fe8))

# @waitroom/common [1.106.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.105.3...@waitroom/common@1.106.0) (2022-12-05)


### Features

* pass additionalData when joining a role ([3d3ed38](https://github.com/Waitroom/waitroom/commit/3d3ed387652dd4e97336bd3ff8c6ce5e60dc3993))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.30.0
* **@waitroom/trixta-utils:** upgraded to 1.12.0

## @waitroom/common [1.105.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.105.2...@waitroom/common@1.105.3) (2022-12-05)


### Bug Fixes

* possible fix for socket race condition ([92fe879](https://github.com/Waitroom/waitroom/commit/92fe8792747f586796bb34c64b91ea80c47e7b60))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.29.10
* **@waitroom/trixta-utils:** upgraded to 1.11.33

## @waitroom/common [1.105.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.105.1...@waitroom/common@1.105.2) (2022-12-01)


### Bug Fixes

* update response shape for requestUserInfo ([#1142](https://github.com/Waitroom/rumi.ai/issues/1142)) ([b3ce6ed](https://github.com/Waitroom/waitroom/commit/b3ce6edca543c45c7e6c80d56e8e77f0e9de2204))

## @waitroom/common [1.105.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.105.0...@waitroom/common@1.105.1) (2022-11-30)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.29.9
* **@waitroom/http-client:** upgraded to 1.8.30
* **@waitroom/models:** upgraded to 1.60.1
* **@waitroom/tests:** upgraded to 1.13.12
* **@waitroom/utils:** upgraded to 1.29.21
* **@waitroom/trixta-utils:** upgraded to 1.11.32

# @waitroom/common [1.105.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.104.8...@waitroom/common@1.105.0) (2022-11-30)


### Features

* cre 1137 viewer reactions web ([#1120](https://github.com/Waitroom/rumi.ai/issues/1120)) ([4867a4f](https://github.com/Waitroom/waitroom/commit/4867a4f472b7b0b19cbab5c5c14f23456de84be6)), closes [#1118](https://github.com/Waitroom/rumi.ai/issues/1118) [#1133](https://github.com/Waitroom/rumi.ai/issues/1133)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.29.8
* **@waitroom/http-client:** upgraded to 1.8.29
* **@waitroom/models:** upgraded to 1.60.0
* **@waitroom/tests:** upgraded to 1.13.11
* **@waitroom/utils:** upgraded to 1.29.20
* **@waitroom/trixta-utils:** upgraded to 1.11.31

## @waitroom/common [1.104.8](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.104.7...@waitroom/common@1.104.8) (2022-11-30)


### Bug Fixes

* fixed start session transition ([#1137](https://github.com/Waitroom/rumi.ai/issues/1137)) ([a0d3ee8](https://github.com/Waitroom/waitroom/commit/a0d3ee8ecb2e33076d5d8d59154a31d1dc79503b))

## @waitroom/common [1.104.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.104.6...@waitroom/common@1.104.7) (2022-11-29)


### Bug Fixes

* requestUserSessionInfo only use success response ([935d1b0](https://github.com/Waitroom/waitroom/commit/935d1b08a78359579746f16d826b6e28545f9838))

## @waitroom/common [1.104.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.104.5...@waitroom/common@1.104.6) (2022-11-25)


### Bug Fixes

* host entered session ([4489cdd](https://github.com/Waitroom/waitroom/commit/4489cdd305dfbd1a2c12319229c27f709053b58e))

## @waitroom/common [1.104.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.104.4...@waitroom/common@1.104.5) (2022-11-23)


### Bug Fixes

* setup current session issue ([e84dd13](https://github.com/Waitroom/waitroom/commit/e84dd136decf00ec8ff92ea7621f109bcc4f675c))

## @waitroom/common [1.104.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.104.3...@waitroom/common@1.104.4) (2022-11-22)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.29.7
* **@waitroom/config:** upgraded to 1.13.3
* **@waitroom/http-client:** upgraded to 1.8.28
* **@waitroom/models:** upgraded to 1.59.1
* **@waitroom/tests:** upgraded to 1.13.10
* **@waitroom/utils:** upgraded to 1.29.19
* **@waitroom/trixta-utils:** upgraded to 1.11.30

## @waitroom/common [1.104.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.104.2...@waitroom/common@1.104.3) (2022-11-12)


### Bug Fixes

* trigger release ([9882d73](https://github.com/Waitroom/waitroom/commit/9882d7345fb90a3c7156d76d12e2514d764d730d))

## @waitroom/common [1.104.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.104.1...@waitroom/common@1.104.2) (2022-11-11)


### Bug Fixes

* conflicting trixta types ([b4f29a2](https://github.com/Waitroom/waitroom/commit/b4f29a27c14e15f85916c0b8c004ea03f9d84c0e))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.29.6

## @waitroom/common [1.104.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.104.0...@waitroom/common@1.104.1) (2022-11-08)


### Bug Fixes

* host button loader ([b778dd6](https://github.com/Waitroom/waitroom/commit/b778dd6cfdfed9100b4a5d1842bfbe0d1e8c2304))

# @waitroom/common [1.104.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.103.4...@waitroom/common@1.104.0) (2022-11-08)


### Features

* hosting flow redesign ([#1100](https://github.com/Waitroom/rumi.ai/issues/1100)) ([202a917](https://github.com/Waitroom/waitroom/commit/202a91764879a745f7bed5ce9d5d19bda9559669))





### Dependencies

* **@waitroom/http-client:** upgraded to 1.8.27
* **@waitroom/models:** upgraded to 1.59.0
* **@waitroom/tests:** upgraded to 1.13.9
* **@waitroom/utils:** upgraded to 1.29.18
* **@waitroom/analytics:** upgraded to 1.29.5
* **@waitroom/trixta-utils:** upgraded to 1.11.29

## @waitroom/common [1.103.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.103.3...@waitroom/common@1.103.4) (2022-11-02)


### Bug Fixes

* refactor tags input ([6dbc19d](https://github.com/Waitroom/waitroom/commit/6dbc19dfdb1dc2690a682ec3c854f6d8f13c0612))





### Dependencies

* **@waitroom/http-client:** upgraded to 1.8.26
* **@waitroom/utils:** upgraded to 1.29.17
* **@waitroom/analytics:** upgraded to 1.29.4
* **@waitroom/trixta-utils:** upgraded to 1.11.28

## @waitroom/common [1.103.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.103.2...@waitroom/common@1.103.3) (2022-10-28)





### Dependencies

* **@waitroom/http-client:** upgraded to 1.8.25
* **@waitroom/models:** upgraded to 1.58.1
* **@waitroom/tests:** upgraded to 1.13.8
* **@waitroom/utils:** upgraded to 1.29.16
* **@waitroom/analytics:** upgraded to 1.29.3
* **@waitroom/trixta-utils:** upgraded to 1.11.27

## @waitroom/common [1.103.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.103.1...@waitroom/common@1.103.2) (2022-10-27)


### Bug Fixes

* now fetch sessoin user info when starting the session ([ebd4274](https://github.com/Waitroom/waitroom/commit/ebd42741aabdbe185603bf3a3a73c9cd99980155))

## @waitroom/common [1.103.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.103.0...@waitroom/common@1.103.1) (2022-10-17)


### Bug Fixes

* carousel navigation ([a052d21](https://github.com/Waitroom/waitroom/commit/a052d21f1e48cb331c8faa8b7dd9284dc405e2b9))

# @waitroom/common [1.103.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.102.1...@waitroom/common@1.103.0) (2022-10-13)


### Features

* now producer can share screen ([#1076](https://github.com/Waitroom/rumi.ai/issues/1076)) ([957f6ad](https://github.com/Waitroom/waitroom/commit/957f6adacbdf5d74138cd81e5ba9ab5074b89f88))





### Dependencies

* **@waitroom/http-client:** upgraded to 1.8.24
* **@waitroom/models:** upgraded to 1.58.0
* **@waitroom/tests:** upgraded to 1.13.7
* **@waitroom/utils:** upgraded to 1.29.15
* **@waitroom/analytics:** upgraded to 1.29.2
* **@waitroom/trixta-utils:** upgraded to 1.11.26

## @waitroom/common [1.102.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.102.0...@waitroom/common@1.102.1) (2022-10-11)


### Bug Fixes

* testlio ([#1077](https://github.com/Waitroom/rumi.ai/issues/1077)) ([2d66751](https://github.com/Waitroom/waitroom/commit/2d66751d2506717189586b1757e6f82aec42b13c))

# @waitroom/common [1.102.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.101.5...@waitroom/common@1.102.0) (2022-10-07)


### Features

* component is more reliable than saga ([3e56c62](https://github.com/Waitroom/waitroom/commit/3e56c6283c977d84e5cd86c14c7b322cc0f983f1))

## @waitroom/common [1.101.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.101.4...@waitroom/common@1.101.5) (2022-10-06)





### Dependencies

* **@waitroom/config:** upgraded to 1.13.2
* **@waitroom/http-client:** upgraded to 1.8.23
* **@waitroom/models:** upgraded to 1.57.1
* **@waitroom/tests:** upgraded to 1.13.6
* **@waitroom/utils:** upgraded to 1.29.14
* **@waitroom/analytics:** upgraded to 1.29.1
* **@waitroom/trixta-utils:** upgraded to 1.11.25

## @waitroom/common [1.101.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.101.3...@waitroom/common@1.101.4) (2022-10-05)


### Bug Fixes

* request_guest_stream in saga ([20e2528](https://github.com/Waitroom/waitroom/commit/20e25288394a2032c7790bb11eff6144ae96335f))

## @waitroom/common [1.101.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.101.2...@waitroom/common@1.101.3) (2022-10-03)





### Dependencies

* **@waitroom/http-client:** upgraded to 1.8.22
* **@waitroom/models:** upgraded to 1.57.0
* **@waitroom/tests:** upgraded to 1.13.5
* **@waitroom/utils:** upgraded to 1.29.13
* **@waitroom/analytics:** upgraded to 1.29.0
* **@waitroom/trixta-utils:** upgraded to 1.11.24

## @waitroom/common [1.101.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.101.1...@waitroom/common@1.101.2) (2022-09-29)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.28.28

## @waitroom/common [1.101.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.101.0...@waitroom/common@1.101.1) (2022-09-29)


### Bug Fixes

* channel connection refresh ([#1045](https://github.com/Waitroom/rumi.ai/issues/1045)) ([3a6a57e](https://github.com/Waitroom/waitroom/commit/3a6a57ebaa286dcd653fbf04c55aa81aa5445f0b))
* tests ([84a1b93](https://github.com/Waitroom/waitroom/commit/84a1b93675e2243dc7eb6fe6cff0955dce246698))





### Dependencies

* **@waitroom/config:** upgraded to 1.13.1
* **@waitroom/http-client:** upgraded to 1.8.21
* **@waitroom/models:** upgraded to 1.56.1
* **@waitroom/tests:** upgraded to 1.13.4
* **@waitroom/utils:** upgraded to 1.29.12
* **@waitroom/analytics:** upgraded to 1.28.27
* **@waitroom/trixta-utils:** upgraded to 1.11.23

# @waitroom/common [1.101.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.100.0...@waitroom/common@1.101.0) (2022-09-27)


### Features

* common chanages for screen share feature ([#1037](https://github.com/Waitroom/rumi.ai/issues/1037)) ([85819ee](https://github.com/Waitroom/waitroom/commit/85819ee7857f2d1f50726c7f38f2f35e22e7c94e))





### Dependencies

* **@waitroom/http-client:** upgraded to 1.8.20
* **@waitroom/models:** upgraded to 1.56.0
* **@waitroom/tests:** upgraded to 1.13.3
* **@waitroom/utils:** upgraded to 1.29.11
* **@waitroom/analytics:** upgraded to 1.28.26
* **@waitroom/trixta-utils:** upgraded to 1.11.22

# @waitroom/common [1.100.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.99.4...@waitroom/common@1.100.0) (2022-09-21)


### Features

* common changes for screen share ([#1025](https://github.com/Waitroom/rumi.ai/issues/1025)) ([6bfcfe0](https://github.com/Waitroom/waitroom/commit/6bfcfe02164e7dc9b50cb2a669925a86cdc071d9))





### Dependencies

* **@waitroom/http-client:** upgraded to 1.8.19
* **@waitroom/models:** upgraded to 1.55.0
* **@waitroom/tests:** upgraded to 1.13.2
* **@waitroom/utils:** upgraded to 1.29.10
* **@waitroom/analytics:** upgraded to 1.28.25
* **@waitroom/trixta-utils:** upgraded to 1.11.21

## @waitroom/common [1.99.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.99.3...@waitroom/common@1.99.4) (2022-09-19)


### Bug Fixes

* on login retain state ([660cebb](https://github.com/Waitroom/waitroom/commit/660cebba7a7439a7b567fff6d40d29b290e8e881))

## @waitroom/common [1.99.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.99.2...@waitroom/common@1.99.3) (2022-09-19)


### Bug Fixes

* selectRtmpUrls ([faf3a91](https://github.com/Waitroom/waitroom/commit/faf3a91bc933dacb3e266fb8780cfc68643ec072))

## @waitroom/common [1.99.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.99.1...@waitroom/common@1.99.2) (2022-09-16)





### Dependencies

* **@waitroom/http-client:** upgraded to 1.8.18
* **@waitroom/models:** upgraded to 1.54.0
* **@waitroom/tests:** upgraded to 1.13.1
* **@waitroom/utils:** upgraded to 1.29.9
* **@waitroom/analytics:** upgraded to 1.28.24
* **@waitroom/trixta-utils:** upgraded to 1.11.20

## @waitroom/common [1.99.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.99.0...@waitroom/common@1.99.1) (2022-09-13)





### Dependencies

* **@waitroom/http-client:** upgraded to 1.8.17
* **@waitroom/utils:** upgraded to 1.29.8
* **@waitroom/analytics:** upgraded to 1.28.23
* **@waitroom/trixta-utils:** upgraded to 1.11.19

# @waitroom/common [1.99.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.98.1...@waitroom/common@1.99.0) (2022-09-08)


### Bug Fixes

* test ([cfb6b2b](https://github.com/Waitroom/waitroom/commit/cfb6b2b9de28272c4b28d0bcc3b0aa7c6955dd5e))


### Features

* rename presentaiton to screen share ([cf5cff4](https://github.com/Waitroom/waitroom/commit/cf5cff450becbf4db7591c455fbd028c12372cbe))





### Dependencies

* **@waitroom/http-client:** upgraded to 1.8.16
* **@waitroom/models:** upgraded to 1.53.0
* **@waitroom/tests:** upgraded to 1.13.0
* **@waitroom/utils:** upgraded to 1.29.7
* **@waitroom/analytics:** upgraded to 1.28.22
* **@waitroom/trixta-utils:** upgraded to 1.11.18

## @waitroom/common [1.98.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.98.0...@waitroom/common@1.98.1) (2022-09-07)


### Bug Fixes

* authenticated users firing resend_reactions twice ([79f8d76](https://github.com/Waitroom/waitroom/commit/79f8d76a69c4ced907d6916980230bac3b21eb67))

# @waitroom/common [1.98.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.97.1...@waitroom/common@1.98.0) (2022-09-06)


### Features

* revert payload change for request_user_session_info ([5f4852b](https://github.com/Waitroom/waitroom/commit/5f4852b37c86e2ca9fad1caebca85f5177eea10f))





### Dependencies

* **@waitroom/http-client:** upgraded to 1.8.15
* **@waitroom/models:** upgraded to 1.52.0
* **@waitroom/tests:** upgraded to 1.12.4
* **@waitroom/utils:** upgraded to 1.29.6
* **@waitroom/analytics:** upgraded to 1.28.21
* **@waitroom/trixta-utils:** upgraded to 1.11.17

## @waitroom/common [1.97.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.97.0...@waitroom/common@1.97.1) (2022-09-06)





### Dependencies

* **@waitroom/config:** upgraded to 1.13.0
* **@waitroom/http-client:** upgraded to 1.8.14
* **@waitroom/models:** upgraded to 1.51.0
* **@waitroom/tests:** upgraded to 1.12.3
* **@waitroom/utils:** upgraded to 1.29.5
* **@waitroom/analytics:** upgraded to 1.28.20
* **@waitroom/trixta-utils:** upgraded to 1.11.16

# @waitroom/common [1.97.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.96.0...@waitroom/common@1.97.0) (2022-09-05)


### Features

* screen presentation details ([318c317](https://github.com/Waitroom/waitroom/commit/318c3179a4609434ef99797f8452a53fee1547a2))





### Dependencies

* **@waitroom/http-client:** upgraded to 1.8.13
* **@waitroom/models:** upgraded to 1.50.0
* **@waitroom/tests:** upgraded to 1.12.2
* **@waitroom/utils:** upgraded to 1.29.4
* **@waitroom/analytics:** upgraded to 1.28.19
* **@waitroom/trixta-utils:** upgraded to 1.11.15

# @waitroom/common [1.96.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.95.0...@waitroom/common@1.96.0) (2022-09-05)


### Features

* share presentation ([8fdb77b](https://github.com/Waitroom/waitroom/commit/8fdb77bacbc2230f4c31b396a30091b595109499))





### Dependencies

* **@waitroom/http-client:** upgraded to 1.8.12
* **@waitroom/models:** upgraded to 1.49.0
* **@waitroom/tests:** upgraded to 1.12.1
* **@waitroom/utils:** upgraded to 1.29.3
* **@waitroom/analytics:** upgraded to 1.28.18
* **@waitroom/trixta-utils:** upgraded to 1.11.14

# @waitroom/common [1.95.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.94.4...@waitroom/common@1.95.0) (2022-08-29)


### Features

* CON-638 write unit tests for waitroom/common ([#996](https://github.com/Waitroom/rumi.ai/issues/996)) ([eecaffd](https://github.com/Waitroom/waitroom/commit/eecaffdb57cae7aa75bb26071ec8cd60b1c428ca))





### Dependencies

* **@waitroom/tests:** upgraded to 1.12.0
* **@waitroom/analytics:** upgraded to 1.28.17

## @waitroom/common [1.94.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.94.3...@waitroom/common@1.94.4) (2022-08-26)





### Dependencies

* **@waitroom/http-client:** upgraded to 1.8.11
* **@waitroom/models:** upgraded to 1.48.0
* **@waitroom/tests:** upgraded to 1.11.11
* **@waitroom/utils:** upgraded to 1.29.2
* **@waitroom/analytics:** upgraded to 1.28.16
* **@waitroom/trixta-utils:** upgraded to 1.11.13

## @waitroom/common [1.94.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.94.2...@waitroom/common@1.94.3) (2022-08-25)

## @waitroom/common [1.94.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.94.1...@waitroom/common@1.94.2) (2022-08-23)





### Dependencies

* **@waitroom/config:** upgraded to 1.12.1
* **@waitroom/http-client:** upgraded to 1.8.10
* **@waitroom/models:** upgraded to 1.47.1
* **@waitroom/tests:** upgraded to 1.11.10
* **@waitroom/utils:** upgraded to 1.29.1
* **@waitroom/analytics:** upgraded to 1.28.15
* **@waitroom/trixta-utils:** upgraded to 1.11.12

## @waitroom/common [1.94.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.94.0...@waitroom/common@1.94.1) (2022-08-18)


### Bug Fixes

* ci and added lighthouse report ([69a286a](https://github.com/Waitroom/waitroom/commit/69a286aff938b45f7a808eacad6ae6209f334846))





### Dependencies

* **@waitroom/config:** upgraded to 1.12.0
* **@waitroom/http-client:** upgraded to 1.8.9
* **@waitroom/models:** upgraded to 1.47.0
* **@waitroom/tests:** upgraded to 1.11.9
* **@waitroom/utils:** upgraded to 1.29.0
* **@waitroom/analytics:** upgraded to 1.28.14
* **@waitroom/trixta-utils:** upgraded to 1.11.11

# @waitroom/common [1.94.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.93.1...@waitroom/common@1.94.0) (2022-08-16)


### Features

* update the pending requests across session producers on interaction ([8097ac6](https://github.com/Waitroom/waitroom/commit/8097ac6d4691cd23f27ee4b246f4893831dc42da))





### Dependencies

* **@waitroom/utils:** upgraded to 1.28.8
* **@waitroom/http-client:** upgraded to 1.8.8
* **@waitroom/models:** upgraded to 1.46.0
* **@waitroom/tests:** upgraded to 1.11.8
* **@waitroom/analytics:** upgraded to 1.28.13
* **@waitroom/trixta-utils:** upgraded to 1.11.10

## @waitroom/common [1.93.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.93.0...@waitroom/common@1.93.1) (2022-08-16)





### Dependencies

* **@waitroom/utils:** upgraded to 1.28.7
* **@waitroom/http-client:** upgraded to 1.8.7
* **@waitroom/models:** upgraded to 1.45.1
* **@waitroom/tests:** upgraded to 1.11.7
* **@waitroom/analytics:** upgraded to 1.28.12
* **@waitroom/trixta-utils:** upgraded to 1.11.9

# @waitroom/common [1.93.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.92.5...@waitroom/common@1.93.0) (2022-08-11)


### Features

* add common translation for session access ([#972](https://github.com/Waitroom/rumi.ai/issues/972)) ([61720ed](https://github.com/Waitroom/waitroom/commit/61720ed252d71627ef808b95cf844023bf7172cb))

## @waitroom/common [1.92.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.92.4...@waitroom/common@1.92.5) (2022-08-10)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.28.11

## @waitroom/common [1.92.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.92.3...@waitroom/common@1.92.4) (2022-08-09)


### Bug Fixes

* session blocking is only for logged in users ([8b6c170](https://github.com/Waitroom/waitroom/commit/8b6c170a292c7155f96384bfb868459f3b79308c))

## @waitroom/common [1.92.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.92.2...@waitroom/common@1.92.3) (2022-08-08)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.28.10

## @waitroom/common [1.92.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.92.1...@waitroom/common@1.92.2) (2022-08-04)


### Bug Fixes

* dual tabs for host or any user blocking others ([e57a0df](https://github.com/Waitroom/waitroom/commit/e57a0dff203e9469b7ddab50d2a4476bfcf62d5f))

## @waitroom/common [1.92.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.92.0...@waitroom/common@1.92.1) (2022-08-03)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.28.9

# @waitroom/common [1.92.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.91.0...@waitroom/common@1.92.0) (2022-08-03)


### Features

* CRE-723 show how many people the session is ([#952](https://github.com/Waitroom/rumi.ai/issues/952)) ([26ec7f8](https://github.com/Waitroom/waitroom/commit/26ec7f83187c40db5c98d056ce736cc151c9ce43))





### Dependencies

* **@waitroom/utils:** upgraded to 1.28.6
* **@waitroom/http-client:** upgraded to 1.8.6
* **@waitroom/models:** upgraded to 1.45.0
* **@waitroom/tests:** upgraded to 1.11.6
* **@waitroom/analytics:** upgraded to 1.28.8
* **@waitroom/trixta-utils:** upgraded to 1.11.8

# @waitroom/common [1.91.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.90.1...@waitroom/common@1.91.0) (2022-08-03)


### Features

* hidden session should transition to session screen when granted access ([c5533be](https://github.com/Waitroom/waitroom/commit/c5533bee79f5f94b444dbc7de5324c07a54ea4bd))

## @waitroom/common [1.90.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.90.0...@waitroom/common@1.90.1) (2022-08-01)


### Bug Fixes

* persist stream ids ([#951](https://github.com/Waitroom/rumi.ai/issues/951)) ([a818019](https://github.com/Waitroom/waitroom/commit/a8180195fc8f2ad1bafa63c1cffac34004ad2947))

# @waitroom/common [1.90.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.89.11...@waitroom/common@1.90.0) (2022-08-01)


### Features

* stream ids map ([#949](https://github.com/Waitroom/rumi.ai/issues/949)) ([ee996b0](https://github.com/Waitroom/waitroom/commit/ee996b041d29878d52f6087cca1632fe325aa09f))





### Dependencies

* **@waitroom/utils:** upgraded to 1.28.5
* **@waitroom/http-client:** upgraded to 1.8.5
* **@waitroom/models:** upgraded to 1.44.0
* **@waitroom/tests:** upgraded to 1.11.5
* **@waitroom/analytics:** upgraded to 1.28.7
* **@waitroom/trixta-utils:** upgraded to 1.11.7

## @waitroom/common [1.89.11](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.89.10...@waitroom/common@1.89.11) (2022-07-28)


### Bug Fixes

* auth refresh token ([#942](https://github.com/Waitroom/rumi.ai/issues/942)) ([36a2145](https://github.com/Waitroom/waitroom/commit/36a21459e8f7447726f58f76b3bb7c4a630f690e))

## @waitroom/common [1.89.10](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.89.9...@waitroom/common@1.89.10) (2022-07-27)





### Dependencies

* **@waitroom/config:** upgraded to 1.11.0

## @waitroom/common [1.89.9](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.89.8...@waitroom/common@1.89.9) (2022-07-27)


### Bug Fixes

* featured sessions not working ([f1177cf](https://github.com/Waitroom/waitroom/commit/f1177cfeb44f60060faef9e6bc9f095446d05011))

## @waitroom/common [1.89.8](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.89.7...@waitroom/common@1.89.8) (2022-07-25)





### Dependencies

* **@waitroom/utils:** upgraded to 1.28.4
* **@waitroom/config:** upgraded to 1.10.4
* **@waitroom/http-client:** upgraded to 1.8.4
* **@waitroom/models:** upgraded to 1.43.2
* **@waitroom/tests:** upgraded to 1.11.4
* **@waitroom/analytics:** upgraded to 1.28.6
* **@waitroom/trixta-utils:** upgraded to 1.11.6

## @waitroom/common [1.89.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.89.6...@waitroom/common@1.89.7) (2022-07-25)





### Dependencies

* **@waitroom/config:** upgraded to 1.10.3

## @waitroom/common [1.89.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.89.5...@waitroom/common@1.89.6) (2022-07-19)





### Dependencies

* **@waitroom/tests:** upgraded to 1.11.3
* **@waitroom/analytics:** upgraded to 1.28.5

## @waitroom/common [1.89.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.89.4...@waitroom/common@1.89.5) (2022-07-18)





### Dependencies

* **@waitroom/utils:** upgraded to 1.28.3
* **@waitroom/config:** upgraded to 1.10.2
* **@waitroom/http-client:** upgraded to 1.8.3
* **@waitroom/models:** upgraded to 1.43.1
* **@waitroom/tests:** upgraded to 1.11.2
* **@waitroom/analytics:** upgraded to 1.28.4
* **@waitroom/trixta-utils:** upgraded to 1.11.5

## @waitroom/common [1.89.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.89.3...@waitroom/common@1.89.4) (2022-07-12)


### Bug Fixes

* unauthed user on a active session redirected away ([9acb4ea](https://github.com/Waitroom/waitroom/commit/9acb4ea61c9673cbc7793cb92b8df5a057289ded))

## @waitroom/common [1.89.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.89.2...@waitroom/common@1.89.3) (2022-07-11)


### Bug Fixes

* use everyone_authed for sesion blocking ([c39e3af](https://github.com/Waitroom/waitroom/commit/c39e3afadf0a7a0821c3ea8d96452b280a0ac841))

## @waitroom/common [1.89.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.89.1...@waitroom/common@1.89.2) (2022-07-08)

## @waitroom/common [1.89.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.89.0...@waitroom/common@1.89.1) (2022-07-07)





### Dependencies

* **@waitroom/config:** upgraded to 1.10.1

# @waitroom/common [1.89.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.88.1...@waitroom/common@1.89.0) (2022-07-07)


### Features

* account deletion request action ([#903](https://github.com/Waitroom/rumi.ai/issues/903)) ([f22c2d2](https://github.com/Waitroom/waitroom/commit/f22c2d20b73f6c2b3781bc6644ac6d4d4303a1b3))

## @waitroom/common [1.88.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.88.0...@waitroom/common@1.88.1) (2022-07-07)





### Dependencies

* **@waitroom/trixta-utils:** upgraded to 1.11.4

# @waitroom/common [1.88.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.87.3...@waitroom/common@1.88.0) (2022-07-06)


### Bug Fixes

* fixed bugs and issues ([#892](https://github.com/Waitroom/rumi.ai/issues/892)) ([9f46895](https://github.com/Waitroom/waitroom/commit/9f468955de1d7e87afa53bab2301703abf72483e))


### Features

* setup user if granted access for a session ([c6d9a43](https://github.com/Waitroom/waitroom/commit/c6d9a430ac0862571952fdbe273d5c106049f73d))





### Dependencies

* **@waitroom/utils:** upgraded to 1.28.2
* **@waitroom/http-client:** upgraded to 1.8.2
* **@waitroom/models:** upgraded to 1.43.0
* **@waitroom/tests:** upgraded to 1.11.1
* **@waitroom/analytics:** upgraded to 1.28.3
* **@waitroom/trixta-utils:** upgraded to 1.11.3

## @waitroom/common [1.87.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.87.2...@waitroom/common@1.87.3) (2022-07-04)


### Bug Fixes

* trixta auth ([808a6b8](https://github.com/Waitroom/waitroom/commit/808a6b829c4ce7898fd46064217d58de60946739))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.28.2

## @waitroom/common [1.87.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.87.1...@waitroom/common@1.87.2) (2022-07-04)





### Dependencies

* **@waitroom/trixta-utils:** upgraded to 1.11.2

## @waitroom/common [1.87.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.87.0...@waitroom/common@1.87.1) (2022-07-01)





### Dependencies

* **@waitroom/utils:** upgraded to 1.28.1
* **@waitroom/http-client:** upgraded to 1.8.1
* **@waitroom/analytics:** upgraded to 1.28.1
* **@waitroom/trixta-utils:** upgraded to 1.11.1

# @waitroom/common [1.87.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.86.3...@waitroom/common@1.87.0) (2022-06-30)


### Features

* request access live session ([#881](https://github.com/Waitroom/rumi.ai/issues/881)) ([13b0415](https://github.com/Waitroom/waitroom/commit/13b0415b2ae8ca1cddc993e17894b2fea679e4cb))





### Dependencies

* **@waitroom/utils:** upgraded to 1.28.0
* **@waitroom/config:** upgraded to 1.10.0
* **@waitroom/http-client:** upgraded to 1.8.0
* **@waitroom/models:** upgraded to 1.42.0
* **@waitroom/tests:** upgraded to 1.11.0
* **@waitroom/analytics:** upgraded to 1.28.0
* **@waitroom/trixta-utils:** upgraded to 1.11.0

## @waitroom/common [1.86.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.86.2...@waitroom/common@1.86.3) (2022-06-29)


### Bug Fixes

* mobile clashing events with common ([0d12154](https://github.com/Waitroom/waitroom/commit/0d12154be71d6a5086847d2a57bb517ce5876914))


### Reverts

* reverted agora package ([2f91a5d](https://github.com/Waitroom/waitroom/commit/2f91a5db782c5846053f99c2bc6c727d8c5be22b))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.27.2

## @waitroom/common [1.86.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.86.1...@waitroom/common@1.86.2) (2022-06-28)





### Dependencies

* **@waitroom/config:** upgraded to 1.9.1

## @waitroom/common [1.86.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.86.0...@waitroom/common@1.86.1) (2022-06-22)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.27.1

# @waitroom/common [1.86.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.85.3...@waitroom/common@1.86.0) (2022-06-22)


### Features

* refactored rest hook ([#873](https://github.com/Waitroom/rumi.ai/issues/873)) ([38f0ebf](https://github.com/Waitroom/waitroom/commit/38f0ebf50793bd6266b0fe1456391251f0176750))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.27.0
* **@waitroom/trixta-utils:** upgraded to 1.10.0

## @waitroom/common [1.85.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.85.2...@waitroom/common@1.85.3) (2022-06-21)





### Dependencies

* **@waitroom/trixta-utils:** upgraded to 1.9.1

## @waitroom/common [1.85.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.85.1...@waitroom/common@1.85.2) (2022-06-21)





### Dependencies

* **@waitroom/tests:** upgraded to 1.10.0
* **@waitroom/analytics:** upgraded to 1.26.10

## @waitroom/common [1.85.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.85.0...@waitroom/common@1.85.1) (2022-06-21)


### Bug Fixes

* set_session_id is no longer the event to setup the user for the session. ([c16ec91](https://github.com/Waitroom/waitroom/commit/c16ec91d7c5288276a6b2c3f885a4fed1da59e8d))

# @waitroom/common [1.85.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.84.5...@waitroom/common@1.85.0) (2022-06-20)


### Features

* use sessionId from channel join instead of selector ([bd9403f](https://github.com/Waitroom/waitroom/commit/bd9403f3f09f78b736f5b650446024123cd114d5))

## @waitroom/common [1.84.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.84.4...@waitroom/common@1.84.5) (2022-06-20)





### Dependencies

* **@waitroom/utils:** upgraded to 1.27.0
* **@waitroom/config:** upgraded to 1.9.0
* **@waitroom/http-client:** upgraded to 1.7.0
* **@waitroom/models:** upgraded to 1.41.0
* **@waitroom/tests:** upgraded to 1.9.0
* **@waitroom/analytics:** upgraded to 1.26.9
* **@waitroom/trixta-utils:** upgraded to 1.9.0

## @waitroom/common [1.84.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.84.3...@waitroom/common@1.84.4) (2022-06-20)


### Bug Fixes

* bug in fullName ([d310d51](https://github.com/Waitroom/waitroom/commit/d310d51f1abfde7df1791dada51f21e4ea7f3a5f))

## @waitroom/common [1.84.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.84.2...@waitroom/common@1.84.3) (2022-06-20)





### Dependencies

* **@waitroom/utils:** upgraded to 1.26.3
* **@waitroom/http-client:** upgraded to 1.6.9
* **@waitroom/analytics:** upgraded to 1.26.8
* **@waitroom/trixta-utils:** upgraded to 1.8.3

## @waitroom/common [1.84.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.84.1...@waitroom/common@1.84.2) (2022-06-17)


### Bug Fixes

* added --passWithNoTests as pre commit hook starts breaking ([#858](https://github.com/Waitroom/rumi.ai/issues/858)) ([9ab4329](https://github.com/Waitroom/waitroom/commit/9ab43299a3a105d83235f767be139914ad683321))





### Dependencies

* **@waitroom/utils:** upgraded to 1.26.2
* **@waitroom/config:** upgraded to 1.8.5
* **@waitroom/http-client:** upgraded to 1.6.8
* **@waitroom/models:** upgraded to 1.40.2
* **@waitroom/tests:** upgraded to 1.8.2
* **@waitroom/analytics:** upgraded to 1.26.7
* **@waitroom/trixta-utils:** upgraded to 1.8.2

## @waitroom/common [1.84.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.84.0...@waitroom/common@1.84.1) (2022-06-17)





### Dependencies

* **@waitroom/utils:** upgraded to 1.26.1
* **@waitroom/config:** upgraded to 1.8.4
* **@waitroom/http-client:** upgraded to 1.6.7
* **@waitroom/models:** upgraded to 1.40.1
* **@waitroom/tests:** upgraded to 1.8.1
* **@waitroom/analytics:** upgraded to 1.26.6
* **@waitroom/trixta-utils:** upgraded to 1.8.1

# @waitroom/common [1.84.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.83.0...@waitroom/common@1.84.0) (2022-06-15)


### Features

* con 533 implement request access flow ([#842](https://github.com/Waitroom/rumi.ai/issues/842)) ([43dd93c](https://github.com/Waitroom/waitroom/commit/43dd93c6db43284045edb5000de2654c1f9272e0))





### Dependencies

* **@waitroom/utils:** upgraded to 1.26.0
* **@waitroom/http-client:** upgraded to 1.6.6
* **@waitroom/models:** upgraded to 1.40.0
* **@waitroom/tests:** upgraded to 1.8.0
* **@waitroom/analytics:** upgraded to 1.26.5
* **@waitroom/trixta-utils:** upgraded to 1.8.0

# @waitroom/common [1.83.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.82.4...@waitroom/common@1.83.0) (2022-06-14)


### Features

* getSessionIdFromChannelTopic function ([549e5d0](https://github.com/Waitroom/waitroom/commit/549e5d08fcf9dfd8612a7a7612d124f345d0de67))





### Dependencies

* **@waitroom/trixta-utils:** upgraded to 1.7.0

## @waitroom/common [1.82.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.82.3...@waitroom/common@1.82.4) (2022-06-14)


### Bug Fixes

* role removal fix ([#843](https://github.com/Waitroom/rumi.ai/issues/843)) ([d8a661d](https://github.com/Waitroom/waitroom/commit/d8a661d59aca1aa3c4453e889201e8c5b9b3ec4d))

## @waitroom/common [1.82.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.82.2...@waitroom/common@1.82.3) (2022-06-08)





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.22
* **@waitroom/http-client:** upgraded to 1.6.5
* **@waitroom/models:** upgraded to 1.39.0
* **@waitroom/tests:** upgraded to 1.7.0
* **@waitroom/analytics:** upgraded to 1.26.4

## @waitroom/common [1.82.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.82.1...@waitroom/common@1.82.2) (2022-06-07)


### Bug Fixes

* models package version ([291e856](https://github.com/Waitroom/waitroom/commit/291e8561dbba78bf49a2d445134c6381b33ea77d))
* package versions ([05c81d9](https://github.com/Waitroom/waitroom/commit/05c81d9d96aafd8d2a7b25dbeccb28cfec9bf039))





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.21
* **@waitroom/http-client:** upgraded to 1.6.4
* **@waitroom/models:** upgraded to 1.38.1
* **@waitroom/tests:** upgraded to 1.6.3
* **@waitroom/analytics:** upgraded to 1.26.3

## @waitroom/common [1.82.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.82.0...@waitroom/common@1.82.1) (2022-06-07)





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.21
* **@waitroom/http-client:** upgraded to 1.6.3
* **@waitroom/models:** upgraded to 1.38.1
* **@waitroom/tests:** upgraded to 1.6.3
* **@waitroom/analytics:** upgraded to 1.26.2

# @waitroom/common [1.82.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.81.2...@waitroom/common@1.82.0) (2022-06-06)


### Features

* release trigger ([d28ed96](https://github.com/Waitroom/waitroom/commit/d28ed96d8eb4dccc32bf4a164c7e9b0e3a621a5a))





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.20
* **@waitroom/http-client:** upgraded to 1.6.2
* **@waitroom/models:** upgraded to 1.38.0
* **@waitroom/tests:** upgraded to 1.6.2
* **@waitroom/analytics:** upgraded to 1.26.1

## @waitroom/common [1.81.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.81.1...@waitroom/common@1.81.2) (2022-06-03)


### Bug Fixes

* improved performance of viewers list ([#828](https://github.com/Waitroom/rumi.ai/issues/828)) ([2b21c63](https://github.com/Waitroom/waitroom/commit/2b21c63f4c1004c978a5b11ac520be317b6f6719))

## @waitroom/common [1.81.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.81.0...@waitroom/common@1.81.1) (2022-06-01)


### Bug Fixes

* make sure to add the exporting of the action type ([b6983b0](https://github.com/Waitroom/waitroom/commit/b6983b04ce845d41b3cec45f223f4add7b422d4c))

# @waitroom/common [1.81.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.80.0...@waitroom/common@1.81.0) (2022-06-01)


### Features

* STATUS_VIEWER role is being replaced by SESSION_INFO_VIEWER ([7f9e02c](https://github.com/Waitroom/waitroom/commit/7f9e02cd4c9f30056492502f548b2c189647a962))





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.19
* **@waitroom/http-client:** upgraded to 1.6.1
* **@waitroom/models:** upgraded to 1.37.0
* **@waitroom/tests:** upgraded to 1.6.1
* **@waitroom/analytics:** upgraded to 1.26.0

# @waitroom/common [1.80.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.79.2...@waitroom/common@1.80.0) (2022-06-01)


### Features

* add response on an action event when error comes through ([dd773dc](https://github.com/Waitroom/waitroom/commit/dd773dc130305c4d47debf8cf345435eb669decd))

## @waitroom/common [1.79.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.79.1...@waitroom/common@1.79.2) (2022-05-26)


### Bug Fixes

* viewers list not showing ([5d5e789](https://github.com/Waitroom/waitroom/commit/5d5e7894589cccfb954b8c3d7d4a6e2179229b0f))

## @waitroom/common [1.79.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.79.0...@waitroom/common@1.79.1) (2022-05-25)


### Bug Fixes

* form callbacks ([#817](https://github.com/Waitroom/rumi.ai/issues/817)) ([53673d5](https://github.com/Waitroom/waitroom/commit/53673d5ffcb4ce6c9f4f71d2fb42d4d1e2de4749))

# @waitroom/common [1.79.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.78.0...@waitroom/common@1.79.0) (2022-05-24)


### Features

* implement CDN for images ([9761628](https://github.com/Waitroom/waitroom/commit/976162846c46b4319173940e6cf89c246c272fb7))

# @waitroom/common [1.78.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.77.1...@waitroom/common@1.78.0) (2022-05-24)


### Features

* cre 404 create invite form ([#811](https://github.com/Waitroom/rumi.ai/issues/811)) ([efd673d](https://github.com/Waitroom/waitroom/commit/efd673d3c4d6b0a96e78751909f9064f4141c1ac))





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.18
* **@waitroom/http-client:** upgraded to 1.6.0
* **@waitroom/models:** upgraded to 1.36.0
* **@waitroom/tests:** upgraded to 1.6.0
* **@waitroom/analytics:** upgraded to 1.25.0

## @waitroom/common [1.77.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.77.0...@waitroom/common@1.77.1) (2022-05-23)


### Bug Fixes

* re add missing role join ([6a77c79](https://github.com/Waitroom/waitroom/commit/6a77c79670aeacd82d5e7b5cb749ef330ae53110))

# @waitroom/common [1.77.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.76.1...@waitroom/common@1.77.0) (2022-05-23)


### Features

* Feature/cre 346 update the session form UI new ([#812](https://github.com/Waitroom/rumi.ai/issues/812)) ([2e38151](https://github.com/Waitroom/waitroom/commit/2e38151fff6b62ccd96864f3beeb398ff344721e)), closes [#806](https://github.com/Waitroom/rumi.ai/issues/806) [#810](https://github.com/Waitroom/rumi.ai/issues/810)





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.17
* **@waitroom/http-client:** upgraded to 1.5.18
* **@waitroom/models:** upgraded to 1.35.0
* **@waitroom/tests:** upgraded to 1.5.5
* **@waitroom/analytics:** upgraded to 1.24.0

## @waitroom/common [1.76.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.76.0...@waitroom/common@1.76.1) (2022-05-20)


### Bug Fixes

* auth ([bdba028](https://github.com/Waitroom/waitroom/commit/bdba028bc92eeb62d2334728d6de20b350b7bb67))

# @waitroom/common [1.76.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.75.0...@waitroom/common@1.76.0) (2022-05-20)


### Features

* wr 1162 implement refresh token ([#791](https://github.com/Waitroom/rumi.ai/issues/791)) ([c75366e](https://github.com/Waitroom/waitroom/commit/c75366e4412d308270096b2c25ba7572bc03c3a7))





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.16
* **@waitroom/http-client:** upgraded to 1.5.17
* **@waitroom/models:** upgraded to 1.34.0
* **@waitroom/tests:** upgraded to 1.5.4
* **@waitroom/analytics:** upgraded to 1.23.0

# @waitroom/common [1.75.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.74.0...@waitroom/common@1.75.0) (2022-05-19)


### Features

* aurora needs filename ([bbe7cdb](https://github.com/Waitroom/waitroom/commit/bbe7cdb125185abc36af24b15e70df1d8112fe47))





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.15
* **@waitroom/http-client:** upgraded to 1.5.16
* **@waitroom/models:** upgraded to 1.33.0
* **@waitroom/tests:** upgraded to 1.5.3
* **@waitroom/analytics:** upgraded to 1.22.7

# @waitroom/common [1.74.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.73.3...@waitroom/common@1.74.0) (2022-05-18)


### Features

* aurora image service implemented ([0d0546b](https://github.com/Waitroom/waitroom/commit/0d0546b25d6ed7d9cd9a6cc87a89b14685db89c7))





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.14
* **@waitroom/http-client:** upgraded to 1.5.15
* **@waitroom/models:** upgraded to 1.32.0
* **@waitroom/tests:** upgraded to 1.5.2
* **@waitroom/analytics:** upgraded to 1.22.6

## @waitroom/common [1.73.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.73.2...@waitroom/common@1.73.3) (2022-05-18)





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.13
* **@waitroom/http-client:** upgraded to 1.5.14
* **@waitroom/models:** upgraded to 1.31.0
* **@waitroom/tests:** upgraded to 1.5.1
* **@waitroom/analytics:** upgraded to 1.22.5

## @waitroom/common [1.73.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.73.1...@waitroom/common@1.73.2) (2022-05-17)





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.12
* **@waitroom/http-client:** upgraded to 1.5.13
* **@waitroom/models:** upgraded to 1.30.0
* **@waitroom/tests:** upgraded to 1.5.0
* **@waitroom/analytics:** upgraded to 1.22.4

## @waitroom/common [1.73.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.73.0...@waitroom/common@1.73.1) (2022-05-12)





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.11
* **@waitroom/http-client:** upgraded to 1.5.12
* **@waitroom/models:** upgraded to 1.29.1
* **@waitroom/tests:** upgraded to 1.4.4
* **@waitroom/analytics:** upgraded to 1.22.3

# @waitroom/common [1.73.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.72.1...@waitroom/common@1.73.0) (2022-05-10)


### Features

* unique list of viewers ([8098a92](https://github.com/Waitroom/waitroom/commit/8098a92a98cf30bd48ec5281285247dc6b09bb77))

## @waitroom/common [1.72.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.72.0...@waitroom/common@1.72.1) (2022-05-09)


### Bug Fixes

* common package ([a0bc6df](https://github.com/Waitroom/waitroom/commit/a0bc6df7cbc04c281714dc8f104bf5ee05b87920))
* fixed tests ([7ce91a4](https://github.com/Waitroom/waitroom/commit/7ce91a4952afbb14d7a8b289b48ab086b1888a62))
* package versions ([0006b53](https://github.com/Waitroom/waitroom/commit/0006b539db835d27b35c786e1d5ca688199ef600))
* remove date time config ([0655428](https://github.com/Waitroom/waitroom/commit/06554286f861b8c6444b0868a1face4829911d94))





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.10
* **@waitroom/config:** upgraded to 1.8.3
* **@waitroom/http-client:** upgraded to 1.5.11
* **@waitroom/models:** upgraded to 1.29.0
* **@waitroom/tests:** upgraded to 1.4.3
* **@waitroom/analytics:** upgraded to 1.22.2

# @waitroom/common [1.72.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.71.0...@waitroom/common@1.72.0) (2022-05-06)


### Features

* subscription hook ([e53285b](https://github.com/Waitroom/waitroom/commit/e53285b8671fe63c0051460acf8f980c510f01a0))





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.10
* **@waitroom/http-client:** upgraded to 1.5.10
* **@waitroom/models:** upgraded to 1.29.0
* **@waitroom/tests:** upgraded to 1.4.3
* **@waitroom/analytics:** upgraded to 1.22.1

# @waitroom/common [1.71.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.70.0...@waitroom/common@1.71.0) (2022-05-05)


### Features

* implement trixta types ([09eb5fd](https://github.com/Waitroom/waitroom/commit/09eb5fd84a09d2d1556891dfbc5f286dec1304a1))





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.9
* **@waitroom/http-client:** upgraded to 1.5.9
* **@waitroom/models:** upgraded to 1.28.0
* **@waitroom/tests:** upgraded to 1.4.2
* **@waitroom/analytics:** upgraded to 1.22.0

# @waitroom/common [1.70.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.69.0...@waitroom/common@1.70.0) (2022-05-04)


### Features

* trixta reaction types ([1e7e201](https://github.com/Waitroom/waitroom/commit/1e7e20149e535afd8877d234b44a8bb955c5c1ec))





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.8
* **@waitroom/http-client:** upgraded to 1.5.8
* **@waitroom/models:** upgraded to 1.27.0
* **@waitroom/tests:** upgraded to 1.4.1
* **@waitroom/analytics:** upgraded to 1.21.8

# @waitroom/common [1.69.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.68.1...@waitroom/common@1.69.0) (2022-05-03)


### Features

* add presence listeners and dispatches to set socket meta data ([a070ffa](https://github.com/Waitroom/waitroom/commit/a070ffaeed0f88438caf704d4273d5c77098d011))
* added unit tests for newly added selector ([#778](https://github.com/Waitroom/rumi.ai/issues/778)) ([2ebe8ab](https://github.com/Waitroom/waitroom/commit/2ebe8ab2533b33a999f975a092d324a33e4596e0))
* now current user moves to top of the viewers list ([e28f00b](https://github.com/Waitroom/waitroom/commit/e28f00b5a0e12afe43e9adf253ade8b5809db2d0))





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.7
* **@waitroom/http-client:** upgraded to 1.5.7
* **@waitroom/models:** upgraded to 1.26.0
* **@waitroom/tests:** upgraded to 1.4.0
* **@waitroom/analytics:** upgraded to 1.21.7

## @waitroom/common [1.68.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.68.0...@waitroom/common@1.68.1) (2022-05-03)





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.6
* **@waitroom/http-client:** upgraded to 1.5.6
* **@waitroom/models:** upgraded to 1.25.1
* **@waitroom/tests:** upgraded to 1.3.6
* **@waitroom/analytics:** upgraded to 1.21.6

# @waitroom/common [1.68.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.67.1...@waitroom/common@1.68.0) (2022-05-03)


### Features

* trixta action types ([a58dff6](https://github.com/Waitroom/waitroom/commit/a58dff60ee98cdf0b8e120e615be345ee53d0b88))





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.5
* **@waitroom/http-client:** upgraded to 1.5.5
* **@waitroom/models:** upgraded to 1.25.0
* **@waitroom/tests:** upgraded to 1.3.5
* **@waitroom/analytics:** upgraded to 1.21.5

## @waitroom/common [1.67.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.67.0...@waitroom/common@1.67.1) (2022-04-28)


### Bug Fixes

* CRE-240 Allow Primary Host image to be displayed on session page ([#770](https://github.com/Waitroom/rumi.ai/issues/770)) ([4fe126d](https://github.com/Waitroom/waitroom/commit/4fe126d23e4857336f5190d4d171feb412043293))

# @waitroom/common [1.67.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.66.3...@waitroom/common@1.67.0) (2022-04-20)


### Features

* add active stream roles to hosts channel users ([#757](https://github.com/Waitroom/rumi.ai/issues/757)) ([f46ef26](https://github.com/Waitroom/waitroom/commit/f46ef26bf0bc1e092fe200e1417ff0bdc4379e95))





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.4
* **@waitroom/http-client:** upgraded to 1.5.4
* **@waitroom/models:** upgraded to 1.24.0
* **@waitroom/tests:** upgraded to 1.3.4
* **@waitroom/analytics:** upgraded to 1.21.4

## @waitroom/common [1.66.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.66.2...@waitroom/common@1.66.3) (2022-04-20)





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.3
* **@waitroom/config:** upgraded to 1.8.2
* **@waitroom/http-client:** upgraded to 1.5.3
* **@waitroom/models:** upgraded to 1.23.2
* **@waitroom/tests:** upgraded to 1.3.3
* **@waitroom/analytics:** upgraded to 1.21.3

## @waitroom/common [1.66.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.66.1...@waitroom/common@1.66.2) (2022-04-15)





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.2
* **@waitroom/http-client:** upgraded to 1.5.2
* **@waitroom/tests:** upgraded to 1.3.2
* **@waitroom/analytics:** upgraded to 1.21.2

## @waitroom/common [1.66.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.66.0...@waitroom/common@1.66.1) (2022-04-14)





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.1
* **@waitroom/config:** upgraded to 1.8.1
* **@waitroom/http-client:** upgraded to 1.5.1
* **@waitroom/models:** upgraded to 1.23.1
* **@waitroom/tests:** upgraded to 1.3.1
* **@waitroom/analytics:** upgraded to 1.21.1

# @waitroom/common [1.66.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.65.1...@waitroom/common@1.66.0) (2022-04-13)


### Features

* wr 748 develop and improve trixta tools for data fetching ([#755](https://github.com/Waitroom/rumi.ai/issues/755)) ([8d0a6d9](https://github.com/Waitroom/waitroom/commit/8d0a6d9da4205149abf518a688913c4e331af4e0))





### Dependencies

* **@waitroom/utils:** upgraded to 1.25.0
* **@waitroom/config:** upgraded to 1.8.0
* **@waitroom/http-client:** upgraded to 1.5.0
* **@waitroom/models:** upgraded to 1.23.0
* **@waitroom/tests:** upgraded to 1.3.0
* **@waitroom/analytics:** upgraded to 1.21.0

## @waitroom/common [1.65.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.65.0...@waitroom/common@1.65.1) (2022-04-11)


### Bug Fixes

* check if the role exists - changing users roel on the fly ([#751](https://github.com/Waitroom/rumi.ai/issues/751)) ([ce35537](https://github.com/Waitroom/waitroom/commit/ce35537146f3efffa3105a88a2466e55fedad689))

# @waitroom/common [1.65.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.64.1...@waitroom/common@1.65.0) (2022-04-11)


### Features

* presence for producers and hosts between eachother ([#736](https://github.com/Waitroom/rumi.ai/issues/736)) ([1b925d2](https://github.com/Waitroom/waitroom/commit/1b925d267ff6e3ad93589d1a2a6e9b1bd7afe7a9))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.20.2
* **@waitroom/http-client:** upgraded to 1.4.16
* **@waitroom/models:** upgraded to 1.22.0
* **@waitroom/utils:** upgraded to 1.24.5
* **@waitroom/tests:** upgraded to 1.2.6

## @waitroom/common [1.64.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.64.0...@waitroom/common@1.64.1) (2022-04-11)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.20.1

# @waitroom/common [1.64.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.63.1...@waitroom/common@1.64.0) (2022-04-08)


### Bug Fixes

* fix common dependency change ([#741](https://github.com/Waitroom/rumi.ai/issues/741)) ([b7632ec](https://github.com/Waitroom/waitroom/commit/b7632ecd30aaf2eb2a15bdbf8efaafa623b7e9d1))
* TrackingEventPaylod import ([#739](https://github.com/Waitroom/rumi.ai/issues/739)) ([fcab7e1](https://github.com/Waitroom/waitroom/commit/fcab7e16db9eff7faa906e92dea6d844a3adf55f))


### Features

* implement trixta recording tracking events ([#738](https://github.com/Waitroom/rumi.ai/issues/738)) ([2aca224](https://github.com/Waitroom/waitroom/commit/2aca22485b25949f9747506487be65f2170ec305))
* update packages ([#740](https://github.com/Waitroom/rumi.ai/issues/740)) ([b3fc7ca](https://github.com/Waitroom/waitroom/commit/b3fc7ca467efe4b83aa3d936a4f8e57646a238b1))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.20.0
* **@waitroom/http-client:** upgraded to 1.4.15
* **@waitroom/models:** upgraded to 1.21.0
* **@waitroom/utils:** upgraded to 1.24.4
* **@waitroom/tests:** upgraded to 1.2.5

## @waitroom/common [1.63.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.63.0...@waitroom/common@1.63.1) (2022-04-07)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.19.0

# @waitroom/common [1.63.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.62.2...@waitroom/common@1.63.0) (2022-04-04)


### Features

* update featured_sessions to reducer ([#731](https://github.com/Waitroom/rumi.ai/issues/731)) ([43603d4](https://github.com/Waitroom/waitroom/commit/43603d4f9d6300f42234d5055236514cc4112777))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.20
* **@waitroom/http-client:** upgraded to 1.4.14
* **@waitroom/models:** upgraded to 1.20.0
* **@waitroom/utils:** upgraded to 1.24.3
* **@waitroom/tests:** upgraded to 1.2.4

## @waitroom/common [1.62.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.62.1...@waitroom/common@1.62.2) (2022-03-29)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.19

## @waitroom/common [1.62.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.62.0...@waitroom/common@1.62.1) (2022-03-29)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.18

# @waitroom/common [1.62.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.61.3...@waitroom/common@1.62.0) (2022-03-28)


### Bug Fixes

* when logging out should resubmit to trixta for sessions ([#720](https://github.com/Waitroom/rumi.ai/issues/720)) ([8cbd720](https://github.com/Waitroom/waitroom/commit/8cbd720a3a744d49de0ce521ec23e51ec8f14ff7))


### Features

* remove mailjet scubscription as mars will replace ([#711](https://github.com/Waitroom/rumi.ai/issues/711)) ([3763acb](https://github.com/Waitroom/waitroom/commit/3763acbb8db8b7b213381e99f3fbb7169bc8d128))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.17

## @waitroom/common [1.61.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.61.2...@waitroom/common@1.61.3) (2022-03-25)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.16
* **@waitroom/http-client:** upgraded to 1.4.13
* **@waitroom/models:** upgraded to 1.19.3
* **@waitroom/utils:** upgraded to 1.24.2
* **@waitroom/tests:** upgraded to 1.2.3

## @waitroom/common [1.61.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.61.1...@waitroom/common@1.61.2) (2022-03-25)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.15
* **@waitroom/http-client:** upgraded to 1.4.12
* **@waitroom/models:** upgraded to 1.19.2
* **@waitroom/utils:** upgraded to 1.24.1
* **@waitroom/tests:** upgraded to 1.2.2

## @waitroom/common [1.61.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.61.0...@waitroom/common@1.61.1) (2022-03-25)


### Bug Fixes

* testlio ([#719](https://github.com/Waitroom/rumi.ai/issues/719)) ([20d16f8](https://github.com/Waitroom/waitroom/commit/20d16f8873fe4c13e4ea45bfca25d8a7829d809c))

# @waitroom/common [1.61.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.60.2...@waitroom/common@1.61.0) (2022-03-25)


### Features

* subscribed to session on session page indications for curr user ([#712](https://github.com/Waitroom/rumi.ai/issues/712)) ([045caec](https://github.com/Waitroom/waitroom/commit/045caec8b1825e764dac71dc62a366e8a549a037))

## @waitroom/common [1.60.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.60.1...@waitroom/common@1.60.2) (2022-03-24)


### Bug Fixes

* register modal overflow TES-42 ([32be051](https://github.com/Waitroom/waitroom/commit/32be051d15fda09d5580c08255a39b36dcea5a81))

## @waitroom/common [1.60.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.60.0...@waitroom/common@1.60.1) (2022-03-22)


### Bug Fixes

* down grade utils ([#707](https://github.com/Waitroom/rumi.ai/issues/707)) ([51a9104](https://github.com/Waitroom/waitroom/commit/51a9104df6df4b6abed465a78acab23c7f93a0f6))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.14
* **@waitroom/http-client:** upgraded to 1.4.11
* **@waitroom/models:** upgraded to 1.19.1
* **@waitroom/utils:** upgraded to 1.24.0
* **@waitroom/tests:** upgraded to 1.2.1

# @waitroom/common [1.60.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.59.0...@waitroom/common@1.60.0) (2022-03-22)


### Features

* move player utils to utils package ([#702](https://github.com/Waitroom/rumi.ai/issues/702)) ([b666428](https://github.com/Waitroom/waitroom/commit/b666428c28281ecf384904f9ccb76d6f78aeeb80))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.13
* **@waitroom/http-client:** upgraded to 1.4.11
* **@waitroom/utils:** upgraded to 1.24.0

# @waitroom/common [1.59.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.58.2...@waitroom/common@1.59.0) (2022-03-22)


### Features

* session reminders ([#692](https://github.com/Waitroom/rumi.ai/issues/692)) ([ce5a6a2](https://github.com/Waitroom/waitroom/commit/ce5a6a29e465f4f77da6a7faa6ac6652e97fa258))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.12
* **@waitroom/http-client:** upgraded to 1.4.10
* **@waitroom/models:** upgraded to 1.19.0
* **@waitroom/utils:** upgraded to 1.23.0
* **@waitroom/tests:** upgraded to 1.2.0

## @waitroom/common [1.58.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.58.1...@waitroom/common@1.58.2) (2022-03-22)


### Bug Fixes

* carousel scroll ([#696](https://github.com/Waitroom/rumi.ai/issues/696)) ([54da959](https://github.com/Waitroom/waitroom/commit/54da95924b58782336a9d877561fdea3ac8816b8))

## @waitroom/common [1.58.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.58.0...@waitroom/common@1.58.1) (2022-03-21)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.11
* **@waitroom/http-client:** upgraded to 1.4.9
* **@waitroom/models:** upgraded to 1.18.0
* **@waitroom/utils:** upgraded to 1.22.3
* **@waitroom/tests:** upgraded to 1.1.3

# @waitroom/common [1.58.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.57.1...@waitroom/common@1.58.0) (2022-03-21)


### Features

* sign up users to mailjet ([#695](https://github.com/Waitroom/rumi.ai/issues/695)) ([1f0d7fa](https://github.com/Waitroom/waitroom/commit/1f0d7fa715d72bde9103675a740d62e718b9f5b0))
* success feedback for subscription ([#694](https://github.com/Waitroom/rumi.ai/issues/694)) ([2c4c805](https://github.com/Waitroom/waitroom/commit/2c4c8056eb91c40dd8de71fd08af2eed1bdfbedd))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.10
* **@waitroom/http-client:** upgraded to 1.4.8
* **@waitroom/models:** upgraded to 1.17.0
* **@waitroom/config:** upgraded to 1.7.1
* **@waitroom/utils:** upgraded to 1.22.2
* **@waitroom/tests:** upgraded to 1.1.2

## @waitroom/common [1.57.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.57.0...@waitroom/common@1.57.1) (2022-03-18)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.9
* **@waitroom/http-client:** upgraded to 1.4.7
* **@waitroom/models:** upgraded to 1.16.0
* **@waitroom/utils:** upgraded to 1.22.1
* **@waitroom/tests:** upgraded to 1.1.1

# @waitroom/common [1.57.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.56.0...@waitroom/common@1.57.0) (2022-03-18)


### Features

* home page redesign ([#667](https://github.com/Waitroom/rumi.ai/issues/667)) ([9c13ceb](https://github.com/Waitroom/waitroom/commit/9c13ceb295e9e070cd3171296301107441d882c3))
* sender role for the message, remove console, add reaction constant ([#691](https://github.com/Waitroom/rumi.ai/issues/691)) ([6a79dac](https://github.com/Waitroom/waitroom/commit/6a79dac9f23aa9f1cb4dda8b02604dd8c7864f81))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.8
* **@waitroom/http-client:** upgraded to 1.4.6
* **@waitroom/models:** upgraded to 1.15.0
* **@waitroom/config:** upgraded to 1.7.0
* **@waitroom/utils:** upgraded to 1.22.0
* **@waitroom/tests:** upgraded to 1.1.0

# @waitroom/common [1.56.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.55.1...@waitroom/common@1.56.0) (2022-03-17)


### Features

* select if my list has active session ([#690](https://github.com/Waitroom/rumi.ai/issues/690)) ([71eee9c](https://github.com/Waitroom/waitroom/commit/71eee9cd883ad86e77b469c4251cd9faca9b5687))

## @waitroom/common [1.55.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.55.0...@waitroom/common@1.55.1) (2022-03-16)


### Bug Fixes

* fix the error handling issue for dashboard and errors not returning on forms for useAction ([#683](https://github.com/Waitroom/rumi.ai/issues/683)) ([63df51d](https://github.com/Waitroom/waitroom/commit/63df51d8ea2e2eff252d13ad773645fe2e6c65b9))

# @waitroom/common [1.55.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.54.3...@waitroom/common@1.55.0) (2022-03-15)


### Features

* new hook for roles, use on landing pages and comments for reaction ([#676](https://github.com/Waitroom/rumi.ai/issues/676)) ([5c08219](https://github.com/Waitroom/waitroom/commit/5c08219905bd08b3ef9ac4d0d012cef64cc98364))

## @waitroom/common [1.54.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.54.2...@waitroom/common@1.54.3) (2022-03-14)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.7

## @waitroom/common [1.54.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.54.1...@waitroom/common@1.54.2) (2022-03-14)


### Bug Fixes

* fixed tests ([48f6a5d](https://github.com/Waitroom/waitroom/commit/48f6a5da715704bf69b5232cac71aed70a7c78d2))
* package versions ([2a84db3](https://github.com/Waitroom/waitroom/commit/2a84db30a81c3590575a965c7dd4f2dd584aed2e))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.6
* **@waitroom/http-client:** upgraded to 1.4.5
* **@waitroom/models:** upgraded to 1.14.1
* **@waitroom/utils:** upgraded to 1.21.1
* **@waitroom/tests:** upgraded to 1.0.0

## @waitroom/common [1.54.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.54.0...@waitroom/common@1.54.1) (2022-03-14)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.6
* **@waitroom/http-client:** upgraded to 1.4.5
* **@waitroom/utils:** upgraded to 1.21.1

# @waitroom/common [1.54.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.53.0...@waitroom/common@1.54.0) (2022-03-11)


### Features

* cre 126 if live streaming setup fails it needs ([#656](https://github.com/Waitroom/rumi.ai/issues/656)) ([425e096](https://github.com/Waitroom/waitroom/commit/425e09606f21d65ff5ec7f3d38e992e40d2725da))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.5
* **@waitroom/http-client:** upgraded to 1.4.4
* **@waitroom/utils:** upgraded to 1.21.0

# @waitroom/common [1.53.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.52.1...@waitroom/common@1.53.0) (2022-03-10)


### Features

* waitroom list subscription hook ([#671](https://github.com/Waitroom/rumi.ai/issues/671)) ([bad9abc](https://github.com/Waitroom/waitroom/commit/bad9abc660fe51ea9f49dc1b212eb180b8535927))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.4
* **@waitroom/http-client:** upgraded to 1.4.3
* **@waitroom/models:** upgraded to 1.14.0
* **@waitroom/utils:** upgraded to 1.20.3

## @waitroom/common [1.52.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.52.0...@waitroom/common@1.52.1) (2022-03-09)


### Bug Fixes

* trixta response callback types are correct ([#666](https://github.com/Waitroom/rumi.ai/issues/666)) ([fb5b20f](https://github.com/Waitroom/waitroom/commit/fb5b20f6ff1643d5664bc4c69ac37d17745b6392))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.3

# @waitroom/common [1.52.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.51.0...@waitroom/common@1.52.0) (2022-03-08)


### Bug Fixes

* remove branch fingerprint on socket connect and update when joined on channel ([#664](https://github.com/Waitroom/rumi.ai/issues/664)) ([bc31c02](https://github.com/Waitroom/waitroom/commit/bc31c02c443ac0c7f819aa0856b3ef73fddf787a))


### Features

* added behaviour events to send to GTM ([#662](https://github.com/Waitroom/rumi.ai/issues/662)) ([f72f74a](https://github.com/Waitroom/waitroom/commit/f72f74af159ef0a278ac54b0a0c5576a770026a9))
* added host messages for pause/resume queue ([#663](https://github.com/Waitroom/rumi.ai/issues/663)) ([5c45195](https://github.com/Waitroom/waitroom/commit/5c4519593321882e5232dcbbd91994df4eeed760))

# @waitroom/common [1.51.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.50.5...@waitroom/common@1.51.0) (2022-03-07)


### Features

* updated toggle collapse in sidebar ([fb7f599](https://github.com/Waitroom/waitroom/commit/fb7f5996e91f5c3ee286b288719763bad1c84dbf))


### Reverts

* reverted saga changes ([0dac54e](https://github.com/Waitroom/waitroom/commit/0dac54e646924709f419064058be0d49ed366997))

## @waitroom/common [1.50.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.50.4...@waitroom/common@1.50.5) (2022-03-07)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.2
* **@waitroom/http-client:** upgraded to 1.4.2
* **@waitroom/models:** upgraded to 1.13.2
* **@waitroom/config:** upgraded to 1.6.1
* **@waitroom/utils:** upgraded to 1.20.2

## @waitroom/common [1.50.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.50.3...@waitroom/common@1.50.4) (2022-03-06)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.1
* **@waitroom/http-client:** upgraded to 1.4.1
* **@waitroom/models:** upgraded to 1.13.1
* **@waitroom/utils:** upgraded to 1.20.1

## @waitroom/common [1.50.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.50.2...@waitroom/common@1.50.3) (2022-03-03)


### Bug Fixes

* reaction that warms up next guest order causes guest to flash ([#653](https://github.com/Waitroom/rumi.ai/issues/653)) ([bb987b3](https://github.com/Waitroom/waitroom/commit/bb987b3015fbde1535be8e3785d0d950d67a67d2))

## @waitroom/common [1.50.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.50.1...@waitroom/common@1.50.2) (2022-03-03)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.18.0

## @waitroom/common [1.50.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.50.0...@waitroom/common@1.50.1) (2022-03-03)


### Bug Fixes

* fixed producer ui bugs ([6ceb069](https://github.com/Waitroom/waitroom/commit/6ceb069b774ac5a8e1f3b3c202d4e32351ec1a80))

# @waitroom/common [1.50.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.17...@waitroom/common@1.50.0) (2022-03-01)


### Features

* session producer ([3f5675d](https://github.com/Waitroom/waitroom/commit/3f5675d7490bdb794eb30afefb18d195f9cce625))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.17.0
* **@waitroom/http-client:** upgraded to 1.4.0
* **@waitroom/models:** upgraded to 1.13.0
* **@waitroom/config:** upgraded to 1.6.0
* **@waitroom/utils:** upgraded to 1.20.0

## @waitroom/common [1.49.17](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.16...@waitroom/common@1.49.17) (2022-02-28)


### Bug Fixes

* mobile session not updating when restarted ([#635](https://github.com/Waitroom/rumi.ai/issues/635)) ([b603626](https://github.com/Waitroom/waitroom/commit/b603626c74757a9badae81a3db31c7976075168b))

## @waitroom/common [1.49.16](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.15...@waitroom/common@1.49.16) (2022-02-22)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.16.4
* **@waitroom/http-client:** upgraded to 1.3.7
* **@waitroom/models:** upgraded to 1.12.4
* **@waitroom/utils:** upgraded to 1.19.5

## @waitroom/common [1.49.15](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.14...@waitroom/common@1.49.15) (2022-02-16)


### Bug Fixes

* sending recurrence id when session starts so we know how to deal with which episode updates ([#600](https://github.com/Waitroom/rumi.ai/issues/600)) ([109eab0](https://github.com/Waitroom/waitroom/commit/109eab04d9e593add0172c4d146f428e0f69996a))

## @waitroom/common [1.49.14](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.13...@waitroom/common@1.49.14) (2022-02-16)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.16.3

## @waitroom/common [1.49.13](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.12...@waitroom/common@1.49.13) (2022-02-11)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.16.2
* **@waitroom/http-client:** upgraded to 1.3.6
* **@waitroom/utils:** upgraded to 1.19.4

## @waitroom/common [1.49.12](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.11...@waitroom/common@1.49.12) (2022-02-11)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.16.1
* **@waitroom/http-client:** upgraded to 1.3.5
* **@waitroom/models:** upgraded to 1.12.3
* **@waitroom/utils:** upgraded to 1.19.3

## @waitroom/common [1.49.11](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.10...@waitroom/common@1.49.11) (2022-02-09)


### Bug Fixes

* moved branch to be set before joining channels ([#567](https://github.com/Waitroom/rumi.ai/issues/567)) ([275dfe1](https://github.com/Waitroom/waitroom/commit/275dfe1e86bbd853817b3ca922fe6f515453be15))

## @waitroom/common [1.49.10](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.9...@waitroom/common@1.49.10) (2022-02-07)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.16.0

## @waitroom/common [1.49.9](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.8...@waitroom/common@1.49.9) (2022-02-01)


### Bug Fixes

* include branch finger print in userClientDetails ([#555](https://github.com/Waitroom/rumi.ai/issues/555)) ([7af4cd3](https://github.com/Waitroom/waitroom/commit/7af4cd35b69713a3cbb18b401f3e6b3a715d9c7d))

## @waitroom/common [1.49.8](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.7...@waitroom/common@1.49.8) (2022-02-01)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.15.6

## @waitroom/common [1.49.7](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.6...@waitroom/common@1.49.7) (2022-02-01)


### Bug Fixes

* remove duplicated tracking events ([#552](https://github.com/Waitroom/rumi.ai/issues/552)) ([9c5df28](https://github.com/Waitroom/waitroom/commit/9c5df28e8818104bce009f923dcb4304c930edda))

## @waitroom/common [1.49.6](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.5...@waitroom/common@1.49.6) (2022-01-31)


### Bug Fixes

* queue count labels ([#551](https://github.com/Waitroom/rumi.ai/issues/551)) ([1ff29c7](https://github.com/Waitroom/waitroom/commit/1ff29c79e2fa5eadae17d60a35d9e65f6a635931))

## @waitroom/common [1.49.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.4...@waitroom/common@1.49.5) (2022-01-31)


### Bug Fixes

* loose option build fix ([fa09e7b](https://github.com/Waitroom/waitroom/commit/fa09e7bb49d063e7ab908784a539a834233dc336))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.15.5
* **@waitroom/http-client:** upgraded to 1.3.4
* **@waitroom/models:** upgraded to 1.12.2
* **@waitroom/config:** upgraded to 1.5.2
* **@waitroom/utils:** upgraded to 1.19.2

## @waitroom/common [1.49.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.3...@waitroom/common@1.49.4) (2022-01-26)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.15.4
* **@waitroom/http-client:** upgraded to 1.3.3
* **@waitroom/models:** upgraded to 1.12.1
* **@waitroom/utils:** upgraded to 1.19.1

## @waitroom/common [1.49.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.2...@waitroom/common@1.49.3) (2022-01-26)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.15.3

## @waitroom/common [1.49.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.1...@waitroom/common@1.49.2) (2022-01-25)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.15.2

## @waitroom/common [1.49.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.49.0...@waitroom/common@1.49.1) (2022-01-25)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.15.1

# @waitroom/common [1.49.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.48.1...@waitroom/common@1.49.0) (2022-01-25)


### Features

* update branch finger print endpoint form data ([#532](https://github.com/Waitroom/rumi.ai/issues/532)) ([dc18c62](https://github.com/Waitroom/waitroom/commit/dc18c6256bf518c4a69503f76e13740200de9592))

## @waitroom/common [1.48.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.48.0...@waitroom/common@1.48.1) (2022-01-21)





### Dependencies

* **@waitroom/config:** upgraded to 1.5.1

# @waitroom/common [1.48.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.47.1...@waitroom/common@1.48.0) (2022-01-21)


### Features

* update branch fingerprint ([#526](https://github.com/Waitroom/rumi.ai/issues/526)) ([c4a6566](https://github.com/Waitroom/waitroom/commit/c4a656695662dac45bfde66fddde48f2f6743132))

## @waitroom/common [1.47.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.47.0...@waitroom/common@1.47.1) (2022-01-20)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.15.0

# @waitroom/common [1.47.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.46.1...@waitroom/common@1.47.0) (2022-01-18)


### Features

* added redux store for analytics ([5bf19a7](https://github.com/Waitroom/waitroom/commit/5bf19a719dabec33f41f14a8091dd878af92ac01))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.14.0
* **@waitroom/http-client:** upgraded to 1.3.2
* **@waitroom/utils:** upgraded to 1.19.0

## @waitroom/common [1.46.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.46.0...@waitroom/common@1.46.1) (2022-01-14)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.13.1
* **@waitroom/http-client:** upgraded to 1.3.1
* **@waitroom/models:** upgraded to 1.12.0
* **@waitroom/utils:** upgraded to 1.18.1

# @waitroom/common [1.46.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.45.2...@waitroom/common@1.46.0) (2022-01-14)


### Features

* playback ([04438e0](https://github.com/Waitroom/waitroom/commit/04438e0f1e7d9de824f049c44c60dd410deba450))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.13.0
* **@waitroom/http-client:** upgraded to 1.3.0
* **@waitroom/models:** upgraded to 1.11.0
* **@waitroom/config:** upgraded to 1.5.0
* **@waitroom/utils:** upgraded to 1.18.0

## @waitroom/common [1.45.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.45.1...@waitroom/common@1.45.2) (2022-01-12)


### Bug Fixes

* fixed circular dep ([4c6d2d3](https://github.com/Waitroom/waitroom/commit/4c6d2d3e92a11f6aa314f49922ee5b9a82175655))

## @waitroom/common [1.45.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.45.0...@waitroom/common@1.45.1) (2022-01-07)





### Dependencies

* **@waitroom/config:** upgraded to 1.4.0

# @waitroom/common [1.45.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.44.0...@waitroom/common@1.45.0) (2022-01-03)


### Features

* trixtajs updated ([#507](https://github.com/Waitroom/rumi.ai/issues/507)) ([76a4fc3](https://github.com/Waitroom/waitroom/commit/76a4fc3fc3f58956c3fd01a83096a1f5058fa9fa))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.12.0

# @waitroom/common [1.44.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.43.0...@waitroom/common@1.44.0) (2021-12-22)


### Features

* release trixta ([#505](https://github.com/Waitroom/rumi.ai/issues/505)) ([5b3136c](https://github.com/Waitroom/waitroom/commit/5b3136c079a125258d174fce5ce740a775aee752))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.11.0

# @waitroom/common [1.43.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.42.2...@waitroom/common@1.43.0) (2021-12-22)


### Features

* added more link to about session content ([8c9db35](https://github.com/Waitroom/waitroom/commit/8c9db354deed650d056b9055720ec172b204034c))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.10.3
* **@waitroom/http-client:** upgraded to 1.2.3
* **@waitroom/models:** upgraded to 1.10.0
* **@waitroom/utils:** upgraded to 1.17.3

## @waitroom/common [1.42.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.42.1...@waitroom/common@1.42.2) (2021-12-16)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.10.2
* **@waitroom/http-client:** upgraded to 1.2.2
* **@waitroom/models:** upgraded to 1.9.2
* **@waitroom/utils:** upgraded to 1.17.2

## @waitroom/common [1.42.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.42.0...@waitroom/common@1.42.1) (2021-12-14)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.10.1
* **@waitroom/http-client:** upgraded to 1.2.1
* **@waitroom/models:** upgraded to 1.9.1
* **@waitroom/utils:** upgraded to 1.17.1

# @waitroom/common [1.42.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.41.4...@waitroom/common@1.42.0) (2021-12-14)


### Features

* playback components, folder refactor ([c8399c4](https://github.com/Waitroom/waitroom/commit/c8399c464778221f5b4b0b04b045235d0fa429b1))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.10.0
* **@waitroom/http-client:** upgraded to 1.2.0
* **@waitroom/models:** upgraded to 1.9.0
* **@waitroom/config:** upgraded to 1.3.0
* **@waitroom/utils:** upgraded to 1.17.0

## @waitroom/common [1.41.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.41.3...@waitroom/common@1.41.4) (2021-12-09)





### Dependencies

* **@waitroom/http-client:** upgraded to 1.1.2
* **@waitroom/config:** upgraded to 1.2.3

## @waitroom/common [1.41.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.41.2...@waitroom/common@1.41.3) (2021-12-08)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.9.3
* **@waitroom/utils:** upgraded to 1.16.3

## @waitroom/common [1.41.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.41.1...@waitroom/common@1.41.2) (2021-12-08)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.9.2
* **@waitroom/http-client:** upgraded to 1.1.1
* **@waitroom/models:** upgraded to 1.8.1
* **@waitroom/config:** upgraded to 1.2.2
* **@waitroom/utils:** upgraded to 1.16.2

## @waitroom/common [1.41.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.41.0...@waitroom/common@1.41.1) (2021-12-07)


### Bug Fixes

* import paths, ui colors update ([923aa17](https://github.com/Waitroom/waitroom/commit/923aa1782b1d1858d57546e971d5a296cd69beb8))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.9.1
* **@waitroom/config:** upgraded to 1.2.1
* **@waitroom/utils:** upgraded to 1.16.1

# @waitroom/common [1.41.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.40.0...@waitroom/common@1.41.0) (2021-12-07)


### Features

* refactor component folder structure, updated font weight, playback components ([de57f54](https://github.com/Waitroom/waitroom/commit/de57f547f9eceb3a66313687fd5f4ecec1cfd43a))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.9.0
* **@waitroom/http-client:** upgraded to 1.1.0
* **@waitroom/models:** upgraded to 1.8.0
* **@waitroom/config:** upgraded to 1.2.0
* **@waitroom/utils:** upgraded to 1.16.0

# @waitroom/common [1.40.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.39.1...@waitroom/common@1.40.0) (2021-12-06)


### Features

* added toast when guest leaves ([#472](https://github.com/Waitroom/rumi.ai/issues/472)) ([20da0f2](https://github.com/Waitroom/waitroom/commit/20da0f2f5f1cdb0b9f9c7307da7faccd63b7c273))

## @waitroom/common [1.39.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.39.0...@waitroom/common@1.39.1) (2021-12-06)


### Bug Fixes

* now on air sound effect doesn't play on queue pause/resume ([#480](https://github.com/Waitroom/rumi.ai/issues/480)) ([3cfbea7](https://github.com/Waitroom/waitroom/commit/3cfbea7247472217316b60b455b40c023a61a890))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.8.1
* **@waitroom/http-client:** upgraded to 1.0.1
* **@waitroom/models:** upgraded to 1.7.1

# @waitroom/common [1.39.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.38.0...@waitroom/common@1.39.0) (2021-12-02)


### Bug Fixes

* fixed auth header ([26e2b07](https://github.com/Waitroom/waitroom/commit/26e2b07278c5bf974c0ac82831ef4c7cf23d4dac))
* on end session clear host related reactions ([#478](https://github.com/Waitroom/rumi.ai/issues/478)) ([b936ef2](https://github.com/Waitroom/waitroom/commit/b936ef2d802182614d4b50d98e8f5b3ecc827d15))


### Features

* add error messages to modal for nft and image upload ([1a69365](https://github.com/Waitroom/waitroom/commit/1a693658206ecfd65dc140648cba8cab2565c6c6))
* host pause and resume single reaction ([#474](https://github.com/Waitroom/rumi.ai/issues/474)) ([07b53fc](https://github.com/Waitroom/waitroom/commit/07b53fc58c426291a0c9cef738304bc95286542c))
* nfts ([7639545](https://github.com/Waitroom/waitroom/commit/7639545b6a38c9e4fd6a503e75bc8cf5570df1a5))
* session success response codes ([#468](https://github.com/Waitroom/rumi.ai/issues/468)) ([46b52a7](https://github.com/Waitroom/waitroom/commit/46b52a7aa5c02bfaf37b35fb7b6a49590e833403))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.8.0
* **@waitroom/http-client:** upgraded to 1.0.0
* **@waitroom/models:** upgraded to 1.7.0
* **@waitroom/config:** upgraded to 1.1.0
* **@waitroom/utils:** upgraded to 1.15.0

# @waitroom/common [1.38.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.37.3...@waitroom/common@1.38.0) (2021-11-18)


### Features

* common middlewares ([#464](https://github.com/Waitroom/rumi.ai/issues/464)) ([721d64c](https://github.com/Waitroom/waitroom/commit/721d64c56d0ab04d3bd68fc64996af722ddf383d))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.7.0

## @waitroom/common [1.37.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.37.2...@waitroom/common@1.37.3) (2021-11-16)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.6.5
* **@waitroom/utils:** upgraded to 1.14.0

## @waitroom/common [1.37.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.37.1...@waitroom/common@1.37.2) (2021-11-16)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.6.4
* **@waitroom/utils:** upgraded to 1.13.0

## @waitroom/common [1.37.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.37.0...@waitroom/common@1.37.1) (2021-11-15)


### Bug Fixes

* response error message change ([#458](https://github.com/Waitroom/rumi.ai/issues/458)) ([1b726b6](https://github.com/Waitroom/waitroom/commit/1b726b6e2ffd91e8f4e7285ce361cd9a5acef18a))

# @waitroom/common [1.37.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.36.5...@waitroom/common@1.37.0) (2021-11-15)


### Features

* response codes ([#456](https://github.com/Waitroom/rumi.ai/issues/456)) ([340edc1](https://github.com/Waitroom/waitroom/commit/340edc135fbc4565a48bc236284f9b1148544ee2))

## @waitroom/common [1.36.5](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.36.4...@waitroom/common@1.36.5) (2021-11-11)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.6.3
* **@waitroom/utils:** upgraded to 1.12.0

## @waitroom/common [1.36.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.36.3...@waitroom/common@1.36.4) (2021-11-09)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.6.2
* **@waitroom/models:** upgraded to 1.6.1

## @waitroom/common [1.36.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.36.2...@waitroom/common@1.36.3) (2021-11-08)





### Dependencies

* **@waitroom/models:** upgraded to 1.6.0

## @waitroom/common [1.36.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.36.1...@waitroom/common@1.36.2) (2021-11-08)





### Dependencies

* **@waitroom/models:** upgraded to 1.5.0

## @waitroom/common [1.36.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.36.0...@waitroom/common@1.36.1) (2021-10-27)





### Dependencies

* **@waitroom/analytics:** upgraded to 1.6.1

# @waitroom/common [1.36.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.35.1...@waitroom/common@1.36.0) (2021-10-12)


### Features

* session interruption using sessionId ([#433](https://github.com/Waitroom/rumi.ai/issues/433)) ([163a043](https://github.com/Waitroom/waitroom/commit/163a043e567ecaa000699bda176ea36fa50c9db5))

## @waitroom/common [1.35.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.35.0...@waitroom/common@1.35.1) (2021-10-08)


### Bug Fixes

* apple signin ([#429](https://github.com/Waitroom/rumi.ai/issues/429)) ([7252f95](https://github.com/Waitroom/waitroom/commit/7252f953804b8853ee21d78b89b209aff3d89dee))

# @waitroom/common [1.35.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.34.0...@waitroom/common@1.35.0) (2021-10-07)


### Features

* apple signin ([#427](https://github.com/Waitroom/rumi.ai/issues/427)) ([bf675bc](https://github.com/Waitroom/waitroom/commit/bf675bc3a0cc0b4b733d3a148f366ea7f232c2dd))

# @waitroom/common [1.34.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.33.0...@waitroom/common@1.34.0) (2021-10-06)


### Features

* agora update, fixed some stream issue ([63035d7](https://github.com/Waitroom/waitroom/commit/63035d7e3d787310b3c7e5d93fd73c64358b6ca5))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.6.0
* **@waitroom/models:** upgraded to 1.4.0
* **@waitroom/utils:** upgraded to 1.11.0

# @waitroom/common [1.33.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.32.2...@waitroom/common@1.33.0) (2021-10-01)


### Features

* multiple session blocking ([#418](https://github.com/Waitroom/rumi.ai/issues/418)) ([40fa711](https://github.com/Waitroom/waitroom/commit/40fa711e5b601df490c0c3e6ce4cb3c7bc0b7df8))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.5.0
* **@waitroom/models:** upgraded to 1.3.0

## @waitroom/common [1.32.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.32.1...@waitroom/common@1.32.2) (2021-09-29)





### Dependencies

* **@waitroom/config:** upgraded to 1.0.3

## @waitroom/common [1.32.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.32.0...@waitroom/common@1.32.1) (2021-09-27)





### Dependencies

* **@waitroom/config:** upgraded to 1.0.2

# @waitroom/common [1.32.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.31.2...@waitroom/common@1.32.0) (2021-09-23)


### Features

* include multiple active session notification interupts ([#413](https://github.com/Waitroom/rumi.ai/issues/413)) ([607e8f8](https://github.com/Waitroom/waitroom/commit/607e8f864e06ae9f8e96bc6f82bd11bc47309591))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.4.1
* **@waitroom/utils:** upgraded to 1.10.0

## @waitroom/common [1.31.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.31.1...@waitroom/common@1.31.2) (2021-09-22)





### Dependencies

* **@waitroom/models:** upgraded to 1.2.1

## @waitroom/common [1.31.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.31.0...@waitroom/common@1.31.1) (2021-09-21)





### Dependencies

* **@waitroom/config:** upgraded to 1.0.1

# @waitroom/common [1.31.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.30.0...@waitroom/common@1.31.0) (2021-09-20)


### Features

* layout-engine and config packages with tests ([#410](https://github.com/Waitroom/rumi.ai/issues/410)) ([815c604](https://github.com/Waitroom/waitroom/commit/815c604a443a657e8f4db16e3a27ba71553f9c01))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.4.0
* **@waitroom/models:** upgraded to 1.2.0
* **@waitroom/config:** upgraded to 1.0.0
* **@waitroom/utils:** upgraded to 1.9.0

# @waitroom/common [1.30.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.29.0...@waitroom/common@1.30.0) (2021-09-17)


### Features

* added current user email selector ([c7a6f7d](https://github.com/Waitroom/waitroom/commit/c7a6f7de74837a75731bbe311d765a70430299f7))

# @waitroom/common [1.29.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.28.0...@waitroom/common@1.29.0) (2021-09-17)


### Features

* user settings selectors, fixed publish command ([ed8ca64](https://github.com/Waitroom/waitroom/commit/ed8ca648e0e7fc60617cf2b18d971ff768280a88))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.3.0
* **@waitroom/models:** upgraded to 1.1.0
* **@waitroom/utils:** upgraded to 1.8.0

# @waitroom/common [1.28.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.27.1...@waitroom/common@1.28.0) (2021-09-15)


### Features

* update show survey action to reflect new reaction data ([#407](https://github.com/Waitroom/rumi.ai/issues/407)) ([01c5f9a](https://github.com/Waitroom/waitroom/commit/01c5f9ac48d5ae4acdf06f8b14aac4407d08d100))

## @waitroom/common [1.27.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.27.0...@waitroom/common@1.27.1) (2021-09-15)


### Bug Fixes

* fixed publish ([7ef8fe6](https://github.com/Waitroom/waitroom/commit/7ef8fe680ceec57191f2f7faadc3cd50aeb8e49c))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.2.1
* **@waitroom/models:** upgraded to 1.0.1
* **@waitroom/utils:** upgraded to 1.7.1

# @waitroom/common [1.27.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.26.0...@waitroom/common@1.27.0) (2021-09-15)


### Bug Fixes

* fixed test coverage ([91d7692](https://github.com/Waitroom/waitroom/commit/91d7692c29a2ff8ff4ab2a17325a00a4e18cbf17))


### Features

* split common package ([#405](https://github.com/Waitroom/rumi.ai/issues/405)) ([19222cc](https://github.com/Waitroom/waitroom/commit/19222ccf9c7c5d425ee7201c272738a12c659ac3))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.2.0
* **@waitroom/models:** upgraded to 1.0.0
* **@waitroom/utils:** upgraded to 1.7.0

# @waitroom/common [1.26.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.25.0...@waitroom/common@1.26.0) (2021-09-13)


### Features

* add notification update to the user model data, needed for mobile toggle ([1f40f92](https://github.com/Waitroom/waitroom/commit/1f40f921559543c52040135963b819278f78c86b))

# @waitroom/common [1.25.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.24.0...@waitroom/common@1.25.0) (2021-09-10)


### Features

* keyboard shortcuts hook ([#399](https://github.com/Waitroom/rumi.ai/issues/399)) ([aa2f075](https://github.com/Waitroom/waitroom/commit/aa2f075008e5b99850a7258dcc8cdc286e91daf2))

# @waitroom/common [1.24.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.23.0...@waitroom/common@1.24.0) (2021-09-10)


### Features

* implement analytics package ([#396](https://github.com/Waitroom/rumi.ai/issues/396)) ([6322f20](https://github.com/Waitroom/waitroom/commit/6322f20229a9f13b8b7e1640b7974ace6680c59a))





### Dependencies

* **@waitroom/analytics:** upgraded to 1.1.0
* **@waitroom/utils:** upgraded to 1.6.0

# @waitroom/common [1.23.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.22.3...@waitroom/common@1.23.0) (2021-09-06)


### Features

* has active session selector ([#391](https://github.com/Waitroom/rumi.ai/issues/391)) ([f84f09a](https://github.com/Waitroom/waitroom/commit/f84f09a3cb6f3f2a7ad05faf110a9b053e4f54a9))

## @waitroom/common [1.22.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.22.2...@waitroom/common@1.22.3) (2021-09-03)


### Bug Fixes

* set session uneeded code set, mylistmap subscriptions, tests ([159151d](https://github.com/Waitroom/waitroom/commit/159151d54bf35565d6f3c584fcfdd3a2482fead4))

## @waitroom/common [1.22.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.22.1...@waitroom/common@1.22.2) (2021-09-03)


### Bug Fixes

* add listener on updated list and status check in my list ([5545793](https://github.com/Waitroom/waitroom/commit/55457934a515b2dbc9f2e1dbc327b2dbd330b3ae))

## @waitroom/common [1.22.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.22.0...@waitroom/common@1.22.1) (2021-09-03)


### Bug Fixes

* refactored common reducers, updated common config ([ecd1452](https://github.com/Waitroom/waitroom/commit/ecd145210b15819e972f0dd9dd9a153f52419c46))

# @waitroom/common [1.22.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.21.1...@waitroom/common@1.22.0) (2021-09-02)


### Features

* sidebar ui update ([#384](https://github.com/Waitroom/rumi.ai/issues/384)) ([5b06b72](https://github.com/Waitroom/waitroom/commit/5b06b7288bab43799cc7bd7e5bd121f11bee513c))
* use optional async function for fingerprint ([#386](https://github.com/Waitroom/rumi.ai/issues/386)) ([c907b19](https://github.com/Waitroom/waitroom/commit/c907b196cbb7ad48d3ce7239ada7791edf218e5f))





### Dependencies

* **@waitroom/utils:** upgraded to 1.5.0

## @waitroom/common [1.21.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.21.0...@waitroom/common@1.21.1) (2021-09-02)


### Bug Fixes

* moved delete account config ([6b3f506](https://github.com/Waitroom/waitroom/commit/6b3f5065a719583b64336a0b02ce0a7a9a1dc3d5))

# @waitroom/common [1.21.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.20.2...@waitroom/common@1.21.0) (2021-09-02)


### Features

* fingerprint optional ([#383](https://github.com/Waitroom/rumi.ai/issues/383)) ([e9f71a5](https://github.com/Waitroom/waitroom/commit/e9f71a59bc71e71b005fb1330031275e4ea7f32f))

## @waitroom/common [1.20.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.20.1...@waitroom/common@1.20.2) (2021-09-01)


### Bug Fixes

* fixed mobile countdown hook ([46ab17b](https://github.com/Waitroom/waitroom/commit/46ab17bc6b8e29ceccc7bc56c2e87965af29bb1d))

## @waitroom/common [1.20.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.20.0...@waitroom/common@1.20.1) (2021-09-01)


### Bug Fixes

* fixed timer on inactive tabs ([4e146ec](https://github.com/Waitroom/waitroom/commit/4e146ec8d41c89fdf25104f1fb0b212d00757971))

# @waitroom/common [1.20.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.19.0...@waitroom/common@1.20.0) (2021-09-01)


### Features

* my list init data ([a34cb63](https://github.com/Waitroom/waitroom/commit/a34cb638337c356bcf8482902659d36dcaf5b1cf))

# @waitroom/common [1.19.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.18.0...@waitroom/common@1.19.0) (2021-09-01)


### Features

* hosting support alerts ([#379](https://github.com/Waitroom/rumi.ai/issues/379)) ([3d9a4a3](https://github.com/Waitroom/waitroom/commit/3d9a4a36d7458ccf091f78e2b7b1e1ec264c389f))

# @waitroom/common [1.18.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.17.0...@waitroom/common@1.18.0) (2021-09-01)


### Features

* session analytics ([#377](https://github.com/Waitroom/rumi.ai/issues/377)) ([9653c42](https://github.com/Waitroom/waitroom/commit/9653c422aac56aab551165371f1c07364df3e06f))

# @waitroom/common [1.17.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.16.0...@waitroom/common@1.17.0) (2021-08-27)


### Features

* dont set active on set_currrent_session ([#376](https://github.com/Waitroom/rumi.ai/issues/376)) ([1177b0b](https://github.com/Waitroom/waitroom/commit/1177b0b5c10ef99f93cad48d994ca3a116c8c928))

# @waitroom/common [1.16.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.15.1...@waitroom/common@1.16.0) (2021-08-26)


### Features

* session countdown timer ([561b3f2](https://github.com/Waitroom/waitroom/commit/561b3f2b17b2150bc92d1aa45100718108ec1496))





### Dependencies

* **@waitroom/utils:** upgraded to 1.4.0

## @waitroom/common [1.15.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.15.0...@waitroom/common@1.15.1) (2021-08-25)


### Bug Fixes

* update the loading for featured sessions ([fb47496](https://github.com/Waitroom/waitroom/commit/fb47496b1bda081f4464e5535febd44802d07d09))

# @waitroom/common [1.15.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.14.0...@waitroom/common@1.15.0) (2021-08-24)


### Features

* added useNotifications to common ([3d44c04](https://github.com/Waitroom/waitroom/commit/3d44c04688113276a76eb43ef20d93ed91d1dcad))

# @waitroom/common [1.14.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.13.3...@waitroom/common@1.14.0) (2021-08-19)


### Features

* updated layout engine ([b087350](https://github.com/Waitroom/waitroom/commit/b0873500f638858c270a819f81850a49a370753d))

## @waitroom/common [1.13.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.13.2...@waitroom/common@1.13.3) (2021-08-19)


### Bug Fixes

* add checks to not join session topics on certain conditions, test updates ([bd9cb01](https://github.com/Waitroom/waitroom/commit/bd9cb01f3d35f091a9c686082ca34a9823177cf9))

## @waitroom/common [1.13.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.13.1...@waitroom/common@1.13.2) (2021-08-19)


### Bug Fixes

* fixed link in config ([287344d](https://github.com/Waitroom/waitroom/commit/287344db0c62bc1e095e87c619f8b3670ca43705))

## @waitroom/common [1.13.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.13.0...@waitroom/common@1.13.1) (2021-08-19)


### Bug Fixes

* fixed and updated config links ([60b37a7](https://github.com/Waitroom/waitroom/commit/60b37a7ff135b5c971590b2fd00b603c348914ce))

# @waitroom/common [1.13.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.12.0...@waitroom/common@1.13.0) (2021-08-16)


### Features

* refactor google auth to use new provider ([#351](https://github.com/Waitroom/rumi.ai/issues/351)) ([e6a6d6c](https://github.com/Waitroom/waitroom/commit/e6a6d6cbcf6f2bc1129fae9f679bdb5076a94596))

# @waitroom/common [1.12.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.11.1...@waitroom/common@1.12.0) (2021-08-13)


### Features

* agora local stream error notification ([0fb195c](https://github.com/Waitroom/waitroom/commit/0fb195cbc1740c9d135c6b3dfad9ccc8948fd8fe))





### Dependencies

* **@waitroom/utils:** upgraded to 1.3.0

## @waitroom/common [1.11.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.11.0...@waitroom/common@1.11.1) (2021-08-12)


### Bug Fixes

* channel removal update and add recent session in mem on load ([b4bc1a1](https://github.com/Waitroom/waitroom/commit/b4bc1a166cecbce5c8d4e95fecd373ccd3886061))

# @waitroom/common [1.11.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.10.0...@waitroom/common@1.11.0) (2021-08-10)


### Features

* listen to recent watched channels on load and status upates ([c9ccba3](https://github.com/Waitroom/waitroom/commit/c9ccba3fbe9b009b946e108b3577a8dd15a83731))

# @waitroom/common [1.10.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.9.0...@waitroom/common@1.10.0) (2021-08-05)


### Features

* update recent list off status viewer ([dd28d9c](https://github.com/Waitroom/waitroom/commit/dd28d9c8eb185729810bffd673bad8252c76fdce))

# @waitroom/common [1.9.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.8.0...@waitroom/common@1.9.0) (2021-08-04)


### Features

* adding recently watched common action and reactions ([e0ef4fa](https://github.com/Waitroom/waitroom/commit/e0ef4fa547c84cd08cabb1fe352384a11ad9941e))

# @waitroom/common [1.8.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.7.0...@waitroom/common@1.8.0) (2021-08-03)


### Features

* mobile queueStatus for now to check the now user ([#342](https://github.com/Waitroom/rumi.ai/issues/342)) ([54c987e](https://github.com/Waitroom/waitroom/commit/54c987e86eae879a26aaef43a90bb77f08c3190e))

# @waitroom/common [1.7.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.6.0...@waitroom/common@1.7.0) (2021-08-02)


### Features

* mobile queueStatus Label ([#338](https://github.com/Waitroom/rumi.ai/issues/338)) ([16af326](https://github.com/Waitroom/waitroom/commit/16af326c751e633f8181e8831702e2af0ca297ed))

# @waitroom/common [1.6.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.5.3...@waitroom/common@1.6.0) (2021-07-27)


### Features

* optional userDetails for verify auth provider ([#334](https://github.com/Waitroom/rumi.ai/issues/334)) ([74e6ffa](https://github.com/Waitroom/waitroom/commit/74e6ffa291b7646a29e03c8de1745880bbf4d9ed))

## @waitroom/common [1.5.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.5.2...@waitroom/common@1.5.3) (2021-07-19)


### Bug Fixes

* include verify listener ([#331](https://github.com/Waitroom/rumi.ai/issues/331)) ([6e6a78e](https://github.com/Waitroom/waitroom/commit/6e6a78e704b21fae5961524e65eb15abba259110))

## @waitroom/common [1.5.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.5.1...@waitroom/common@1.5.2) (2021-07-19)


### Bug Fixes

* change auth token response to be the same as the rest ([#330](https://github.com/Waitroom/rumi.ai/issues/330)) ([365e857](https://github.com/Waitroom/waitroom/commit/365e8578d3b1e6da4161c13ba3f19c1dc48d65f4))

## @waitroom/common [1.5.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.5.0...@waitroom/common@1.5.1) (2021-07-19)


### Bug Fixes

* hide dismiss button on paused ([#321](https://github.com/Waitroom/rumi.ai/issues/321)) ([08abfe3](https://github.com/Waitroom/waitroom/commit/08abfe37e3be29459dd79412f0adc126ad6a7bb7))

# @waitroom/common [1.5.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.4.4...@waitroom/common@1.5.0) (2021-07-16)


### Features

* verify auth provider ([#329](https://github.com/Waitroom/rumi.ai/issues/329)) ([1f65175](https://github.com/Waitroom/waitroom/commit/1f65175d02d9ff1d425a83ba47bb5d8f60b6a282))

## @waitroom/common [1.4.4](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.4.3...@waitroom/common@1.4.4) (2021-07-16)


### Bug Fixes

* refactored and fixed authTypes ([74a0539](https://github.com/Waitroom/waitroom/commit/74a053996b5af799bef9b3a5bf0ea62df87c5890))

## @waitroom/common [1.4.3](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.4.2...@waitroom/common@1.4.3) (2021-07-16)


### Bug Fixes

* reverted GUEST_ON_AIR action for mobile ([c1e1695](https://github.com/Waitroom/waitroom/commit/c1e16950eeb52cb385137eff80fb3e92defbd502))

## @waitroom/common [1.4.2](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.4.1...@waitroom/common@1.4.2) (2021-07-16)


### Bug Fixes

* fixed publish command ([979cd0f](https://github.com/Waitroom/waitroom/commit/979cd0f3d38bcbe61b28acdc820d954bd16c4276))





### Dependencies

* **@waitroom/utils:** upgraded to 1.2.1

## @waitroom/common [1.4.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.4.0...@waitroom/common@1.4.1) (2021-07-16)


### Bug Fixes

* fixed library publish ([fdcc1e4](https://github.com/Waitroom/waitroom/commit/fdcc1e4537f51bd78bfa276374a2a6e9064daea1))

# @waitroom/common [1.4.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.3.0...@waitroom/common@1.4.0) (2021-07-15)


### Features

* updated and refactored redux actions ([3ee2a32](https://github.com/Waitroom/waitroom/commit/3ee2a327d5ccb15c4b645f82777dbfaeba3d7cb0))





### Dependencies

* **@waitroom/utils:** upgraded to 1.2.0

# @waitroom/common [1.3.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.2.0...@waitroom/common@1.3.0) (2021-07-14)


### Features

* self service live streams ([aa87f8f](https://github.com/Waitroom/waitroom/commit/aa87f8f1f5527dbb4e78fc5fbcb34bb5e350de34))





### Dependencies

* **@waitroom/utils:** upgraded to 1.1.0

# @waitroom/common [1.2.0](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.1.1...@waitroom/common@1.2.0) (2021-07-06)


### Features

* add live streaming support ([128fa82](https://github.com/Waitroom/waitroom/commit/128fa821697bef12c51851f29b392d882a526875))

## @waitroom/common [1.1.1](https://github.com/Waitroom/waitroom/compare/@waitroom/common@1.1.0...@waitroom/common@1.1.1) (2021-07-01)


### Bug Fixes

* improved and fixed library workflows ([1bb9c76](https://github.com/Waitroom/waitroom/commit/1bb9c764748023c804db2f3b5db5acc0b3e4ec4a))





### Dependencies

* **@waitroom/utils:** upgraded to 1.0.2
