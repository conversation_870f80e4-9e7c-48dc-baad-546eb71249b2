import { Box, Center, Spinner } from '@chakra-ui/react';
import { useEffect, useState } from 'react';

export const Video = ({ src }: { src: string }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  useEffect(() => {
    setIsLoaded(!src);
  }, [src]);

  return (
    <Box pos="relative">
      {!isLoaded && (
        <Center pos="absolute" top={0} left={0} right={0} bottom={0}>
          <Spinner size="xl" />
        </Center>
      )}
      <Box
        as="video"
        width="100%"
        height="100%"
        rounded="2xl"
        playsInline
        autoPlay
        muted
        loop
        onPlay={() => setIsLoaded(true)}
        src={src}
      />
    </Box>
  );
};
