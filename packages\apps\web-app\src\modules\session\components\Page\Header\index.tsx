import {
  Box,
  DarkMode,
  Flex,
  Grid,
  GridProps,
  HStack,
  RecursiveObject,
  Text,
  Tooltip,
} from '@chakra-ui/react';
import { RecIcon } from '@modules/core/components/Icon/Rec/RecIcon';
import { RecPausedIcon } from '@modules/core/components/Icon/RecPaused/RecPausedIcon';
import PremiumBadge from '@modules/session/components/Badge/Premium/PremiumBadge';
import FreeVersionButton from '@modules/subscription/components/Button/FreeVersion/FreeVersionButton';
import {
  isFreePlan,
  makeSelectIsCurrentSessionOrganizer,
  selectCurrentSessionSettings,
  selectCurrentSessionStartedAt,
  selectCurrentSessionSubscriptionPlan,
  selectCurrentSessionTitle,
  useSessionSelectorWithCurrentUserId,
  useSessionStore,
} from '@waitroom/common';
import { useStreamStore } from '@waitroom/stream';
import { memo, ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import theme from '../../../../theme';
import { TeamMemoryBadgeContainer } from '../../Badge/TeamMemory/TeamMemory';
import SessionTimer from '../../Timer/Timer';
import { SIDEBAR_BP } from '../Sidebar/styles';
import Menu from './Menu';

const blackAlpha = theme.colors.blackAlpha as RecursiveObject<string>;

const TitleGridItem = memo((): ReactElement | null => {
  const sessionTitle = useSessionStore(selectCurrentSessionTitle);
  const subscriptionPlan = useSessionStore(selectCurrentSessionSubscriptionPlan);
  const isOrganizer = useSessionSelectorWithCurrentUserId(makeSelectIsCurrentSessionOrganizer);
  const startedAt = useSessionStore(selectCurrentSessionStartedAt);
  const isSessionFree = isFreePlan(subscriptionPlan);
  return (
    <Flex direction="row" justify="center" align="center" textAlign="center" gap={2}>
      <Text
        as="strong"
        lineHeight="shorter"
        fontSize={{ base: 'xs', sm: 'sm', md: 'md' }}
        noOfLines={1}
        title={sessionTitle}
      >
        {sessionTitle}
      </Text>
      <TeamMemoryBadgeContainer />
      {isSessionFree ? (
        <FreeVersionButton responsive isOrganizer={isOrganizer} />
      ) : (
        <PremiumBadge responsive />
      )}
      {!!startedAt && (
        <Text
          as="span"
          fontSize={'xs'}
          color={'gray.200'}
          fontWeight={'bold'}
          lineHeight={'shorter'}
          flexShrink={0}
          wordBreak={'break-word'}
          w={'58px'}
          textAlign={'left'}
        >
          <SessionTimer startedAt={startedAt} />
        </Text>
      )}
    </Flex>
  );
});
const RecGridItem = memo((): ReactElement | null => {
  const { t } = useTranslation();
  const { enableRecording } = useSessionStore(selectCurrentSessionSettings) || {};
  const isOffTheRecord = useStreamStore.use.offTheRecordStatus() === 'ENABLED';
  return (
    <HStack w="full" justifyContent="end">
      {enableRecording && (
        <Box
          bg="t.gray-900-50"
          rounded={4}
          pl={1}
          pr={2}
          py="4px"
          fontSize="2xs"
          fontWeight="bold"
          title={enableRecording ? t('session.thisSessionIsBeingRecorded') : ''}
        >
          {enableRecording ? (
            <Tooltip isDisabled={!isOffTheRecord} label={t('offTheRecord.recordingIsPaused')}>
              <Flex alignItems="center">
                {isOffTheRecord ? (
                  <RecPausedIcon w="16px" h="16px" mr={1} />
                ) : (
                  <RecIcon w="16px" h="16px" mr={1} />
                )}
                <Text size="2xs" fontWeight={800} color={isOffTheRecord ? 'gray.500' : 'white'}>
                  REC
                </Text>
              </Flex>
            </Tooltip>
          ) : null}
        </Box>
      )}
    </HStack>
  );
});

export type HeaderProps = GridProps & {
  isEnded?: boolean;
};
const Header = memo(({ isEnded, ...rest }: HeaderProps): ReactElement | null => (
  <>
    <Grid
      className="top-overlay"
      position="relative"
      alignItems="center"
      gridTemplateColumns={{ base: '65px 1fr 65px', md: '125px 1fr 125px' }}
      gap={[1, 1, 2]}
      h={12}
      pl={4}
      pr={{ base: 4, [SIDEBAR_BP]: 0 }}
      pointerEvents="auto"
      bg={`linear-gradient(0, rgba(0, 0, 0, 0) 0%, ${blackAlpha[300]} 100%)`}
      zIndex={10}
      minH={12}
      {...rest}
    >
      <DarkMode>
        <Flex gap={6} w="full" justifyContent="flex-start" align="center">
          <Menu />
        </Flex>
      </DarkMode>
      <Box flex={1} flexGrow={1} mx="auto">
        {!isEnded && <TitleGridItem />}
      </Box>
      <div>{!isEnded && <RecGridItem />}</div>
    </Grid>
  </>
));

export default Header;
