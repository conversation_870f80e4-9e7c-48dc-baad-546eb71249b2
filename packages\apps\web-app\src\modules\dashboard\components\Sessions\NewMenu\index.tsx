import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Spinner } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { faCalendarPlus, faPlus } from '@fortawesome/pro-solid-svg-icons';
import { useInstantSession } from '@modules/session/hooks/useInstantSession';
import { selectCurrentUser, useAuthStore } from '@waitroom/auth';
import { setModal } from '@waitroom/common';
import { memo, ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { meetNowModalProps } from '../helpers';

const NewMenu = memo((): ReactElement | null => {
  const { t } = useTranslation();
  const { firstName = '' } = useAuthStore(selectCurrentUser) || {};
  const { start, isPending } = useInstantSession();

  return (
    <Menu placement="bottom">
      <MenuButton
        as={IconB<PERSON>on}
        variant={'outline'}
        borderWidth={'2px'}
        colorScheme={'red.700'}
        color={'white'}
        size={'3xs'}
      >
        <Icon icon={faPlus} size="lg" />
      </MenuButton>
      <MenuList>
        <MenuItem
          isDisabled={isPending}
          onClick={() => {
            start();
          }}
        >
          {isPending && <Spinner mr={4} />}
          <Icon icon={faPlus} mr={2} />
          {t('dashboard.meetNow')}
        </MenuItem>
        <MenuItem
          onClick={() => {
            setModal(meetNowModalProps(firstName, t));
          }}
        >
          <Icon icon={faCalendarPlus} mr={2} />
          {t('dashboard.newSessionBtn')}
        </MenuItem>
      </MenuList>
    </Menu>
  );
});
export default NewMenu;
