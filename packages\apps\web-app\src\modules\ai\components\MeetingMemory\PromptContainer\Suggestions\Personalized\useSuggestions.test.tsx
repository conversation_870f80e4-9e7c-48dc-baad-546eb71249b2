import React from 'react';
import { act, renderHook } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { meetingsService } from '@waitroom/common-api';
import { getApiResponseCode, getPersonalizedSuggestionsQueryKey } from '@waitroom/react-query';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { useSuggestions, waitAfterEndTimestamp } from './useSuggestions';

// Mock dependencies
vi.mock('@waitroom/common-api', async () => {
  const actual = await import('@waitroom/common-api');
  return {
    ...actual,
    meetingsService: {
      getPersonalizedSuggestions: vi.fn(),
    },
    DefaultApiResponse: {},
  };
});

vi.mock('@waitroom/react-query', async () => {
  const actual = await import('@waitroom/react-query');
  return {
    ...actual,
    getApiResponseCode: vi.fn(),
    getPersonalizedSuggestionsQueryKey: vi.fn(),
    inactiveOptions: {
      '1h': { staleTime: 3600000 },
      '7d': { staleTime: 604800000 },
    },
  };
});

const mockMeetingsService = vi.mocked(meetingsService);
const mockGetApiResponseCode = vi.mocked(getApiResponseCode);
const mockGetPersonalizedSuggestionsQueryKey = vi.mocked(getPersonalizedSuggestionsQueryKey);

// Test wrapper component
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        experimental_prefetchInRender: true,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useSuggestions', () => {
  const mockSessionId = 'session-123';
  const mockRecurrenceId = 'recurrence-456';
  const mockQueryKey = ['personalizedSuggestions', mockSessionId, mockRecurrenceId];

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();

    // Setup default mocks
    mockGetPersonalizedSuggestionsQueryKey.mockReturnValue(mockQueryKey);
    mockGetApiResponseCode.mockReturnValue(200);
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should not set up timeout when endTimestamp is not provided', () => {
    const { result } = renderHook(
      () => useSuggestions({ sessionId: mockSessionId, recurrenceId: mockRecurrenceId }),
      { wrapper: createWrapper() }
    );

    // Should return initial state
    expect(result.current.suggestions).toBeUndefined();
    expect(result.current.isLoading).toBe(false);

    // Fast forward time significantly - no timeout should be set
    vi.advanceTimersByTime(60000);

    // Query should remain disabled
    expect(result.current.isLoading).toBe(false);
  });

  it('should set up timeout when endTimestamp is provided', async () => {
    const currentTime = Date.now();
    const endTimestamp = currentTime;

    vi.spyOn(Date, 'now').mockReturnValue(currentTime);

    const { result } = renderHook(
      () =>
        useSuggestions({
          sessionId: mockSessionId,
          recurrenceId: mockRecurrenceId,
          endTimestamp: endTimestamp / 1000,
        }),
      { wrapper: createWrapper() },
    );

    // Initially should not be loading
    expect(result.current.isLoading).toBe(false);

    // Fast forward to just before the 10-second mark
    await act(() => vi.advanceTimersByTime(waitAfterEndTimestamp - 1));
    expect(result.current.isLoading).toBe(false);

    // Fast forward to trigger the timeout
    await act(() => vi.advanceTimersByTime(1));
    expect(result.current.isLoading).toBe(true);
  });

  it('should handle endTimestamp in the past correctly', async () => {
    const currentTime = Date.now();
    const pastTimestamp = currentTime - 30000; // 30 seconds ago

    vi.spyOn(Date, 'now').mockReturnValue(currentTime);

    const { result } = renderHook(
      () =>
        useSuggestions({
          sessionId: mockSessionId,
          recurrenceId: mockRecurrenceId,
          endTimestamp: pastTimestamp / 1000,
        }),
      { wrapper: createWrapper() },
    );

    await act(() => vi.advanceTimersByTime(1));
    // Should return initial state
    expect(result.current.suggestions).toBeUndefined();
    expect(result.current.isLoading).toBe(true);
  });

  it('should handle endTimestamp in the future correctly', async () => {
    const currentTime = Date.now();
    const futureTimestamp = currentTime + 5000; // 5 seconds in the future

    vi.spyOn(Date, 'now').mockReturnValue(currentTime);

    const { result } = renderHook(
      () =>
        useSuggestions({
          sessionId: mockSessionId,
          recurrenceId: mockRecurrenceId,
          endTimestamp: futureTimestamp / 1000,
        }),
      { wrapper: createWrapper() },
    );

    // Initially should not be loading
    expect(result.current.isLoading).toBe(false);

    // Fast forward to just before the timeout
    await act(() => vi.advanceTimersByTime(waitAfterEndTimestamp + 4999));
    expect(result.current.isLoading).toBe(false);

    // Fast forward to trigger the timeout
    await act(() => vi.advanceTimersByTime(1));

    expect(result.current.isLoading).toBe(true);
  });

  it('should cleanup timeout when component unmounts', async () => {
    const currentTime = Date.now();
    const endTimestamp = currentTime + 5000; // 5 seconds in the future

    vi.spyOn(Date, 'now').mockReturnValue(currentTime);

    const { unmount } = renderHook(
      () =>
        useSuggestions({
          sessionId: mockSessionId,
          recurrenceId: mockRecurrenceId,
          endTimestamp: endTimestamp / 1000,
        }),
      { wrapper: createWrapper() },
    );

    // Unmount before timeout triggers
    unmount();

    // Fast forward time to when the timeout would have triggered
    await act(() => vi.advanceTimersByTime(waitAfterEndTimestamp + 1));

    // Should not call the API since component was unmounted
    expect(mockMeetingsService.getPersonalizedSuggestions).not.toHaveBeenCalled();
  });
}); 