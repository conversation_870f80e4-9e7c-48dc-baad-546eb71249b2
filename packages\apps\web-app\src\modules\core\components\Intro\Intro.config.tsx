import { CDN_VIDEOS_URL } from '@core/config';
import { Notetaker } from './components/Notetaker/Notetaker';
import { Stage } from './Intro.types';

export const stages: Stage[] = [
  {
    key: 0,
    right: {
      video: `${CDN_VIDEOS_URL}/meeting-memory.mp4`,
      description: '',
    },
  },
];

export const stagesWithBots = [
  {
    key: 2,
    left: {
      children: ({ onNext }: { onNext?: () => void }) => (
        <Notetaker showButton={true} onNext={onNext} />
      ),
    },
    right: {
      video: `${CDN_VIDEOS_URL}/notetaker.mp4`,
      description: '',
    },
  },
  ...stages,
];
